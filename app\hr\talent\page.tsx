"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Users, 
  Search, 
  Filter, 
  UserPlus, 
  Briefcase, 
  Building, 
  Globe, 
  PieChart, 
  TrendingUp, 
  CheckCircle, 
  Clock, 
  Download,
  FileText
} from "lucide-react";

// Mock data for recruitment funnel
const recruitmentFunnel = {
  openRoles: 5,
  avgHireTime: "23d",
  sources: {
    LinkedIn: { candidates: 142, hires: 38 },
    Referrals: { candidates: 89, hires: 37 },
    JobBoards: { candidates: 156, hires: 24 },
    Company: { candidates: 52, hires: 15 }
  },
  diversityMetrics: {
    gender: { male: 54, female: 43, other: 3 },
    nationality: ["IN", "US", "DE", "CA", "UK", "JP"]
  }
};

// Mock data for talent list
const candidates = [
  { 
    id: 1, 
    name: "Arjun Patel", 
    role: "Senior Immigration Specialist", 
    department: "Legal", 
    status: "Interview", 
    source: "LinkedIn",
    rating: 4.8,
    experience: "7 years" 
  },
  { 
    id: 2, 
    name: "Sarah Chen", 
    role: "Client Relations Lead", 
    department: "Customer Success", 
    status: "Screening", 
    source: "Referral",
    rating: 4.5,
    experience: "5 years" 
  },
  { 
    id: 3, 
    name: "Miguel Rodriguez", 
    role: "Visa Process Coordinator", 
    department: "Operations", 
    status: "Offer", 
    source: "JobBoard",
    rating: 4.7,
    experience: "4 years" 
  },
  { 
    id: 4, 
    name: "Fatima Al-Hassan", 
    role: "Travel Document Specialist", 
    department: "Documentation", 
    status: "Assessment", 
    source: "LinkedIn",
    rating: 4.2,
    experience: "3 years" 
  },
  { 
    id: 5, 
    name: "Raj Mehta", 
    role: "Immigration Consultant", 
    department: "Advisory", 
    status: "Interview", 
    source: "Company Website",
    rating: 4.6,
    experience: "6 years" 
  }
];

// Current employees list
const employees = [
  { 
    id: 101, 
    name: "John Doe", 
    position: "Senior Visa Specialist", 
    department: "Processing", 
    tenure: "3y 4m",
    performance: "High Performer"
  },
  { 
    id: 102, 
    name: "Priya Sharma", 
    position: "Team Lead, Client Services", 
    department: "Customer Relations", 
    tenure: "2y 8m",
    performance: "High Potential"
  },
  { 
    id: 103, 
    name: "Michael Wong", 
    position: "Documentation Officer", 
    department: "Processing", 
    tenure: "1y 2m",
    performance: "Core Performer"
  },
  { 
    id: 104, 
    name: "Sonia Garcia", 
    position: "Immigration Counselor", 
    department: "Advisory", 
    tenure: "4y 1m",
    performance: "High Performer"
  },
];

export default function TalentHub() {
  const [activeTab, setActiveTab] = useState("recruitment");
  const [searchQuery, setSearchQuery] = useState("");

  const getStatusBadge = (status: string) => {
    switch(status) {
      case "Interview":
        return <Badge className="bg-blue-500">Interview</Badge>;
      case "Screening":
        return <Badge variant="outline">Screening</Badge>;
      case "Assessment":
        return <Badge className="bg-amber-500">Assessment</Badge>;
      case "Offer":
        return <Badge className="bg-green-500">Offer</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPerformanceBadge = (performance: string) => {
    switch(performance) {
      case "High Performer":
        return <Badge className="bg-green-500">High Performer</Badge>;
      case "High Potential":
        return <Badge className="bg-purple-500">High Potential</Badge>;
      case "Core Performer":
        return <Badge variant="outline">Core Performer</Badge>;
      default:
        return <Badge variant="outline">{performance}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Talent Hub</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button>
            <UserPlus className="mr-2 h-4 w-4" />
            Add New
          </Button>
        </div>
      </div>

      <Tabs defaultValue="recruitment" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="recruitment">Recruitment</TabsTrigger>
          <TabsTrigger value="employees">Employees</TabsTrigger>
          <TabsTrigger value="diversity">Diversity & Inclusion</TabsTrigger>
        </TabsList>
        
        <TabsContent value="recruitment" className="space-y-4">
          {/* Recruitment Funnel */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Open Roles</CardTitle>
                <Briefcase className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{recruitmentFunnel.openRoles}</div>
                <p className="text-xs text-muted-foreground">
                  Across 3 departments
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Hire Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{recruitmentFunnel.avgHireTime}</div>
                <p className="text-xs text-muted-foreground">
                  -2d from last quarter
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Top Source</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">LinkedIn</div>
                <p className="text-xs text-muted-foreground">
                  38 hires this year
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Referral Rate</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">32%</div>
                <p className="text-xs text-muted-foreground">
                  +5% from last year
                </p>
              </CardContent>
            </Card>
          </div>
          
          {/* Candidate Search & Filter */}
          <Card className="p-6">
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
              <div className="flex-1 space-y-2">
                <label className="text-sm font-medium">Search Candidates</label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input 
                    type="search" 
                    placeholder="Search by name, role or skills..." 
                    className="pl-8" 
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="flex-1 space-y-2 md:max-w-[200px]">
                <label className="text-sm font-medium">Department</label>
                <Select defaultValue="all">
                  <SelectTrigger>
                    <SelectValue placeholder="All Departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    <SelectItem value="legal">Legal</SelectItem>
                    <SelectItem value="operations">Operations</SelectItem>
                    <SelectItem value="customer">Customer Success</SelectItem>
                    <SelectItem value="advisory">Advisory</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1 space-y-2 md:max-w-[200px]">
                <label className="text-sm font-medium">Status</label>
                <Select defaultValue="all">
                  <SelectTrigger>
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="screening">Screening</SelectItem>
                    <SelectItem value="assessment">Assessment</SelectItem>
                    <SelectItem value="interview">Interview</SelectItem>
                    <SelectItem value="offer">Offer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-end pt-6 md:pt-0">
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                </Button>
              </div>
            </div>
          </Card>
          
          {/* Candidates Table */}
          <Card>
            <CardHeader>
              <CardTitle>Active Candidates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="border-b">
                    <tr className="text-muted-foreground">
                      <th className="h-10 px-2 text-left font-medium">Name</th>
                      <th className="h-10 px-2 text-left font-medium">Position</th>
                      <th className="h-10 px-2 text-left font-medium">Department</th>
                      <th className="h-10 px-2 text-left font-medium">Status</th>
                      <th className="h-10 px-2 text-left font-medium">Source</th>
                      <th className="h-10 px-2 text-left font-medium">Experience</th>
                      <th className="h-10 px-2 text-left font-medium">Rating</th>
                      <th className="h-10 px-2 text-left font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {candidates.map((candidate) => (
                      <tr key={candidate.id} className="border-b transition-colors hover:bg-muted/50">
                        <td className="p-2 font-medium">{candidate.name}</td>
                        <td className="p-2">{candidate.role}</td>
                        <td className="p-2">{candidate.department}</td>
                        <td className="p-2">{getStatusBadge(candidate.status)}</td>
                        <td className="p-2">{candidate.source}</td>
                        <td className="p-2">{candidate.experience}</td>
                        <td className="p-2">{candidate.rating}/5</td>
                        <td className="p-2">
                          <Button variant="ghost" size="sm">View</Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="employees" className="space-y-4">
          {/* Employee Statistics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">247</div>
                <p className="text-xs text-muted-foreground">
                  Across 8 departments
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">91.5%</div>
                <p className="text-xs text-muted-foreground">
                  +2.5% from last year
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Tenure</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.7 yrs</div>
                <p className="text-xs text-muted-foreground">
                  Industry avg: 2.1 yrs
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Reviews Due</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">15</div>
                <p className="text-xs text-muted-foreground">
                  Next due: May 12
                </p>
              </CardContent>
            </Card>
          </div>
          
          {/* Employee Search Bar */}
          <Card className="p-6">
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
              <div className="flex-1 space-y-2">
                <label className="text-sm font-medium">Search Employees</label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input type="search" placeholder="Search by name, position or department..." className="pl-8" />
                </div>
              </div>
              
              <div className="flex-1 space-y-2 md:max-w-[200px]">
                <label className="text-sm font-medium">Department</label>
                <Select defaultValue="all">
                  <SelectTrigger>
                    <SelectValue placeholder="All Departments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="customer">Customer Relations</SelectItem>
                    <SelectItem value="advisory">Advisory</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1 space-y-2 md:max-w-[200px]">
                <label className="text-sm font-medium">Performance</label>
                <Select defaultValue="all">
                  <SelectTrigger>
                    <SelectValue placeholder="All Ratings" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Ratings</SelectItem>
                    <SelectItem value="high">High Performer</SelectItem>
                    <SelectItem value="potential">High Potential</SelectItem>
                    <SelectItem value="core">Core Performer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-end pt-6 md:pt-0">
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Filters
                </Button>
              </div>
            </div>
          </Card>
          
          {/* Employee Table */}
          <Card>
            <CardHeader>
              <CardTitle>Employee Directory</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="border-b">
                    <tr className="text-muted-foreground">
                      <th className="h-10 px-2 text-left font-medium">Name</th>
                      <th className="h-10 px-2 text-left font-medium">Position</th>
                      <th className="h-10 px-2 text-left font-medium">Department</th>
                      <th className="h-10 px-2 text-left font-medium">Tenure</th>
                      <th className="h-10 px-2 text-left font-medium">Performance</th>
                      <th className="h-10 px-2 text-left font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {employees.map((employee) => (
                      <tr key={employee.id} className="border-b transition-colors hover:bg-muted/50">
                        <td className="p-2 font-medium">{employee.name}</td>
                        <td className="p-2">{employee.position}</td>
                        <td className="p-2">{employee.department}</td>
                        <td className="p-2">{employee.tenure}</td>
                        <td className="p-2">{getPerformanceBadge(employee.performance)}</td>
                        <td className="p-2">
                          <Button variant="ghost" size="sm">View</Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="diversity" className="space-y-4">
          {/* Diversity Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Gender Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-48">
                  <div className="space-y-4 text-center">
                    <PieChart className="h-16 w-16 mx-auto text-primary" />
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold">{recruitmentFunnel.diversityMetrics.gender.male}%</div>
                        <p className="text-xs text-muted-foreground">Male</p>
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{recruitmentFunnel.diversityMetrics.gender.female}%</div>
                        <p className="text-xs text-muted-foreground">Female</p>
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{recruitmentFunnel.diversityMetrics.gender.other}%</div>
                        <p className="text-xs text-muted-foreground">Other</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>National Representation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-48 flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <Globe className="h-16 w-16 mx-auto text-primary" />
                    <div className="flex flex-wrap justify-center gap-2">
                      {recruitmentFunnel.diversityMetrics.nationality.map((country) => (
                        <Badge key={country} variant="outline">{country}</Badge>
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Team members from 17 countries
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Inclusion Initiatives</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="mt-0.5 rounded-full bg-green-100 p-1">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Unbiased Hiring Training</p>
                      <p className="text-xs text-muted-foreground">Completed by 94% of managers</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="mt-0.5 rounded-full bg-green-100 p-1">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Cultural Sensitivity Workshop</p>
                      <p className="text-xs text-muted-foreground">Scheduled monthly for all staff</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="mt-0.5 rounded-full bg-amber-100 p-1">
                      <Clock className="h-4 w-4 text-amber-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Inclusive Leadership Program</p>
                      <p className="text-xs text-muted-foreground">In progress • 62% completed</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Diversity & Inclusion Strategy</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Our talent strategy emphasizes creating an inclusive environment for visa industry professionals from all backgrounds. 
                  The diversity initiatives focus on establishing a culture where different perspectives are valued and contribute to our service quality.
                </p>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Goals for 2025</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                        Increase gender parity in leadership (target: 48/52%)
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                        Expand cultural representation across our global offices
                      </li>
                      <li className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-amber-600" />
                        Implement blind resume review process for all positions
                      </li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Inclusion Programs</h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                        Mentorship matching across departments and backgrounds
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                        Cultural awareness and visa regulation training
                      </li>
                      <li className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-amber-600" />
                        Employee resource groups for underrepresented demographics
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 