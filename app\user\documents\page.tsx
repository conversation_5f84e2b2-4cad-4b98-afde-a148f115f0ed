"use client";

import { useRef, useState, useEffect } from "react";
import Link from "next/link";

// SVG Icons as React components
const DocumentIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
  </svg>
);

const CheckCircleIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
  </svg>
);

const ExclamationCircleIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
  </svg>
);

const ClockIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

const ArrowUpTrayIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
  </svg>
);

function GlassCard({ children, className = "" }: { children: React.ReactNode; className?: string }) {
  return (
    <div
      className={`rounded-2xl p-6 m-2 shadow-lg ${className}`}
      style={{
        background: "linear-gradient(135deg, #f5f7fa55 0%, #c3cfe255 100%)",
        backdropFilter: "blur(12px)",
        boxShadow: "8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff",
        border: "1px solid rgba(255,255,255,0.15)",
      }}
    >
      {children}
    </div>
  );
}

interface Document {
  id: string;
  filename: string;
  size: number;
  mimetype: string;
  status: string;
  uploadedAt: string;
}

const documentTypes = [
  { id: "passport", name: "Passport" },
  { id: "photo", name: "Photograph" },
  { id: "financials", name: "Financial Documents" },
  { id: "resume", name: "Resume/CV" },
  { id: "invitation", name: "Invitation Letter" },
  { id: "travel", name: "Travel Itinerary" },
  { id: "accommodation", name: "Accommodation Proof" },
  { id: "employment", name: "Employment Verification" },
];

export default function DocumentsPage() {
  const [dragActive, setDragActive] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [uploadedDocs, setUploadedDocs] = useState<Document[]>([]);
  const [uploading, setUploading] = useState(false);
  const [selectedType, setSelectedType] = useState(documentTypes[0].id);
  const [uploadProgress, setUploadProgress] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchDocs();
  }, []);

  async function fetchDocs() {
    try {
      const res = await fetch("/api/user-documents?userId=mock-user-id");
      if (res.ok) {
        const data = await res.json();
        setUploadedDocs(data);
      }
    } catch (error) {
      console.error("Failed to fetch documents:", error);
    }
  }

  async function uploadFile(file: File) {
    setUploading(true);
    setUploadProgress(0);
    
    const formData = new FormData();
    formData.append("file", file);
    formData.append("documentType", selectedType);
    
    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 20;
          return newProgress >= 100 ? 100 : newProgress;
        });
      }, 200);
      
      const res = await fetch("/api/upload", { 
        method: "POST", 
        body: formData 
      });
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      if (res.ok) {
        await fetchDocs();
        setTimeout(() => {
          setUploading(false);
          setUploadProgress(0);
        }, 500);
      } else {
        setUploading(false);
        alert("Upload failed. Please try again.");
      }
    } catch (error) {
      setUploading(false);
      console.error("Error uploading file:", error);
      alert("Upload failed. Please try again.");
    }
  }

  function handleDrag(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") setDragActive(true);
    else if (e.type === "dragleave") setDragActive(false);
  }

  function handleDrop(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      setFiles(Array.from(e.dataTransfer.files));
      uploadFile(e.dataTransfer.files[0]);
    }
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files.length > 0) {
      setFiles(Array.from(e.target.files));
      uploadFile(e.target.files[0]);
    }
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case "APPROVED":
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case "REJECTED":
        return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
    }
  }

  function formatFileSize(bytes: number) {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / 1048576).toFixed(1) + " MB";
  }

  function getFileTypeIcon(mimetype: string) {
    return <DocumentIcon className="h-5 w-5 text-gray-400" />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e6eeff] to-[#f5f7ff] py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Document Management</h1>
          <Link href="/user" className="text-blue-600 hover:text-blue-800">
            Back to Dashboard
          </Link>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <GlassCard className="lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Required Documents</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {documentTypes.map((type) => {
                const uploaded = uploadedDocs.some(
                  doc => doc.filename.toLowerCase().includes(type.id)
                );
                
                return (
                  <div 
                    key={type.id}
                    className={`p-4 rounded-lg border flex items-center justify-between ${
                      uploaded ? 'bg-green-50 border-green-200' : 'bg-white/40 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center">
                      <DocumentIcon className="h-6 w-6 text-gray-500 mr-3" />
                      <span>{type.name}</span>
                    </div>
                    {uploaded ? (
                      <span className="text-green-600 text-sm font-medium flex items-center">
                        <CheckCircleIcon className="h-5 w-5 mr-1" />
                        Uploaded
                      </span>
                    ) : (
                      <span className="text-yellow-600 text-sm font-medium">Required</span>
                    )}
                  </div>
                );
              })}
            </div>
          </GlassCard>
          
          <GlassCard>
            <h2 className="text-xl font-semibold mb-4">Upload Document</h2>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Document Type
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md bg-white/70"
                disabled={uploading}
              >
                {documentTypes.map((type) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div
              className={`h-44 flex flex-col items-center justify-center border-2 border-dashed rounded-xl transition-colors duration-200 cursor-pointer ${
                dragActive 
                  ? "border-[#1E90FF] bg-blue-50/40" 
                  : uploading 
                    ? "border-gray-300 bg-gray-50/30 cursor-not-allowed" 
                    : "border-gray-300 bg-white/30 hover:bg-blue-50/20 hover:border-blue-300"
              }`}
              onDragEnter={!uploading ? handleDrag : undefined}
              onDragOver={!uploading ? handleDrag : undefined}
              onDragLeave={!uploading ? handleDrag : undefined}
              onDrop={!uploading ? handleDrop : undefined}
              onClick={() => !uploading && inputRef.current?.click()}
            >
              <input
                ref={inputRef}
                type="file"
                className="hidden"
                onChange={handleChange}
                disabled={uploading}
              />
              
              {uploading ? (
                <div className="w-full px-8">
                  <div className="flex items-center justify-center mb-2">
                    <ArrowUpTrayIcon className="h-6 w-6 text-blue-600 animate-pulse" />
                  </div>
                  <div className="text-center text-gray-600 mb-3">Uploading {files[0]?.name}...</div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-blue-600 h-2.5 rounded-full transition-all duration-200" 
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              ) : (
                <>
                  <ArrowUpTrayIcon className="h-8 w-8 text-gray-400 mb-2" />
                  <span className="text-gray-600 text-center">
                    Drag and drop your file here<br />or click to browse
                  </span>
                  <span className="text-xs text-gray-400 mt-2">
                    Supported formats: PDF, JPG, PNG (Max: 10MB)
                  </span>
                </>
              )}
            </div>
          </GlassCard>
          
          <GlassCard className="lg:col-span-3">
            <h2 className="text-xl font-semibold mb-4">Uploaded Documents</h2>
            {uploadedDocs.length === 0 ? (
              <div className="text-center py-10 text-gray-500">
                No documents uploaded yet. Upload your first document above.
              </div>
            ) : (
              <div className="overflow-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-600">Document</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600">Type</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600">Size</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600">Uploaded</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600">Status</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {uploadedDocs.map((doc) => (
                      <tr key={doc.id} className="border-b border-gray-100 hover:bg-white/30">
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            {getFileTypeIcon(doc.mimetype)}
                            <span className="ml-2 truncate max-w-[200px]">{doc.filename}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-sm text-gray-600">
                          {documentTypes.find(t => doc.filename.toLowerCase().includes(t.id))?.name || "Other"}
                        </td>
                        <td className="py-3 px-4 text-sm text-gray-600">{formatFileSize(doc.size)}</td>
                        <td className="py-3 px-4 text-sm text-gray-600">
                          {new Date(doc.uploadedAt).toLocaleDateString()}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            {getStatusIcon(doc.status)}
                            <span className="ml-1 text-sm">
                              {doc.status === "SUBMITTED" ? "Pending Review" : doc.status.charAt(0) + doc.status.slice(1).toLowerCase()}
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <button className="text-blue-600 hover:text-blue-800 text-sm mr-2">
                            View
                          </button>
                          <button className="text-red-600 hover:text-red-800 text-sm">
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </GlassCard>
        </div>
      </div>
    </div>
  );
} 