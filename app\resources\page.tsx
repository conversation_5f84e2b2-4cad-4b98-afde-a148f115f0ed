import Link from "next/link";
import { FileText, Video, HelpCircle, ArrowRight } from "lucide-react";

interface ResourceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
}

function ResourceCard({ title, description, icon, href }: ResourceCardProps) {
  return (
    <div className="rounded-xl p-6 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl hover:translate-y-[-4px]"
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}>
      <div className="mb-4 text-indigo-600">
        {icon}
      </div>
      <h3 className="text-lg font-semibold text-indigo-800 mb-2">{title}</h3>
      <p className="text-sm text-gray-600 mb-4">
        {description}
      </p>
      <Link href={href as any} className="text-indigo-600 font-medium flex items-center">
        Explore <ArrowRight size={16} className="ml-1" />
      </Link>
    </div>
  );
}

export default function ResourcesPage() {
  return (
    <div>
      <h2 className="text-xl font-semibold text-indigo-800 mb-6">Knowledge Resources</h2>
      
      <p className="text-gray-600 mb-8">
        Access our comprehensive collection of resources designed to help you navigate the 
        complex world of visas, immigration, and global mobility. From detailed guides and 
        instructional webinars to frequently asked questions, we've got you covered.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <ResourceCard 
          title="Application Guides"
          description="Step-by-step instructions for different visa types and immigration pathways."
          icon={<FileText size={36} />}
          href="/resources/guides"
        />
        
        <ResourceCard 
          title="Expert Webinars"
          description="Watch recorded sessions with immigration experts covering various topics."
          icon={<Video size={36} />}
          href="/resources/webinars"
        />
        
        <ResourceCard 
          title="Frequently Asked Questions"
          description="Find answers to common questions about visas and immigration processes."
          icon={<HelpCircle size={36} />}
          href="/resources/faqs"
        />
      </div>
      
      <div className="mt-12">
        <h3 className="text-lg font-semibold text-indigo-800 mb-4">Need Personalized Help?</h3>
        <p className="text-gray-600 mb-4">
          Our resources provide general guidance, but every immigration journey is unique. 
          Connect with our visa experts for personalized assistance tailored to your specific needs.
        </p>
        <Link href="/contact" className="inline-block bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md">
          Schedule a Consultation
        </Link>
      </div>
    </div>
  );
} 