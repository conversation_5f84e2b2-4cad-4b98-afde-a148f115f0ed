// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Add enums for better type safety
enum UserRole {
  USER
  ADMIN
  SALES_EXECUTIVE
  SALES_MANAGER
  CRM_MANAGER
  HR_MANAGER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
}

enum Priority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum CommunicationPref {
  EMAIL
  SMS
  PHONE
  IN_APP
}

enum ApplicationStatus {
  DRAFT
  SUBMITTED
  IN_REVIEW
  APPROVED
  REJECTED
  PENDING_DOCUMENTS
  INTERVIEW_SCHEDULED
  COMPLETED
}

enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  COMPLETED
  CANCELLED
  RESCHEDULED
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum MessageType {
  SYSTEM
  USER_TO_COUNSELOR
  COUNSELOR_TO_USER
  NOTIFICATION
}

enum VisaStage {
  PROFILE_EVALUATION
  DOCS_COLLECTION
  APPLICATION_SUBMISSION
  BIOMETRICS
  INTERVIEW_SCHEDULED
  DECISION_PENDING
  VISA_APPROVED
  VISA_GRANTED
}

model User {
  id                   String         @id @default(uuid())
  clerkId              String?        @unique // Clerk user ID for authentication
  name                 String?
  email                String         @unique
  image                String?
  role                 UserRole       @default(USER)
  status               UserStatus     @default(ACTIVE)
  country              String?
  phone                String?
  location             String?
  visaType             String?
  visaGoals            String?        @db.Text
  priority             Priority       @default(NORMAL)
  profileCompleted     Boolean        @default(false)
  completedSteps       Json           @default("{}")
  profileProgress      Int            @default(20)      // Percentage complete
  avatar               String?
  profilePicture       String?
  membershipTier       String         @default("Standard")
  memberSince          String?
  communicationPreference CommunicationPref @default(EMAIL)
  receiveUpdates       Boolean?       @default(true)
  receivePromotions    Boolean?       @default(false)
  documents            Json?          // Stores document upload status
  lastLogin            DateTime?
  createdAt            DateTime       @default(now())
  updatedAt            DateTime       @updatedAt

  // Relations
  leads                EnhancedLead[] @relation("LeadAssignee")
  applications         Application[]
  appointments         Appointment[]
  tasks                Task[]
  sentMessages         Message[]      @relation("MessageSender")
  receivedMessages     Message[]      @relation("MessageReceiver")
  bookings             Booking[]
  travelRecommendations TravelRecommendation[]
}

model EnhancedLead {
  id                   String   @id @default(uuid())
  name                 String
  email                String
  visaType             String
  nationality          String
  documentCompleteness Float
  responseTime         Float
  financialCapacity    Float
  score                Float    @default(0)
  status               String   @default("new")
  assigneeId           String?
  routingHistory       Json?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  assignee             User?    @relation("LeadAssignee", fields: [assigneeId], references: [id])
}

model RoutingRule {
  id          String   @id @default(uuid())
  name        String
  description String
  priority    Int
  isActive    Boolean  @default(true)
  conditions  Json
  actions     Json
  assigneeId  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Application {
  id              String            @id @default(uuid())
  userId          String
  type            String            // e.g., "Student Visa - USA"
  status          ApplicationStatus @default(DRAFT)
  progress        Int               @default(0) // 0-100 percentage
  currentStage    VisaStage         @default(PROFILE_EVALUATION)
  submittedAt     DateTime?
  lastUpdated     DateTime          @updatedAt
  documents       Json?             // Document checklist and status
  notes           String?           @db.Text
  counselorId     String?
  estimatedCompletion DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
}

model Appointment {
  id              String            @id @default(uuid())
  userId          String
  title           String
  description     String?           @db.Text
  date            DateTime
  time            String
  duration        Int               @default(60) // minutes
  status          AppointmentStatus @default(SCHEDULED)
  counselorId     String?
  counselorName   String?
  meetingLink     String?
  notes           String?           @db.Text
  reminderSent    Boolean           @default(false)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([date])
  @@index([status])
}

model Task {
  id              String            @id @default(uuid())
  userId          String
  title           String
  description     String?           @db.Text
  status          TaskStatus        @default(PENDING)
  priority        Priority          @default(NORMAL)
  dueDate         DateTime?
  completedAt     DateTime?
  assignedBy      String?           // counselor or system
  category        String?           // e.g., "Documents", "Interview Prep"
  applicationId   String?           // linked to specific application
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
  @@index([dueDate])
}

model Message {
  id              String            @id @default(uuid())
  senderId        String
  receiverId      String
  type            MessageType       @default(USER_TO_COUNSELOR)
  subject         String?
  content         String            @db.Text
  isRead          Boolean           @default(false)
  readAt          DateTime?
  attachments     Json?             // File attachments
  metadata        Json?             // Additional message data
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  sender          User              @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver        User              @relation("MessageReceiver", fields: [receiverId], references: [id], onDelete: Cascade)

  @@index([senderId])
  @@index([receiverId])
  @@index([createdAt])
}

model Booking {
  id              String            @id @default(uuid())
  userId          String
  serviceType     String            // e.g., "Consultation", "Document Review"
  date            DateTime
  time            String
  duration        Int               @default(60)
  status          String            @default("confirmed")
  counselorName   String?
  meetingType     String            @default("video") // video, phone, in-person
  notes           String?           @db.Text
  price           Decimal?          @db.Decimal(10, 2)
  paymentStatus   String?           @default("pending")
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([date])
}

model TravelRecommendation {
  id              String            @id @default(uuid())
  userId          String
  title           String
  description     String            @db.Text
  destination     String
  category        String            // e.g., "Visa Requirements", "Travel Tips"
  priority        Priority          @default(NORMAL)
  isPersonalized  Boolean           @default(true)
  validUntil      DateTime?
  imageUrl        String?
  actionUrl       String?           // Link to more details
  isActive        Boolean           @default(true)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([category])
}


