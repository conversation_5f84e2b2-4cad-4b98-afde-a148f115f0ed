// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Add enums for better type safety
enum UserRole {
  USER
  ADMIN
  SALES_EXECUTIVE
  SALES_MANAGER
  CRM_MANAGER
  HR_MANAGER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
}

enum Priority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum CommunicationPref {
  EMAIL
  SMS
  PHONE
  IN_APP
}

model User {
  id                   String         @id @default(uuid())
  name                 String?
  email                String         @unique
  image                String?
  role                 UserRole       @default(USER)
  status               UserStatus     @default(ACTIVE)
  country              String?
  phone                String?
  visaType             String?
  visaGoals            String?        @db.Text
  priority             Priority       @default(NORMAL)
  profileCompleted     Boolean        @default(false)
  completedSteps       Json           @default("{}")
  profileProgress      Int            @default(20)      // Percentage complete
  avatar               String?
  communicationPreference CommunicationPref @default(EMAIL)
  receiveUpdates       Boolean?       @default(true)
  receivePromotions    Boolean?       @default(false)
  documents            Json?          // Stores document upload status
  lastLogin            DateTime?
  createdAt            DateTime       @default(now())
  updatedAt            DateTime       @updatedAt
  leads                EnhancedLead[] @relation("LeadAssignee")
}

model EnhancedLead {
  id                   String   @id @default(uuid())
  name                 String
  email                String
  visaType             String
  nationality          String
  documentCompleteness Float
  responseTime         Float
  financialCapacity    Float
  score                Float    @default(0)
  status               String   @default("new")
  assigneeId           String?
  routingHistory       Json?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  assignee             User?    @relation("LeadAssignee", fields: [assigneeId], references: [id])
}

model RoutingRule {
  id          String   @id @default(uuid())
  name        String
  description String
  priority    Int
  isActive    Boolean  @default(true)
  conditions  Json
  actions     Json
  assigneeId  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}


