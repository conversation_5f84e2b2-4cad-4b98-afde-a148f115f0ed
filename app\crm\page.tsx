"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { LeadScoringEngine } from "@/components/crm/lead-scoring-engine";
import { EmailCampaignStudio } from "@/components/crm/email-campaign-studio";
import GlassCard from "@/components/GlassCard";
import RoleBasedAccess from "@/components/auth/RoleBasedAccess";
import {
  Users,
  CheckCircle2,
  Clock,
  AlertTriangle,
  TrendingUp,
  Clipboard,
  Calendar,
  BarChart3
} from "lucide-react";

// Mock data for the dashboard
const dashboardData = {
  clients: {
    total: 156,
    active: 124,
    newThisMonth: 12,
    conversion: 73
  },
  visas: {
    inProgress: 68,
    approved: 32,
    pending: 14,
    rejected: 5
  },
  appointments: {
    today: 8,
    upcoming: 32,
    completed: 124
  },
  risks: {
    highRisk: 7,
    mediumRisk: 18,
    lowRisk: 99
  }
};

// Mock recent client activity
const recentActivity = [
  { id: 1, clientName: "<PERSON><PERSON>", action: "Visa Approved", date: "2 hours ago", status: "success" },
  { id: 2, clientName: "<PERSON>", action: "Documents Submitted", date: "5 hours ago", status: "info" },
  { id: 3, clientName: "Ananya Desai", action: "Visa Interview Scheduled", date: "1 day ago", status: "warning" },
  { id: 4, clientName: "Vikram Singh", action: "Initial Consultation", date: "2 days ago", status: "info" },
  { id: 5, clientName: "Meera Kapoor", action: "Payment Completed", date: "3 days ago", status: "success" },
];

// Mock upcoming tasks
const upcomingTasks = [
  { id: 1, clientName: "Arjun Mehta", task: "Visa Interview Preparation", deadline: "Tomorrow, 10:00 AM" },
  { id: 2, clientName: "Neha Gupta", task: "Document Review", deadline: "Today, 4:00 PM" },
  { id: 3, clientName: "Suresh Kumar", task: "Follow-up Call", deadline: "Today, 2:30 PM" },
  { id: 4, clientName: "Kavita Reddy", task: "Application Submission", deadline: "Tomorrow, 12:00 PM" },
];

export default function CrmDashboardPage() {
  return (
    <RoleBasedAccess allowedRoles={['admin', 'crm', 'sales-manager']}>
      <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
          CRM Dashboard
        </h1>
        <div>
          <button className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md">
            Add New Client
          </button>
        </div>
      </div>
      
      <Tabs defaultValue="overview" className="space-y-6">
        <div className="bg-white/50 backdrop-blur-sm p-1 rounded-lg inline-flex">
          <TabsList className="bg-transparent">
            <TabsTrigger 
              value="overview"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger 
              value="analytics"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              Analytics
            </TabsTrigger>
            <TabsTrigger 
              value="leads"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              Leads
            </TabsTrigger>
            <TabsTrigger 
              value="tools"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              Tools
            </TabsTrigger>
        </TabsList>
        </div>
        
        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Total Leads</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-400 to-indigo-300 flex items-center justify-center text-white shadow-sm">
                  <Users size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">2,368</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>+12.5% from last month</span>
                </p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Active Cases</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-indigo-400 to-purple-300 flex items-center justify-center text-white shadow-sm">
                  <Clipboard size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">1,274</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>+8.2% from last month</span>
                </p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Approval Rate</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-green-400 to-emerald-300 flex items-center justify-center text-white shadow-sm">
                  <CheckCircle2 size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">87.5%</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>+2.3% from last month</span>
                </p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Processing Time</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-amber-400 to-orange-300 flex items-center justify-center text-white shadow-sm">
                  <Clock size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">42 days</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>5 days faster than last month</span>
                </p>
              </div>
            </GlassCard>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-8">
            <GlassCard className="col-span-full md:col-span-4 p-6">
              <div className="flex flex-col h-full">
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Recent Activity</h3>
                <p className="text-sm text-gray-600 mb-4">Latest updates from the CRM system</p>
                
                <div className="space-y-3 flex-1 overflow-hidden">
                  {recentActivity.map((activity) => (
                    <div 
                      key={activity.id} 
                      className="bg-white/50 backdrop-blur-sm border border-indigo-100/30 rounded-lg p-3"
                    >
                      <div className="flex items-start">
                        <div 
                          className={`h-8 w-8 rounded-full flex items-center justify-center shadow-sm mr-3 ${
                            activity.status === 'success' ? 'bg-gradient-to-br from-green-400 to-emerald-300 text-white' :
                            activity.status === 'warning' ? 'bg-gradient-to-br from-amber-400 to-orange-300 text-white' :
                            'bg-gradient-to-br from-blue-400 to-indigo-300 text-white'
                          }`}
                        >
                          {activity.status === 'success' ? (
                            <CheckCircle2 size={16} />
                          ) : activity.status === 'warning' ? (
                            <AlertTriangle size={16} />
                          ) : (
                            <Clock size={16} />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between">
                            <p className="text-sm font-medium text-indigo-800">{activity.clientName}</p>
                            <p className="text-xs text-gray-500">{activity.date}</p>
                          </div>
                          <p className="text-xs text-indigo-600 mt-1">{activity.action}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <button className="mt-4 self-end text-xs text-indigo-600 hover:text-indigo-800 transition-colors flex items-center">
                  View All Activities
                </button>
              </div>
            </GlassCard>
            
            <GlassCard className="col-span-full md:col-span-4 p-6">
              <div className="flex flex-col h-full">
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Upcoming Tasks</h3>
                <p className="text-sm text-gray-600 mb-4">Scheduled appointments and deadlines</p>
                
                <div className="space-y-3 flex-1 overflow-hidden">
                  {upcomingTasks.map((task) => (
                    <div 
                      key={task.id} 
                      className="bg-white/50 backdrop-blur-sm border border-indigo-100/30 rounded-lg p-3"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-indigo-800">{task.task}</p>
                          <p className="text-xs text-gray-600 mt-1">Client: {task.clientName}</p>
                        </div>
                        <div className="bg-indigo-50/70 rounded-lg px-2.5 py-1 text-xs text-indigo-700 font-medium">
                          {task.deadline}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <button className="mt-4 self-end text-xs text-indigo-600 hover:text-indigo-800 transition-colors flex items-center">
                  View All Tasks
                </button>
              </div>
            </GlassCard>
            
            <GlassCard className="col-span-full lg:col-span-5 p-6">
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Visa Application Distribution</h3>
                <p className="text-sm text-gray-600 mb-4">Current visa application types</p>
                
                <div className="bg-indigo-50/50 rounded-lg p-4 text-center h-48 flex items-center justify-center">
                  <p className="text-sm text-indigo-600">Visa Types Chart Component</p>
                </div>
                
                <div className="grid grid-cols-4 gap-2 mt-4">
                  <div className="bg-white/70 backdrop-blur-sm p-2 rounded-lg border border-indigo-100/30">
                    <div className="text-xs text-gray-600">Tourist</div>
                    <div className="text-sm font-medium text-indigo-800">42%</div>
                  </div>
                  <div className="bg-white/70 backdrop-blur-sm p-2 rounded-lg border border-indigo-100/30">
                    <div className="text-xs text-gray-600">Study</div>
                    <div className="text-sm font-medium text-indigo-800">27%</div>
                  </div>
                  <div className="bg-white/70 backdrop-blur-sm p-2 rounded-lg border border-indigo-100/30">
                    <div className="text-xs text-gray-600">Work</div>
                    <div className="text-sm font-medium text-indigo-800">21%</div>
                  </div>
                  <div className="bg-white/70 backdrop-blur-sm p-2 rounded-lg border border-indigo-100/30">
                    <div className="text-xs text-gray-600">Other</div>
                    <div className="text-sm font-medium text-indigo-800">10%</div>
                  </div>
                </div>
              </div>
            </GlassCard>
            
            <GlassCard className="col-span-full lg:col-span-3 p-6">
              <div className="flex flex-col h-full">
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Quick Stats</h3>
                <p className="text-sm text-gray-600 mb-4">Key performance metrics</p>
                
                <div className="space-y-4 flex-1">
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Client Satisfaction</span>
                      <span className="text-xs font-medium text-indigo-800">94%</span>
                    </div>
                    <div className="h-1.5 rounded-full bg-indigo-100 overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full" style={{ width: '94%' }}></div>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Document Accuracy</span>
                      <span className="text-xs font-medium text-indigo-800">87%</span>
                    </div>
                    <div className="h-1.5 rounded-full bg-indigo-100 overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full" style={{ width: '87%' }}></div>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Processing Efficiency</span>
                      <span className="text-xs font-medium text-indigo-800">76%</span>
                    </div>
                    <div className="h-1.5 rounded-full bg-indigo-100 overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full" style={{ width: '76%' }}></div>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-600">Follow-up Rate</span>
                      <span className="text-xs font-medium text-indigo-800">92%</span>
                    </div>
                    <div className="h-1.5 rounded-full bg-indigo-100 overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full" style={{ width: '92%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-6">
          <GlassCard className="p-6">
            <h3 className="text-lg font-semibold text-indigo-800 mb-2">Conversion Analytics</h3>
            <p className="text-sm text-gray-600 mb-4">Track lead-to-client conversion rates</p>
            
            <div className="bg-indigo-50/50 rounded-lg p-4 text-center h-64 flex items-center justify-center">
              <div className="text-sm text-indigo-600">Conversion Analytics Component</div>
          </div>
          </GlassCard>
        </TabsContent>
        
        <TabsContent value="leads" className="space-y-6">
          <GlassCard className="p-6">
          <LeadScoringEngine />
          </GlassCard>
        </TabsContent>
        
        <TabsContent value="tools" className="space-y-6">
          <GlassCard className="p-6">
          <EmailCampaignStudio />
          </GlassCard>
        </TabsContent>
      </Tabs>
      </div>
    </RoleBasedAccess>
  );
}