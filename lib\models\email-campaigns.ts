/**
 * Email Template interface for the Email Campaign Studio
 */
export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  trigger: string;
  content: string;
  variables: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Email Campaign interface
 */
export interface EmailCampaign {
  id: string;
  name: string;
  description: string;
  templateId: string;
  status: 'draft' | 'scheduled' | 'active' | 'completed' | 'paused';
  segmentIds: string[];
  scheduledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  stats?: EmailCampaignStats;
}

/**
 * Email Campaign Analytics
 */
export interface EmailCampaignStats {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  bounced: number;
  unsubscribed: number;
  openRate: number;
  clickRate: number;
}

/**
 * Parse email content with variables
 */
export const parseEmailTemplate = (
  template: string, 
  variables: Record<string, string>
): string => {
  return template.replace(/{{([^{}]+)}}/g, (match, varName) => {
    const trimmedVarName = varName.trim();
    return variables[trimmedVarName] || match;
  });
};

/**
 * Calculate campaign metrics
 */
export const calculateCampaignMetrics = (campaign: Partial<EmailCampaignStats>): Partial<EmailCampaignStats> => {
  const stats = { ...campaign };
  
  if (stats.sent && stats.delivered) {
    const opened = stats.opened || 0;
    const clicked = stats.clicked || 0;
    
    stats.openRate = (opened / stats.delivered) * 100;
    stats.clickRate = opened > 0 ? (clicked / opened) * 100 : 0;
  }
  
  return stats;
}; 