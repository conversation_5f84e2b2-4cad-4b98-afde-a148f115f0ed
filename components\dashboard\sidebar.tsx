"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  FileText,
  Calendar,
  MessageSquare,
  CreditCard,
  Settings,
  HelpCircle,
  LogOut,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function DashboardSidebar() {
  const pathname = usePathname()

  const routes = [
    {
      label: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      href: "/dashboard",
      active: pathname === "/dashboard",
    },
    {
      label: "Applications",
      icon: <FileText className="h-5 w-5" />,
      href: "/dashboard/applications",
      active: pathname === "/dashboard/applications",
    },
    {
      label: "Appointments",
      icon: <Calendar className="h-5 w-5" />,
      href: "/dashboard/appointments",
      active: pathname === "/dashboard/appointments",
    },
    {
      label: "Messages",
      icon: <MessageSquare className="h-5 w-5" />,
      href: "/dashboard/messages",
      active: pathname === "/dashboard/messages",
    },
    {
      label: "Payments",
      icon: <CreditCard className="h-5 w-5" />,
      href: "/dashboard/payments",
      active: pathname === "/dashboard/payments",
    },
    {
      label: "Settings",
      icon: <Settings className="h-5 w-5" />,
      href: "/dashboard/settings",
      active: pathname === "/dashboard/settings",
    },
    {
      label: "Help & Support",
      icon: <HelpCircle className="h-5 w-5" />,
      href: "/dashboard/support",
      active: pathname === "/dashboard/support",
    },
  ]

  return (
    <div className="hidden md:flex h-screen w-64 flex-col bg-white border-r">
      <div className="p-6">
        <Link href="/" className="flex items-center gap-2">
          <span className="font-outfit font-bold text-xl text-[#1E90FF]">Visa Mentor</span>
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid gap-1 px-2">
          {routes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium",
                route.active
                  ? "bg-[#1E90FF]/10 text-[#1E90FF]"
                  : "text-gray-500 hover:text-[#1E90FF] hover:bg-[#1E90FF]/5",
              )}
            >
              {route.icon}
              {route.label}
            </Link>
          ))}
        </nav>
      </div>
      <div className="p-4 border-t">
        <Button
          variant="outline"
          className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
          asChild
        >
          <Link href="/logout">
            <LogOut className="mr-2 h-4 w-4" />
            Log out
          </Link>
        </Button>
      </div>
    </div>
  )
}
