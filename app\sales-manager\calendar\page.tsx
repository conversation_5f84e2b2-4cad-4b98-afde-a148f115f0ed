"use client";

import { useState } from "react";
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar as CalendarIcon, 
  Clock, 
  Users, 
  Filter,
  Plus,
  Search,
  MoreHorizontal,
  Check,
  X
} from "lucide-react";

// Define types for our data
interface TeamMember {
  id: number;
  name: string;
  avatar: string;
  color: string;
  available: boolean;
}

interface Event {
  id: number;
  title: string;
  date: string;
  time: string;
  members: number[];
  type: string;
  location: string;
}

interface CalendarDay {
  day: number | null;
  date: string | null;
  events?: Event[];
}

// Mock team members
const teamMembers: TeamMember[] = [
  { id: 1, name: '<PERSON><PERSON>', avatar: '', color: 'bg-indigo-600', available: true },
  { id: 2, name: '<PERSON><PERSON>', avatar: '', color: 'bg-blue-600', available: true },
  { id: 3, name: '<PERSON><PERSON>', avatar: '', color: 'bg-purple-600', available: false },
  { id: 4, name: '<PERSON><PERSON><PERSON>', avatar: '', color: 'bg-green-600', available: true },
];

// Mock events
const events: Event[] = [
  { id: 1, title: 'Team Meeting', date: '2023-07-12', time: '10:00 AM - 11:00 AM', members: [1, 2, 3, 4], type: 'meeting', location: 'Conference Room A' },
  { id: 2, title: 'Client Consultation: Tech Masters', date: '2023-07-12', time: '1:00 PM - 2:00 PM', members: [1, 3], type: 'client', location: 'Virtual (Zoom)' },
  { id: 3, title: 'Document Review: Johnson Family', date: '2023-07-12', time: '3:30 PM - 4:30 PM', members: [2, 4], type: 'task', location: 'Office' },
  { id: 4, title: 'Training Session: New Visa Policies', date: '2023-07-13', time: '11:00 AM - 12:30 PM', members: [1, 2, 3, 4], type: 'training', location: 'Training Room' },
  { id: 5, title: 'EB-1 Strategy Planning', date: '2023-07-13', time: '2:00 PM - 3:00 PM', members: [1, 4], type: 'meeting', location: 'Meeting Room B' },
  { id: 6, title: 'Client Call: Global Tech', date: '2023-07-14', time: '9:30 AM - 10:30 AM', members: [3], type: 'client', location: 'Phone' },
  { id: 7, title: 'Deadline: H-1B Submission', date: '2023-07-14', time: '5:00 PM', members: [2], type: 'deadline', location: '' },
  { id: 8, title: 'Performance Reviews', date: '2023-07-15', time: '10:00 AM - 4:00 PM', members: [1], type: 'meeting', location: 'Manager Office' },
];

// Generate days for the calendar
const generateCalendarDays = (month: number, year: number): CalendarDay[] => {
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  const firstDayOfMonth = new Date(year, month, 1).getDay();
  
  const days: CalendarDay[] = [];
  
  // Add empty days for the start of the month
  for (let i = 0; i < firstDayOfMonth; i++) {
    days.push({ day: null, date: null });
  }
  
  // Add days in the month
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    const dateString = date.toISOString().split('T')[0];
    
    days.push({
      day,
      date: dateString,
      events: events.filter(event => event.date === dateString)
    });
  }
  
  return days;
};

export default function CalendarPage() {
  const currentDate = new Date();
  const [month, setMonth] = useState(currentDate.getMonth());
  const [year, setYear] = useState(currentDate.getFullYear());
  const [selectedDate, setSelectedDate] = useState(currentDate.toISOString().split('T')[0]);
  const [viewType, setViewType] = useState<'month' | 'week' | 'day'>('month');
  const [filteredTeamMembers, setFilteredTeamMembers] = useState(teamMembers.map(member => member.id));
  
  const calendarDays = generateCalendarDays(month, year);
  const selectedDateEvents = events.filter(event => event.date === selectedDate);
  
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  const goToPreviousMonth = () => {
    if (month === 0) {
      setMonth(11);
      setYear(year - 1);
    } else {
      setMonth(month - 1);
    }
  };
  
  const goToNextMonth = () => {
    if (month === 11) {
      setMonth(0);
      setYear(year + 1);
    } else {
      setMonth(month + 1);
    }
  };
  
  const toggleTeamMemberFilter = (memberId: number) => {
    if (filteredTeamMembers.includes(memberId)) {
      setFilteredTeamMembers(filteredTeamMembers.filter(id => id !== memberId));
    } else {
      setFilteredTeamMembers([...filteredTeamMembers, memberId]);
    }
  };
  
  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'meeting': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'client': return 'bg-green-100 text-green-800 border-green-200';
      case 'task': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'training': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'deadline': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">Team Calendar</h1>
          <p className="text-gray-500 mt-1">Manage schedules and coordinate team activities</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button 
              className={`px-3 py-1.5 text-sm font-medium ${viewType === 'month' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700'}`}
              onClick={() => setViewType('month')}
            >
              Month
            </button>
            <button 
              className={`px-3 py-1.5 text-sm font-medium ${viewType === 'week' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700'}`}
              onClick={() => setViewType('week')}
            >
              Week
            </button>
            <button 
              className={`px-3 py-1.5 text-sm font-medium ${viewType === 'day' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700'}`}
              onClick={() => setViewType('day')}
            >
              Day
            </button>
          </div>
          <button className="flex items-center gap-1 bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700">
            <Plus className="h-4 w-4" />
            <span>Add Event</span>
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-12 gap-6">
        {/* Calendar sidebar */}
        <div className="col-span-12 md:col-span-3 space-y-6">
          {/* Month selector */}
          <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <button onClick={goToPreviousMonth} className="p-1 rounded-full hover:bg-gray-100">
                <ChevronLeft className="h-5 w-5 text-gray-600" />
              </button>
              <h2 className="text-lg font-medium text-gray-800">
                {monthNames[month]} {year}
              </h2>
              <button onClick={goToNextMonth} className="p-1 rounded-full hover:bg-gray-100">
                <ChevronRight className="h-5 w-5 text-gray-600" />
              </button>
            </div>
            
            <div className="grid grid-cols-7 gap-1">
              {dayNames.map((day, index) => (
                <div key={index} className="text-center text-xs font-medium text-gray-500 py-1">
                  {day}
                </div>
              ))}
              
              {calendarDays.map((day, index) => (
                <div 
                  key={index} 
                  className={`
                    text-center text-sm py-1 rounded-full cursor-pointer
                    ${day.day === null ? 'text-gray-300' : 'text-gray-800'}
                    ${day.date === selectedDate ? 'bg-indigo-600 text-white' : 'hover:bg-gray-100'}
                    ${day.events && day.events.length > 0 ? 'font-medium' : ''}
                  `}
                  onClick={() => day.date && setSelectedDate(day.date)}
                >
                  {day.day}
                  {day.events && day.events.length > 0 && (
                    <div className="h-1 w-1 bg-indigo-500 rounded-full mx-auto mt-0.5"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Team members filter */}
          <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-sm font-medium text-gray-800 mb-3 flex items-center">
              <Users className="h-4 w-4 mr-1" />
              Team Members
            </h3>
            
            <div className="space-y-2">
              {teamMembers.map((member) => (
                <div key={member.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`member-${member.id}`}
                    checked={filteredTeamMembers.includes(member.id)}
                    onChange={() => toggleTeamMemberFilter(member.id)}
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                  <label htmlFor={`member-${member.id}`} className="ml-2 flex items-center">
                    <div className={`h-6 w-6 rounded-full ${member.color} flex items-center justify-center text-white text-xs font-medium mr-2`}>
                      {member.name.charAt(0)}
                    </div>
                    <span className="text-sm text-gray-700">{member.name}</span>
                    {!member.available && (
                      <span className="ml-2 bg-red-100 text-red-800 text-xs px-1.5 py-0.5 rounded-full">Away</span>
                    )}
                  </label>
                </div>
              ))}
            </div>
          </div>
          
          {/* Event types filter */}
          <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-sm font-medium text-gray-800 mb-3 flex items-center">
              <Filter className="h-4 w-4 mr-1" />
              Event Types
            </h3>
            
            <div className="space-y-2">
              {['Meeting', 'Client', 'Task', 'Training', 'Deadline'].map((type) => (
                <div key={type} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`type-${type}`}
                    checked={true}
                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                  <label htmlFor={`type-${type}`} className="ml-2 text-sm text-gray-700">
                    {type}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Calendar main content */}
        <div className="col-span-12 md:col-span-9">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-800">
                  {new Date(selectedDate).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                </h2>
                <div className="relative text-gray-500">
                  <Search className="h-4 w-4 absolute top-1/2 left-3 -translate-y-1/2" />
                  <input 
                    type="text" 
                    placeholder="Search events..." 
                    className="pl-9 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>
            </div>
            
            {/* Day view */}
            {viewType === 'day' && (
              <div className="p-6 space-y-4">
                {selectedDateEvents.length === 0 ? (
                  <div className="text-center py-12">
                    <CalendarIcon className="mx-auto h-12 w-12 text-gray-300" />
                    <h3 className="mt-2 text-lg font-medium text-gray-700">No events scheduled</h3>
                    <p className="mt-1 text-sm text-gray-500">Click the "Add Event" button to create a new event</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {selectedDateEvents
                      .filter(event => {
                        // Check if any assigned member is in the filtered list
                        return event.members.some(memberId => 
                          filteredTeamMembers.includes(memberId)
                        );
                      })
                      .sort((a, b) => {
                        return a.time.localeCompare(b.time);
                      })
                      .map((event) => (
                        <div key={event.id} className={`p-4 rounded-lg border ${getEventTypeColor(event.type)}`}>
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-medium text-gray-800">{event.title}</h3>
                              <div className="mt-1 flex items-center text-sm">
                                <Clock className="h-4 w-4 mr-1 text-gray-500" />
                                <span>{event.time}</span>
                                {event.location && (
                                  <>
                                    <span className="mx-2">•</span>
                                    <span>{event.location}</span>
                                  </>
                                )}
                              </div>
                            </div>
                            <button className="text-gray-500 hover:text-gray-700">
                              <MoreHorizontal className="h-5 w-5" />
                            </button>
                          </div>
                          <div className="mt-3 flex items-center space-x-1">
                            {event.members.map((memberId) => {
                              const member = teamMembers.find(m => m.id === memberId);
                              return member ? (
                                <div key={member.id} className={`h-6 w-6 rounded-full ${member.color} flex items-center justify-center text-white text-xs font-medium`}>
                                  {member.name.charAt(0)}
                                </div>
                              ) : null;
                            })}
                          </div>
                        </div>
                      ))
                    }
                  </div>
                )}
              </div>
            )}
            
            {/* Month view (simplified) */}
            {viewType === 'month' && (
              <div className="p-4">
                <div className="grid grid-cols-7 gap-4">
                  {dayNames.map((day) => (
                    <div key={day} className="text-center py-2 font-medium text-sm text-gray-600 border-b border-gray-200">
                      {day}
                    </div>
                  ))}
                  
                  {calendarDays.map((day, index) => (
                    <div 
                      key={index}
                      className={`min-h-[100px] p-1 border border-gray-100 ${day.date === selectedDate ? 'bg-indigo-50 border-indigo-200' : ''}`}
                      onClick={() => day.date && setSelectedDate(day.date)}
                    >
                      {day.day !== null && (
                        <>
                          <div className="text-right mb-1">
                            <span 
                              className={`inline-block h-6 w-6 rounded-full text-center leading-6 text-sm
                                ${day.date === new Date().toISOString().split('T')[0] 
                                  ? 'bg-indigo-600 text-white' 
                                  : 'text-gray-700'
                                }
                              `}
                            >
                              {day.day}
                            </span>
                          </div>
                          <div className="space-y-1">
                            {day.events && day.events
                              .filter(event => event.members.some(memberId => filteredTeamMembers.includes(memberId)))
                              .slice(0, 2)
                              .map((event) => (
                                <div 
                                  key={event.id} 
                                  className={`px-1 py-0.5 text-xs rounded truncate ${getEventTypeColor(event.type)}`}
                                >
                                  {event.title}
                                </div>
                              ))
                            }
                            {day.events && day.events.filter(event => event.members.some(memberId => filteredTeamMembers.includes(memberId))).length > 2 && (
                              <div className="text-xs text-center text-gray-500">
                                +{day.events.filter(event => event.members.some(memberId => filteredTeamMembers.includes(memberId))).length - 2} more
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Week view (placeholder) */}
            {viewType === 'week' && (
              <div className="p-4">
                <div className="text-center py-12">
                  <h3 className="text-lg font-medium text-gray-700">Week View Coming Soon</h3>
                  <p className="mt-1 text-sm text-gray-500">Please use Month or Day view for now</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 