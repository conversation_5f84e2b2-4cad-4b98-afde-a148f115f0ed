"use client";

import { useState } from "react";
import Link from "next/link";
import { ChevronLeft, Save, Lock, ShieldAlert, ShieldCheck, Key, Clock, User, AlertCircle, ToggleLeft, ToggleRight } from "lucide-react";

// Form Field Component
interface FormFieldProps {
  label: string;
  children: React.ReactNode;
  helper?: string;
  badge?: {
    text: string;
    color: string;
  };
}

const FormField = ({ label, children, helper, badge }: FormFieldProps) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start py-4 border-b border-gray-100 last:border-0">
    <div>
      <div className="flex items-center">
        <label className="block text-sm font-medium text-gray-700">{label}</label>
        {badge && (
          <span className={`ml-2 text-xs px-2 py-0.5 rounded-full ${badge.color}`}>
            {badge.text}
          </span>
        )}
      </div>
      {helper && <p className="mt-1 text-xs text-gray-500">{helper}</p>}
    </div>
    <div className="md:col-span-2">
      {children}
    </div>
  </div>
);

// Toggle Switch Component
interface ToggleSwitchProps {
  enabled: boolean;
  onChange: () => void;
  size?: "sm" | "md" | "lg";
}

const ToggleSwitch = ({ enabled, onChange, size = "md" }: ToggleSwitchProps) => {
  const sizeClasses = {
    sm: "w-8 h-4",
    md: "w-11 h-6",
    lg: "w-14 h-7"
  };
  
  const thumbSizeClasses = {
    sm: "h-3 w-3",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  };
  
  const translateClasses = {
    sm: "translate-x-4",
    md: "translate-x-5",
    lg: "translate-x-7"
  };
  
  return (
    <button
      type="button"
      className={`${
        enabled ? 'bg-indigo-600' : 'bg-gray-200'
      } relative inline-flex flex-shrink-0 ${sizeClasses[size]} border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none`}
      role="switch"
      aria-checked={enabled}
      onClick={onChange}
    >
      <span
        className={`${
          enabled ? translateClasses[size] : 'translate-x-0'
        } pointer-events-none relative inline-block ${thumbSizeClasses[size]} rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200`}
      />
    </button>
  );
};

export default function SecuritySettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  // Security settings state
  const [settings, setSettings] = useState({
    // Authentication
    mfaEnabled: false,
    mfaRequiredForAdmins: true,
    mfaRequiredForAll: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    
    // IP & Access
    allowedIPs: "",
    blockForeignIPs: false,
    enableGeoFencing: false,
    
    // Encryption & Privacy
    encryptionLevel: "high",
    enableSSL: true,
    enableDataAnonymization: false,
    
    // Audit & Logs
    logSuccessfulLogins: true,
    logFailedLogins: true,
    logAdminActions: true,
    retainLogs: 180
  });
  
  // Handle toggle changes
  const toggleSetting = (field: string) => {
    setSettings({
      ...settings,
      [field]: !settings[field as keyof typeof settings]
    });
  };
  
  // Handle input changes
  const handleChange = (field: string, value: string | number) => {
    setSettings({
      ...settings,
      [field]: value
    });
  };
  
  // Save settings
  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Link href="/admin/settings" className="text-gray-500 hover:text-gray-700 mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-800">Security Settings</h1>
        </div>
        <p className="text-gray-600">Configure security policies, authentication options, and data protection</p>
      </div>
      
      {/* Success Message */}
      {showSuccess && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-800 rounded-lg p-4 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <span>Security settings have been successfully updated.</span>
        </div>
      )}
      
      {/* Security Status Card */}
      <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-xl border border-indigo-100 p-6 mb-6">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-lg font-semibold text-indigo-900">Security Status</h2>
            <p className="text-indigo-700 mt-1">Your current security configuration is <span className="font-semibold">Strong</span></p>
            
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white bg-opacity-60 p-3 rounded-lg flex items-center">
                <div className="rounded-full bg-green-100 p-2 mr-3">
                  <ShieldCheck className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-800">Password Policy</span>
                  <p className="text-xs text-gray-500">Strong</p>
                </div>
              </div>
              
              <div className="bg-white bg-opacity-60 p-3 rounded-lg flex items-center">
                <div className="rounded-full bg-amber-100 p-2 mr-3">
                  <Key className="h-5 w-5 text-amber-600" />
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-800">2-Factor Auth</span>
                  <p className="text-xs text-gray-500">Admins Only</p>
                </div>
              </div>
              
              <div className="bg-white bg-opacity-60 p-3 rounded-lg flex items-center">
                <div className="rounded-full bg-green-100 p-2 mr-3">
                  <Lock className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-800">Data Encryption</span>
                  <p className="text-xs text-gray-500">High</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="hidden md:block">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-indigo-100">
                <ShieldCheck className="h-10 w-10 text-indigo-600" />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Settings Form */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <Lock className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-semibold">Authentication Settings</h2>
          </div>
          
          <div className="space-y-2">
            <FormField 
              label="Session Timeout" 
              helper="Set the duration after which inactive users will be logged out"
            >
              <div className="flex items-center">
                <input
                  type="number"
                  min="5"
                  max="1440"
                  className="w-20 p-2 border border-gray-300 rounded-md"
                  value={settings.sessionTimeout}
                  onChange={(e) => handleChange('sessionTimeout', parseInt(e.target.value))}
                />
                <span className="ml-2 text-gray-500">minutes</span>
              </div>
            </FormField>
            
            <FormField 
              label="Maximum Login Attempts" 
              helper="Number of failed login attempts before account lockout"
            >
              <div className="flex items-center">
                <input
                  type="number"
                  min="1"
                  max="10"
                  className="w-20 p-2 border border-gray-300 rounded-md"
                  value={settings.maxLoginAttempts}
                  onChange={(e) => handleChange('maxLoginAttempts', parseInt(e.target.value))}
                />
                <span className="ml-2 text-gray-500">attempts</span>
              </div>
            </FormField>
            
            <FormField 
              label="Account Lockout Duration" 
              helper="Duration for which account remains locked after exceeding maximum login attempts"
            >
              <div className="flex items-center">
                <input
                  type="number"
                  min="5"
                  max="1440"
                  className="w-20 p-2 border border-gray-300 rounded-md"
                  value={settings.lockoutDuration}
                  onChange={(e) => handleChange('lockoutDuration', parseInt(e.target.value))}
                />
                <span className="ml-2 text-gray-500">minutes</span>
              </div>
            </FormField>
          </div>
        </div>
        
        <div className="border-t border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <ShieldCheck className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-semibold">Multi-Factor Authentication</h2>
          </div>
          
          <div className="space-y-2">
            <FormField 
              label="Enable MFA" 
              helper="Allow multi-factor authentication for accounts"
              badge={{ text: "Recommended", color: "bg-green-100 text-green-700" }}
            >
              <div className="flex items-center">
                <ToggleSwitch 
                  enabled={settings.mfaEnabled} 
                  onChange={() => toggleSetting('mfaEnabled')}
                />
                <span className="ml-2 text-sm text-gray-700">
                  {settings.mfaEnabled ? "Enabled" : "Disabled"}
                </span>
              </div>
            </FormField>
            
            {settings.mfaEnabled && (
              <>
                <FormField 
                  label="Required for Admins" 
                  helper="Make MFA mandatory for admin accounts"
                >
                  <div className="flex items-center">
                    <ToggleSwitch 
                      enabled={settings.mfaRequiredForAdmins} 
                      onChange={() => toggleSetting('mfaRequiredForAdmins')}
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {settings.mfaRequiredForAdmins ? "Required" : "Optional"}
                    </span>
                  </div>
                </FormField>
                
                <FormField 
                  label="Required for All Users" 
                  helper="Make MFA mandatory for all user accounts"
                >
                  <div className="flex items-center">
                    <ToggleSwitch 
                      enabled={settings.mfaRequiredForAll} 
                      onChange={() => toggleSetting('mfaRequiredForAll')}
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {settings.mfaRequiredForAll ? "Required" : "Optional"}
                    </span>
                  </div>
                </FormField>
              </>
            )}
            
            <FormField 
              label="Session Timeout" 
              helper="Automatically log out inactive users"
            >
              <div className="flex items-center">
                <input
                  type="number"
                  min="5"
                  max="1440"
                  className="w-20 p-2 border border-gray-300 rounded-md"
                  value={settings.sessionTimeout}
                  onChange={(e) => handleChange('sessionTimeout', parseInt(e.target.value))}
                />
                <span className="ml-2 text-gray-500">minutes</span>
              </div>
            </FormField>
          </div>
        </div>
        
        <div className="border-t border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <ShieldAlert className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-semibold">Login Security</h2>
          </div>
          
          <div className="space-y-2">
            <FormField 
              label="IP Address Restrictions" 
              helper="Restrict login to specific IP addresses (separate with commas)"
            >
              <textarea
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={2}
                placeholder="e.g., ***********, ********"
                value={settings.allowedIPs}
                onChange={(e) => handleChange('allowedIPs', e.target.value)}
              />
            </FormField>
            
            <FormField 
              label="Geo-Restrictions" 
              helper="Enable geographic restrictions for application access"
            >
              <div className="space-y-2">
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.blockForeignIPs} 
                    onChange={() => toggleSetting('blockForeignIPs')}
                  />
                  <span className="ml-2 text-sm text-gray-700">Block login attempts from foreign countries</span>
                </div>
                
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.enableGeoFencing} 
                    onChange={() => toggleSetting('enableGeoFencing')}
                  />
                  <span className="ml-2 text-sm text-gray-700">Enable geo-fencing for application access</span>
                </div>
              </div>
            </FormField>
          </div>
        </div>
        
        <div className="border-t border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <Lock className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-semibold">Data Protection</h2>
          </div>
          
          <div className="space-y-2">
            <FormField 
              label="Encryption Level" 
              helper="Set the encryption level for sensitive data"
              badge={{ text: "Critical", color: "bg-red-100 text-red-700" }}
            >
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.encryptionLevel}
                onChange={(e) => handleChange('encryptionLevel', e.target.value)}
              >
                <option value="standard">Standard (AES-128)</option>
                <option value="high">High (AES-256)</option>
                <option value="military">Military Grade (AES-256 with enhanced protocols)</option>
              </select>
            </FormField>
            
            <FormField 
              label="Force SSL" 
              helper="Require SSL for all connections to the application"
            >
              <div className="flex items-center">
                <ToggleSwitch 
                  enabled={settings.enableSSL} 
                  onChange={() => toggleSetting('enableSSL')}
                />
                <span className="ml-2 text-sm text-gray-700">
                  {settings.enableSSL ? "Enabled (HTTPS required)" : "Disabled (HTTP allowed)"}
                </span>
              </div>
            </FormField>
            
            <FormField 
              label="Data Anonymization" 
              helper="Anonymize personal data in logs and reports"
            >
              <div className="flex items-center">
                <ToggleSwitch 
                  enabled={settings.enableDataAnonymization} 
                  onChange={() => toggleSetting('enableDataAnonymization')}
                />
                <span className="ml-2 text-sm text-gray-700">
                  {settings.enableDataAnonymization ? "Enabled" : "Disabled"}
                </span>
              </div>
            </FormField>
          </div>
        </div>
        
        <div className="border-t border-gray-100 p-6">
          <div className="flex items-center mb-4">
            <Clock className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-semibold">Audit & Logging</h2>
          </div>
          
          <div className="space-y-2">
            <FormField 
              label="Security Logging" 
              helper="Configure what security events to log"
            >
              <div className="space-y-2">
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.logSuccessfulLogins} 
                    onChange={() => toggleSetting('logSuccessfulLogins')}
                  />
                  <span className="ml-2 text-sm text-gray-700">Log successful login attempts</span>
                </div>
                
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.logFailedLogins} 
                    onChange={() => toggleSetting('logFailedLogins')}
                  />
                  <span className="ml-2 text-sm text-gray-700">Log failed login attempts</span>
                </div>
                
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.logAdminActions} 
                    onChange={() => toggleSetting('logAdminActions')}
                  />
                  <span className="ml-2 text-sm text-gray-700">Log all administrative actions</span>
                </div>
              </div>
            </FormField>
            
            <FormField 
              label="Log Retention Period" 
              helper="How long to keep security logs"
            >
              <div className="flex items-center">
                <input
                  type="number"
                  min="30"
                  max="3650"
                  className="w-20 p-2 border border-gray-300 rounded-md"
                  value={settings.retainLogs}
                  onChange={(e) => handleChange('retainLogs', parseInt(e.target.value))}
                />
                <span className="ml-2 text-gray-500">days</span>
              </div>
            </FormField>
          </div>
        </div>
        
        <div className="border-t border-gray-100 p-6 bg-gray-50 flex justify-end">
          <div className="flex gap-3">
            <Link 
              href="/admin/settings"
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
            >
              Cancel
            </Link>
            <button 
              onClick={saveSettings}
              className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
                isSaving 
                  ? "bg-gray-200 text-gray-600 cursor-not-allowed" 
                  : "bg-indigo-600 text-white hover:bg-indigo-700"
              }`}
              disabled={isSaving}
            >
              <Save className="h-4 w-4" />
              {isSaving ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 