"use client";

import { useState } from "react";
import {
  Search,
  Filter,
  ChevronDown,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  Upload,
  AlertTriangle,
  FileText,
  ChevronLeft,
  ChevronRight,
  MessageSquare,
  Send,
  Calendar,
  Camera,
  Info
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock data for documents
const mockDocuments = [
  {
    id: "doc-1",
    userId: "u-1",
    userName: "<PERSON>",
    applicationId: "app-123",
    filename: "passport_scan.pdf",
    originalName: "passport_JohnSmith.pdf",
    path: "/uploads/documents/passport_scan.pdf",
    size: 2450000,
    mimetype: "application/pdf",
    documentType: "passport",
    status: "SUBMITTED",
    uploadedAt: "2023-09-15T09:30:00Z",
  },
  {
    id: "doc-2",
    userId: "u-2",
    userName: "<PERSON>",
    applicationId: "app-456",
    filename: "financial_statement.pdf",
    originalName: "bank_statement_q3_2023.pdf",
    path: "/uploads/documents/financial_statement.pdf",
    size: 1250000,
    mimetype: "application/pdf",
    documentType: "financials",
    status: "APPROVED",
    reviewNotes: "All financial requirements met.",
    reviewedBy: "<EMAIL>",
    reviewedAt: "2023-09-16T14:20:00Z",
    uploadedAt: "2023-09-14T11:15:00Z",
  },
  {
    id: "doc-3",
    userId: "u-3",
    userName: "Robert Chen",
    applicationId: "app-789",
    filename: "invitation_letter.pdf",
    originalName: "invitation_university.pdf",
    path: "/uploads/documents/invitation_letter.pdf",
    size: 890000,
    mimetype: "application/pdf",
    documentType: "invitation",
    status: "REJECTED",
    reviewNotes: "Document appears to be modified. Please submit original letter.",
    reviewedBy: "<EMAIL>",
    reviewedAt: "2023-09-17T10:45:00Z",
    uploadedAt: "2023-09-16T08:30:00Z",
  },
  {
    id: "doc-4",
    userId: "u-4",
    userName: "Sophia Rodriguez",
    applicationId: "app-012",
    filename: "photo.jpg",
    originalName: "passport_photo.jpg",
    path: "/uploads/documents/photo.jpg",
    size: 450000,
    mimetype: "image/jpeg",
    documentType: "photo",
    status: "SUBMITTED",
    uploadedAt: "2023-09-18T15:20:00Z",
  },
  {
    id: "doc-5",
    userId: "u-1",
    userName: "John Smith",
    applicationId: "app-123",
    filename: "travel_itinerary.pdf",
    originalName: "flight_booking.pdf",
    path: "/uploads/documents/travel_itinerary.pdf",
    size: 1050000,
    mimetype: "application/pdf",
    documentType: "travel",
    status: "SUBMITTED",
    uploadedAt: "2023-09-15T09:45:00Z",
  },
  {
    id: "doc-6",
    userId: "u-5",
    userName: "Daniel Wilson",
    applicationId: "app-345",
    filename: "employment_verification.pdf",
    originalName: "employment_letter.pdf",
    path: "/uploads/documents/employment_verification.pdf",
    size: 780000,
    mimetype: "application/pdf",
    documentType: "employment",
    status: "SUBMITTED",
    uploadedAt: "2023-09-19T11:30:00Z",
  },
];

export default function DocumentsPage() {
  const [documents, setDocuments] = useState(mockDocuments);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);
  const [reviewNotes, setReviewNotes] = useState("");
  
  const itemsPerPage = 5;
  
  // Document type options
  const documentTypes = [
    { id: "all", name: "All Types" },
    { id: "passport", name: "Passport" },
    { id: "photo", name: "Photograph" },
    { id: "financials", name: "Financial Documents" },
    { id: "invitation", name: "Invitation Letter" },
    { id: "travel", name: "Travel Itinerary" },
    { id: "employment", name: "Employment Verification" },
  ];
  
  // Filter documents
  const filteredDocuments = documents.filter(doc => {
    // Search filter
    const searchMatch = 
      doc.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.documentType.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Status filter
    const statusMatch = statusFilter === "all" || doc.status === statusFilter;
    
    // Type filter
    const typeMatch = typeFilter === "all" || doc.documentType === typeFilter;
    
    return searchMatch && statusMatch && typeMatch;
  });
  
  // Pagination
  const totalPages = Math.ceil(filteredDocuments.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedDocuments = filteredDocuments.slice(startIndex, startIndex + itemsPerPage);
  
  // Document review actions
  const handleApproveDocument = (docId: string) => {
    setDocuments(documents.map(doc => 
      doc.id === docId 
        ? { 
            ...doc, 
            status: "APPROVED", 
            reviewNotes: reviewNotes || "Approved after verification.", 
            reviewedBy: "<EMAIL>", 
            reviewedAt: new Date().toISOString() 
          }
        : doc
    ));
    setSelectedDocument(null);
    setReviewNotes("");
  };
  
  const handleRejectDocument = (docId: string) => {
    if (!reviewNotes) {
      alert("Please provide rejection reason before rejecting document.");
      return;
    }
    
    setDocuments(documents.map(doc => 
      doc.id === docId 
        ? { 
            ...doc, 
            status: "REJECTED", 
            reviewNotes: reviewNotes, 
            reviewedBy: "<EMAIL>", 
            reviewedAt: new Date().toISOString() 
          }
        : doc
    ));
    setSelectedDocument(null);
    setReviewNotes("");
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / 1048576).toFixed(1) + " MB";
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "APPROVED":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "REJECTED":
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-blue-500" />;
    }
  };
  
  const getDocumentIcon = (mimetype: string) => {
    if (mimetype.startsWith("image/")) {
      return <Camera className="h-5 w-5 text-indigo-500" />;
    } else {
      return <FileText className="h-5 w-5 text-indigo-500" />;
    }
  };
  
  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
          Document Verification
        </h1>
        <p className="text-gray-600 mt-1">Review and verify submitted documents</p>
      </div>
      
      {/* Filters & Actions */}
      <GlassCard className="mb-6 transform transition hover:translate-y-[-2px]">
        <div className="p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            {/* Search */}
            <div className="relative w-full sm:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-indigo-400" />
              </div>
              <input
                type="text"
                placeholder="Search documents..."
                className="pl-10 pr-3 py-2 w-full border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            {/* Filters */}
            <div className="flex items-center gap-3 w-full sm:w-auto">
              <div className="relative w-full sm:w-auto">
                <select
                  className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="all">All Statuses</option>
                  <option value="SUBMITTED">Submitted</option>
                  <option value="APPROVED">Approved</option>
                  <option value="REJECTED">Rejected</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
              
              <div className="relative w-full sm:w-auto">
                <select
                  className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  {documentTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
            </div>
            
            {/* Stats */}
            <div className="flex items-center gap-4 text-sm">
              <div>
                <span className="font-medium text-green-600">{documents.filter(d => d.status === "APPROVED").length}</span> Approved
              </div>
              <div>
                <span className="font-medium text-red-600">{documents.filter(d => d.status === "REJECTED").length}</span> Rejected
              </div>
              <div>
                <span className="font-medium text-blue-600">{documents.filter(d => d.status === "SUBMITTED").length}</span> Pending
              </div>
            </div>
          </div>
        </div>
      </GlassCard>
      
      {/* Documents Table */}
      <GlassCard className="mb-6 overflow-hidden transform transition hover:translate-y-[-2px]" opacity="high">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-indigo-100/30">
            <thead className="bg-indigo-50/30">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Document
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  User
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Document Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Uploaded
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-indigo-100/20">
              {paginatedDocuments.map((doc) => (
                <tr key={doc.id} className="hover:bg-indigo-50/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getDocumentIcon(doc.mimetype)}
                      <div className="ml-2">
                        <div className="text-sm font-medium text-indigo-800 truncate max-w-[200px]">
                          {doc.originalName}
                        </div>
                        <div className="text-xs text-gray-600">{formatFileSize(doc.size)}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-indigo-800">{doc.userName}</div>
                    <div className="text-xs text-gray-600">ID: {doc.userId}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100/70 backdrop-blur-sm text-indigo-800">
                      {doc.documentType.charAt(0).toUpperCase() + doc.documentType.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {formatDate(doc.uploadedAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(doc.status)}
                      <span className={`ml-1.5 text-xs font-medium ${
                        doc.status === "APPROVED" ? "text-green-700" : 
                        doc.status === "REJECTED" ? "text-red-700" : 
                        "text-blue-700"
                      }`}>
                        {doc.status.charAt(0) + doc.status.slice(1).toLowerCase()}
                      </span>
                    </div>
                    {doc.reviewNotes && (
                      <div className="mt-1 text-xs text-gray-600 max-w-[200px] truncate" title={doc.reviewNotes}>
                        {doc.reviewNotes}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button 
                        className="p-1 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded transition-colors"
                        onClick={() => window.open(doc.path, "_blank")}
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {doc.status === "SUBMITTED" && (
                        <>
                          <button 
                            className="p-1 text-green-600 hover:text-green-900 bg-green-50/70 backdrop-blur-sm rounded transition-colors"
                            onClick={() => setSelectedDocument(doc.id)}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                          <button 
                            className="p-1 text-red-600 hover:text-red-900 bg-red-50/70 backdrop-blur-sm rounded transition-colors"
                            onClick={() => setSelectedDocument(doc.id)}
                          >
                            <XCircle className="h-4 w-4" />
                          </button>
                        </>
                      )}
                      <button className="p-1 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded transition-colors">
                        <Download className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        <div className="bg-indigo-50/30 px-4 py-3 flex items-center justify-between border-t border-indigo-100/20 sm:px-6">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-indigo-700">
                Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(startIndex + itemsPerPage, filteredDocuments.length)}
                </span>{' '}
                of <span className="font-medium">{filteredDocuments.length}</span> documents
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-indigo-200/60 bg-white/80 backdrop-blur-sm text-sm font-medium text-indigo-600 hover:bg-indigo-50/70 transition-colors ${
                    currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`relative inline-flex items-center px-4 py-2 border ${
                      currentPage === page
                        ? 'z-10 bg-indigo-100/70 border-indigo-500 text-indigo-700'
                        : 'border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-600 hover:bg-indigo-50/70'
                    } text-sm font-medium transition-colors`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-indigo-200/60 bg-white/80 backdrop-blur-sm text-sm font-medium text-indigo-600 hover:bg-indigo-50/70 transition-colors ${
                    currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </GlassCard>
      
      {/* Document Review Modal */}
      {selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
          <GlassCard className="w-full max-w-2xl" opacity="high">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-indigo-800">Document Review</h3>
                <button 
                  onClick={() => {
                    setSelectedDocument(null);
                    setReviewNotes("");
                  }}
                  className="text-indigo-400 hover:text-indigo-600 transition-colors"
                >
                  <XCircle className="h-5 w-5" />
                </button>
              </div>
              
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <FileText className="h-5 w-5 text-indigo-500 mr-2" />
                  <h4 className="font-medium text-indigo-800">
                    {documents.find(d => d.id === selectedDocument)?.originalName}
                  </h4>
                </div>
                <div className="bg-indigo-50/30 rounded-lg p-4 text-sm text-indigo-800 mb-4">
                  <div className="flex items-start gap-1 mb-2">
                    <Info className="h-4 w-4 text-indigo-500 mt-0.5 flex-shrink-0" />
                    <span>
                      The document preview would appear here. In a real implementation, this would display the actual document content for review. Please refer to the original document path for the actual content.
                    </span>
                  </div>
                  <div className="mt-2 text-xs text-gray-600">
                    Path: {documents.find(d => d.id === selectedDocument)?.path}
                  </div>
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-indigo-700 mb-1">
                  Review Notes
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-indigo-200/60 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Enter your review comments..."
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                ></textarea>
              </div>
              
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => {
                    setSelectedDocument(null);
                    setReviewNotes("");
                  }}
                  className="px-4 py-2 border border-indigo-200/60 bg-white/80 backdrop-blur-sm rounded-lg text-sm font-medium text-indigo-700 hover:bg-indigo-50/70 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleApproveDocument(selectedDocument)}
                  className="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-green-600 to-emerald-500 hover:from-green-700 hover:to-emerald-600 transition-colors"
                >
                  Approve
                </button>
                <button
                  onClick={() => handleRejectDocument(selectedDocument)}
                  className="px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-red-600 to-rose-500 hover:from-red-700 hover:to-rose-600 transition-colors"
                >
                  Reject
                </button>
              </div>
            </div>
          </GlassCard>
        </div>
      )}
    </div>
  );
} 