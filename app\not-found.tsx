import Link from "next/link";

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4">
      <div 
        className="rounded-xl p-8 backdrop-blur-lg shadow-lg max-w-md w-full transform transition-all"
        style={{
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(12px)",
          border: "1px solid rgba(255, 255, 255, 0.25)",
          boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
        }}
      >
        <div className="text-center">
          <h1 className="text-6xl font-bold text-indigo-800 mb-2">404</h1>
          <h2 className="text-xl font-semibold text-indigo-700 mb-6">Page Not Found</h2>
          <p className="text-gray-600 mb-8">
            Sorry, the page you are looking for doesn't exist or has been moved.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link 
              href="/" 
              className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-3 px-6 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
            >
              Return Home
            </Link>
            
            <Link 
              href="/contact" 
              className="border border-indigo-300 rounded-lg py-3 px-6 text-indigo-700 font-medium bg-white hover:bg-indigo-50 transition"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
      
      <div className="mt-12 text-center max-w-md">
        <h3 className="text-lg font-medium text-indigo-800 mb-3">Looking for something else?</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-4">
          <Link href="/" className="text-indigo-600 hover:underline">Home</Link>
          <span className="mx-2">•</span>
          <Link href={"/services" as any} className="text-indigo-600 hover:underline">Services</Link>
          <span className="mx-2">•</span>
          <Link href="/contact" className="text-indigo-600 hover:underline">Contact</Link>
          <Link href="/resources" className="text-indigo-600 hover:underline">Resources</Link>
          <Link href="/about" className="text-indigo-600 hover:underline">About Us</Link>
          <Link href="/login" className="text-indigo-600 hover:underline">Log In</Link>
          <Link href="/resources/faqs" className="text-indigo-600 hover:underline">FAQs</Link>
        </div>
      </div>
    </div>
  );
} 