import { NextResponse } from 'next/server';

// In a real application, you would use a database
// This is just a mock implementation for demonstration
const mockLeads: any[] = [
  { 
    id: 'lead1', 
    clientName: 'Global Tech Inc.', 
    contactName: '<PERSON>',
    contactEmail: '<EMAIL>',
    contactPhone: '+91 98765 43210',
    service: 'EB-1',
    date: '2023-12-10',
    status: 'new',
    priority: 'high',
    assignedTo: 'exec1',
    value: 250000,
    notes: 'Tech company seeking visas for 3 senior engineers',
    lastActivity: '2023-12-10T14:30:00',
    nextFollowUp: '2023-12-15',
    source: 'Website Contact Form'
  },
  { 
    id: 'lead2', 
    clientName: 'Johnson Family', 
    contactName: '<PERSON>',
    contactEmail: '<EMAIL>',
    contactPhone: '+91 92345 67890',
    service: 'Student Visa',
    date: '2023-12-08',
    status: 'contacted',
    priority: 'medium',
    assignedTo: 'exec2',
    value: 75000,
    notes: 'Family seeking student visas for twins applying to US universities',
    lastActivity: '2023-12-09T10:15:00',
    nextFollowUp: '2023-12-14',
    source: 'Website Contact Form'
  }
];

// GET endpoint to fetch all leads
export async function GET(request: Request) {
  try {
    // In a real application, you would fetch from database
    // You could also implement filtering, pagination, etc. here

    // Get the URL search parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    // If id parameter exists, return a single lead
    if (id) {
      const lead = mockLeads.find(l => l.id === id);
      if (!lead) {
        return NextResponse.json({ 
          success: false, 
          message: 'Lead not found' 
        }, { status: 404 });
      }
      return NextResponse.json({ success: true, lead });
    }

    // Otherwise return all leads
    return NextResponse.json({ success: true, leads: mockLeads });
    
  } catch (error) {
    console.error('Error fetching leads:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'An error occurred while fetching leads' 
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.name || !data.email || !data.mobile || !data.service || !data.message) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing required fields' 
      }, { status: 400 });
    }
    
    // Create a new lead with additional metadata
    const newLead = {
      id: `lead${Date.now()}`, // Generate a unique ID
      clientName: data.name,
      contactName: data.name,
      contactEmail: data.email,
      contactPhone: data.mobile,
      service: data.service,
      date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
      status: 'new',
      priority: data.priority || 'medium',
      assignedTo: null, // Unassigned initially
      value: estimateLeadValue(data.service), // Estimate potential value based on service
      notes: data.message,
      lastActivity: new Date().toISOString(),
      nextFollowUp: getNextFollowUpDate(data.priority),
      source: data.source || 'Website Contact Form'
    };
    
    // In a real application, you would save to database
    // For demo, we'll just add to our mock array
    mockLeads.push(newLead);
    
    console.log('New lead created:', newLead);
    
    // Integration point: You could trigger notifications to sales team here
    // Example: notifySalesTeam(newLead);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Lead created successfully',
      leadId: newLead.id,
      lead: newLead
    });
    
  } catch (error) {
    console.error('Error processing lead:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'An error occurred while processing your request' 
    }, { status: 500 });
  }
}

// PUT endpoint to update a lead
export async function PUT(request: Request) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.id) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing lead ID' 
      }, { status: 400 });
    }
    
    // Find the lead to update
    const leadIndex = mockLeads.findIndex(l => l.id === data.id);
    if (leadIndex === -1) {
      return NextResponse.json({ 
        success: false, 
        message: 'Lead not found' 
      }, { status: 404 });
    }
    
    // Update the lead
    const updatedLead = {
      ...mockLeads[leadIndex],
      ...data,
      lastActivity: new Date().toISOString()
    };
    
    mockLeads[leadIndex] = updatedLead;
    
    return NextResponse.json({ 
      success: true, 
      message: 'Lead updated successfully',
      lead: updatedLead
    });
    
  } catch (error) {
    console.error('Error updating lead:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'An error occurred while updating the lead' 
    }, { status: 500 });
  }
}

// DELETE endpoint to delete a lead
export async function DELETE(request: Request) {
  try {
    // Get the URL search parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing lead ID' 
      }, { status: 400 });
    }
    
    // Find the lead to delete
    const leadIndex = mockLeads.findIndex(l => l.id === id);
    if (leadIndex === -1) {
      return NextResponse.json({ 
        success: false, 
        message: 'Lead not found' 
      }, { status: 404 });
    }
    
    // Delete the lead
    mockLeads.splice(leadIndex, 1);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Lead deleted successfully'
    });
    
  } catch (error) {
    console.error('Error deleting lead:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'An error occurred while deleting the lead' 
    }, { status: 500 });
  }
}

// PATCH endpoint for quick status updates
export async function PATCH(request: Request) {
  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.id) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing lead ID' 
      }, { status: 400 });
    }
    
    // Find the lead to update
    const leadIndex = mockLeads.findIndex(l => l.id === data.id);
    if (leadIndex === -1) {
      return NextResponse.json({ 
        success: false, 
        message: 'Lead not found' 
      }, { status: 404 });
    }
    
    // Update only specified fields
    const updatedLead = {
      ...mockLeads[leadIndex],
      ...data,
      lastActivity: new Date().toISOString()
    };
    
    mockLeads[leadIndex] = updatedLead;
    
    return NextResponse.json({ 
      success: true, 
      message: 'Lead status updated successfully',
      lead: updatedLead
    });
    
  } catch (error) {
    console.error('Error updating lead status:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'An error occurred while updating the lead status' 
    }, { status: 500 });
  }
}

// Helper function to estimate lead value based on service type
function estimateLeadValue(service: string): number {
  // These would be based on your actual business metrics
  const serviceValues: Record<string, number> = {
    'EB-1': 250000,
    'O-1': 180000,
    'H-1B': 125000,
    'Student Visa': 75000,
    'EB-5': 500000,
    'J-1': 85000,
    'General Inquiry': 100000
  };
  
  return serviceValues[service] || 100000;
}

// Helper function to determine next follow-up date based on priority
function getNextFollowUpDate(priority: string): string {
  const today = new Date();
  
  switch(priority) {
    case 'high':
      // High priority: follow up in 1 day
      today.setDate(today.getDate() + 1);
      break;
    case 'medium':
      // Medium priority: follow up in 3 days
      today.setDate(today.getDate() + 3);
      break;
    case 'low':
    default:
      // Low priority: follow up in 7 days
      today.setDate(today.getDate() + 7);
  }
  
  return today.toISOString().split('T')[0];
} 