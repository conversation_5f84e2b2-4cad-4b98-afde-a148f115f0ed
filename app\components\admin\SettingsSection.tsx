"use client";

import React from "react";

interface SettingsSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

const SettingsSection = ({ title, description, children, className = "" }: SettingsSectionProps) => (
  <div className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6 ${className}`}>
    <div className="p-6">
      <h2 className="text-lg font-semibold mb-1">{title}</h2>
      {description && <p className="text-sm text-gray-500 mb-4">{description}</p>}
      <div className="space-y-2">
        {children}
      </div>
    </div>
  </div>
);

export default SettingsSection; 