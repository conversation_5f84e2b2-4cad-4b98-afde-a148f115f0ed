"use client";

function GlassCard({ children, className = "" }: { children: React.ReactNode; className?: string }) {
  return (
    <div
      className={`rounded-2xl p-6 m-2 shadow-lg ${className}`}
      style={{
        background: "linear-gradient(135deg, #f5f7fa55 0%, #c3cfe255 100%)",
        backdropFilter: "blur(12px)",
        boxShadow: "8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff",
        border: "1px solid rgba(255,255,255,0.15)",
      }}
    >
      {children}
    </div>
  );
}

export default function SchedulePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e6eeff] to-[#f5f7ff] py-12 px-4 flex flex-col items-center justify-center">
      <GlassCard className="max-w-xl w-full">
        <h1 className="text-2xl font-bold mb-4 text-center">Schedule Appointment</h1>
        <div className="h-64 flex items-center justify-center text-gray-500">[Calendly Widget Placeholder]</div>
      </GlassCard>
    </div>
  );
} 