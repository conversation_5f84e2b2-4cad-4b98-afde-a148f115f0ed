"use client";

import React from "react";
import { AlertCircle } from "lucide-react";

interface SuccessMessageProps {
  message: string;
}

const SuccessMessage = ({ message }: SuccessMessageProps) => (
  <div className="mb-6 bg-green-50 border border-green-200 text-green-800 rounded-lg p-4 flex items-center">
    <AlertCircle className="h-5 w-5 mr-2" />
    <span>{message}</span>
  </div>
);

export default SuccessMessage; 