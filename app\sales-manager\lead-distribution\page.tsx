"use client";

import { useState } from "react";
import { 
  Upload, 
  FileSpreadsheet, 
  Filter, 
  SlidersHorizontal, 
  RefreshCcw, 
  ArrowRightLeft,
  PlusCircle
} from "lucide-react";
import { LeadDistribution } from "../components/lead-distribution";

// Mock data
const executives = [
  { id: 'exec1', name: '<PERSON><PERSON>', avatar: '', metrics: { conversions: 42, revenue: '$128k', satisfaction: 4.7 }, status: 'exceeding', lastActivity: '2h ago', workload: 12 },
  { id: 'exec2', name: '<PERSON><PERSON>', avatar: '', metrics: { conversions: 35, revenue: '$105k', satisfaction: 4.5 }, status: 'meeting', lastActivity: '1h ago', workload: 15 },
  { id: 'exec3', name: '<PERSON><PERSON>', avatar: '', metrics: { conversions: 38, revenue: '$116k', satisfaction: 4.6 }, status: 'meeting', lastActivity: '30m ago', workload: 17 },
  { id: 'exec4', name: '<PERSON><PERSON><PERSON>', avatar: '', metrics: { conversions: 44, revenue: '$132k', satisfaction: 4.8 }, status: 'exceeding', lastActivity: '15m ago', workload: 11 },
];

const pendingLeads = [
  { id: 'lead1', name: 'Global Tech Inc.', service: 'EB-1', priority: 'high', status: 'new' },
  { id: 'lead2', name: 'Johnson Family', service: 'Student Visa', priority: 'medium', status: 'new' },
  { id: 'lead3', name: 'Sarah Williams', service: 'O-1', priority: 'high', status: 'new' },
  { id: 'lead4', name: 'Micron Solutions', service: 'H-1B', priority: 'medium', status: 'new' },
  { id: 'lead5', name: 'Tech Masters LLC', service: 'EB-1', priority: 'high', status: 'new' },
  { id: 'lead6', name: 'Creative Studios', service: 'O-1', priority: 'medium', status: 'new' },
  { id: 'lead7', name: 'Global Finance', service: 'H-1B', priority: 'high', status: 'new' },
  { id: 'lead8', name: 'University Group', service: 'Student Visa', priority: 'medium', status: 'new' },
];

export default function LeadDistributionPage() {
  const [distributionStrategy, setDistributionStrategy] = useState("balanced");
  const [filterCategory, setFilterCategory] = useState("all");

  // Filter leads
  const filteredLeads = filterCategory === "all" 
    ? pendingLeads
    : pendingLeads.filter(lead => 
        filterCategory === "priority" 
          ? lead.priority === "high" 
          : lead.service === filterCategory
      );

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">Lead Distribution</h1>
          <p className="text-gray-500 mt-1">Allocate leads to your sales executives efficiently</p>
        </div>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-1 bg-white text-gray-700 px-3 py-2 rounded-lg border border-gray-300 hover:bg-gray-50">
            <Upload size={16} />
            <span>Import</span>
          </button>
          <button className="flex items-center gap-1 bg-white text-gray-700 px-3 py-2 rounded-lg border border-gray-300 hover:bg-gray-50">
            <FileSpreadsheet size={16} />
            <span>Export</span>
          </button>
          <button className="flex items-center gap-1 bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700">
            <PlusCircle size={16} />
            <span>Add Lead</span>
          </button>
        </div>
      </div>

      {/* Control panel */}
      <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-medium text-gray-800">Distribution Control Panel</h2>
          <button className="flex items-center gap-2 text-indigo-600 hover:text-indigo-800 font-medium">
            <RefreshCcw size={16} />
            <span>Reset All Assignments</span>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Distribution Strategy
            </label>
            <select 
              className="w-full bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={distributionStrategy}
              onChange={(e) => setDistributionStrategy(e.target.value)}
            >
              <option value="balanced">Balanced (Even Workload)</option>
              <option value="skill-based">Skill-Based (Expertise Match)</option>
              <option value="priority">Priority-Based (Performance)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Filter Leads
            </label>
            <select 
              className="w-full bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
            >
              <option value="all">All Leads</option>
              <option value="EB-1">EB-1 Visas</option>
              <option value="O-1">O-1 Visas</option>
              <option value="H-1B">H-1B Visas</option>
              <option value="Student Visa">Student Visas</option>
              <option value="priority">High Priority</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quick Actions
            </label>
            <div className="flex gap-2">
              <button className="flex-1 flex items-center justify-center gap-1 bg-indigo-50 text-indigo-700 py-2 px-3 rounded-lg hover:bg-indigo-100">
                <SlidersHorizontal size={16} />
                <span>Auto Assign</span>
              </button>
              <button className="flex-1 flex items-center justify-center gap-1 bg-blue-50 text-blue-700 py-2 px-3 rounded-lg hover:bg-blue-100">
                <ArrowRightLeft size={16} />
                <span>Balance Load</span>
              </button>
            </div>
          </div>
        </div>

        <LeadDistribution 
          executives={executives} 
          leads={filteredLeads}
          strategy={distributionStrategy}
          rules={{
            maxPerExecutive: 15,
            serviceSpecialization: {
              'EB-1': ['exec1', 'exec3'],
              'O-1': ['exec2', 'exec4'],
              'H-1B': ['exec1', 'exec4'],
              'Student Visa': ['exec2', 'exec3']
            }
          }}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Lead Status</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg text-center">
              <p className="text-sm text-gray-500">Unassigned Leads</p>
              <p className="text-2xl font-bold text-indigo-600">{pendingLeads.length - 4}</p>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg text-center">
              <p className="text-sm text-gray-500">Assigned Leads</p>
              <p className="text-2xl font-bold text-green-600">4</p>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg text-center">
              <p className="text-sm text-gray-500">High Priority</p>
              <p className="text-2xl font-bold text-amber-600">4</p>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg text-center">
              <p className="text-sm text-gray-500">Corporate Leads</p>
              <p className="text-2xl font-bold text-blue-600">3</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Service Distribution</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>EB-1</span>
                <span className="font-medium">2 leads (25%)</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2">
                <div className="h-2 rounded-full bg-indigo-600" style={{ width: '25%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>O-1</span>
                <span className="font-medium">2 leads (25%)</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2">
                <div className="h-2 rounded-full bg-blue-600" style={{ width: '25%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>H-1B</span>
                <span className="font-medium">2 leads (25%)</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2">
                <div className="h-2 rounded-full bg-purple-600" style={{ width: '25%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Student Visa</span>
                <span className="font-medium">2 leads (25%)</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2">
                <div className="h-2 rounded-full bg-green-600" style={{ width: '25%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 