'use client';

import { useState, useEffect } from 'react';
import { X, Phone, Mail, ChevronRight, User, Calendar, Clock, Edit, Trash2 } from 'lucide-react';

interface LeadDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  leadId: string | null;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

interface Lead {
  id: string;
  clientName: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  service: string;
  date: string;
  status: string;
  priority: string;
  assignedTo: string | null;
  value: number;
  notes: string;
  lastActivity: string;
  nextFollowUp: string | null;
  source: string;
}

export default function LeadDetailsModal({ isOpen, onClose, leadId, onEdit, onDelete }: LeadDetailsModalProps) {
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && leadId) {
      fetchLeadDetails(leadId);
    }
  }, [isOpen, leadId]);

  const fetchLeadDetails = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/leads?id=${id}`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch lead details');
      }
      
      setLead(data.lead);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      console.error('Error fetching lead details:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-blue-600 p-6 text-white flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold truncate">
              {loading ? 'Loading...' : error ? 'Error' : lead?.clientName}
            </h2>
            <p className="text-indigo-100 text-sm truncate">
              {loading ? '' : error ? error : lead?.service}
            </p>
          </div>
          <div className="flex items-center gap-4">
            {lead && (
              <>
                <button 
                  onClick={() => onEdit(lead.id)}
                  className="text-white/80 hover:text-white transition-colors"
                  title="Edit Lead"
                >
                  <Edit size={18} />
                </button>
                <button 
                  onClick={() => onDelete(lead.id)}
                  className="text-white/80 hover:text-white transition-colors"
                  title="Delete Lead"
                >
                  <Trash2 size={18} />
                </button>
              </>
            )}
            <button 
              onClick={onClose}
              className="text-white/80 hover:text-white transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-500 mb-2">Failed to load lead details</div>
              <button 
                onClick={() => leadId && fetchLeadDetails(leadId)}
                className="text-indigo-600 hover:text-indigo-800"
              >
                Try Again
              </button>
            </div>
          ) : lead ? (
            <div className="space-y-6">
              {/* Status and Priority */}
              <div className="flex flex-wrap gap-3">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  lead.status === 'new' ? 'bg-blue-100 text-blue-800' :
                  lead.status === 'contacted' ? 'bg-indigo-100 text-indigo-800' :
                  lead.status === 'qualified' ? 'bg-purple-100 text-purple-800' :
                  lead.status === 'proposal' ? 'bg-amber-100 text-amber-800' :
                  lead.status === 'negotiation' ? 'bg-orange-100 text-orange-800' :
                  lead.status === 'closed' ? 'bg-green-100 text-green-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {lead.status.charAt(0).toUpperCase() + lead.status.slice(1)}
                </span>
                
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  lead.priority === 'high' ? 'bg-red-100 text-red-800' :
                  lead.priority === 'medium' ? 'bg-amber-100 text-amber-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {lead.priority.charAt(0).toUpperCase() + lead.priority.slice(1)} Priority
                </span>
                
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                  ₹{lead.value.toLocaleString('en-IN')}
                </span>
              </div>
              
              {/* Contact Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border-t border-b border-gray-100 py-4">
                <div className="flex items-center gap-3">
                  <User className="text-gray-400" size={18} />
                  <div>
                    <div className="text-sm text-gray-500">Contact</div>
                    <div className="font-medium">{lead.contactName}</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="text-gray-400" size={18} />
                  <div>
                    <div className="text-sm text-gray-500">Email</div>
                    <a href={`mailto:${lead.contactEmail}`} className="font-medium text-indigo-600 hover:text-indigo-800">
                      {lead.contactEmail}
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="text-gray-400" size={18} />
                  <div>
                    <div className="text-sm text-gray-500">Phone</div>
                    <a href={`tel:${lead.contactPhone}`} className="font-medium text-indigo-600 hover:text-indigo-800">
                      {lead.contactPhone}
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="text-gray-400" size={18} />
                  <div>
                    <div className="text-sm text-gray-500">Created On</div>
                    <div className="font-medium">
                      {new Date(lead.date).toLocaleDateString('en-IN', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric'
                      })}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Notes */}
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Notes</h3>
                <div className="bg-gray-50 p-4 rounded-lg text-gray-700 whitespace-pre-wrap">
                  {lead.notes || 'No notes provided'}
                </div>
              </div>
              
              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Clock className="text-gray-400" size={18} />
                  <div>
                    <div className="text-sm text-gray-500">Last Activity</div>
                    <div className="font-medium">
                      {new Date(lead.lastActivity).toLocaleString('en-IN', {
                        day: 'numeric',
                        month: 'short',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                </div>
                {lead.nextFollowUp && (
                  <div className="flex items-center gap-3">
                    <Calendar className="text-gray-400" size={18} />
                    <div>
                      <div className="text-sm text-gray-500">Next Follow-up</div>
                      <div className="font-medium">
                        {new Date(lead.nextFollowUp).toLocaleDateString('en-IN', {
                          day: 'numeric',
                          month: 'short',
                          year: 'numeric'
                        })}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Source */}
              <div className="pt-2 border-t border-gray-100">
                <div className="text-sm text-gray-500 flex items-center">
                  <span>Source:</span>
                  <span className="ml-1 text-gray-700">{lead.source}</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              No lead selected
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 