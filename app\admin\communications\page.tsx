"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import {
  Search,
  Users,
  MessageSquare,
  ChevronDown,
  Filter,
  RefreshCw,
  Clock,
  CheckCircle,
  AlertCircle,
  Send,
  Paperclip,
  UserPlus,
  UserCheck,
  FileText,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  X,
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock data
const mockUsers = [
  {
    id: "u-1",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    avatar: "JS",
    unreadCount: 2,
    lastMessage: "I have a question about my visa application",
    lastMessageTime: "2023-09-28T14:25:00Z",
    assignedAgent: "a-2",
  },
  {
    id: "u-2",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    avatar: "<PERSON><PERSON>",
    unreadCount: 0,
    lastMessage: "Thank you for your help",
    lastMessageTime: "2023-09-30T09:10:00Z",
    assignedAgent: "a-1",
  },
  {
    id: "u-3",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "pending",
    avatar: "RC",
    unreadCount: 5,
    lastMessage: "When will my documents be reviewed?",
    lastMessageTime: "2023-09-20T16:30:00Z",
    assignedAgent: null,
  },
  {
    id: "u-4",
    name: "Sophia Rodriguez",
    email: "<EMAIL>",
    status: "active",
    avatar: "SR",
    unreadCount: 0,
    lastMessage: "I've uploaded the requested documents",
    lastMessageTime: "2023-09-29T11:45:00Z",
    assignedAgent: "a-3",
  },
  {
    id: "u-5",
    name: "Daniel Wilson",
    email: "<EMAIL>",
    status: "suspended",
    avatar: "DW",
    unreadCount: 1,
    lastMessage: "Can I get an update on my application status?",
    lastMessageTime: "2023-09-10T10:15:00Z",
    assignedAgent: null,
  },
];

const mockAgents = [
  { id: "a-1", name: "Sarah Johnson", role: "Senior Visa Consultant", status: "active", avatar: "SJ" },
  { id: "a-2", name: "Michael Chen", role: "Visa Specialist", status: "active", avatar: "MC" },
  { id: "a-3", name: "Emma Thompson", role: "Document Verification Agent", status: "active", avatar: "ET" },
  { id: "admin", name: "Admin User", role: "System Administrator", status: "active", avatar: "AU" },
];

const mockMessages = [
  {
    id: "m-1",
    userId: "u-1",
    sender: "user" as const,
    text: "Hello, I have a question about my visa application",
    timestamp: "2023-09-28T14:25:00Z",
    read: true,
  },
  {
    id: "m-2",
    userId: "u-1",
    sender: "agent" as const,
    agentId: "a-2",
    text: "Hi John, I'd be happy to assist with your visa application. What specific questions do you have?",
    timestamp: "2023-09-28T14:30:00Z",
    read: true,
  },
  {
    id: "m-3",
    userId: "u-1",
    sender: "user" as const,
    text: "I'm not sure if I submitted all the required documents. Is there a way to check?",
    timestamp: "2023-09-28T14:35:00Z",
    read: true,
  },
  {
    id: "m-4",
    userId: "u-1",
    sender: "agent" as const,
    agentId: "a-2",
    text: "Yes, you can check your document status on the Documents page. I can see that you're missing your financial statements. Would you like me to guide you through the upload process?",
    timestamp: "2023-09-28T14:40:00Z",
    read: true,
  },
  {
    id: "m-5",
    userId: "u-1",
    sender: "user" as const,
    text: "That would be very helpful. I have my bank statements ready, but I'm not sure which format is acceptable.",
    timestamp: "2023-09-29T09:15:00Z",
    read: false,
  },
  {
    id: "m-6",
    userId: "u-1",
    sender: "user" as const,
    text: "Also, how long does the verification process usually take?",
    timestamp: "2023-09-29T09:16:00Z",
    read: false,
  },
  // Messages for user 2
  {
    id: "m-7",
    userId: "u-2",
    sender: "user" as const,
    text: "I've completed my visa application and submitted all documents",
    timestamp: "2023-09-29T10:05:00Z",
    read: true,
  },
  {
    id: "m-8",
    userId: "u-2",
    sender: "agent" as const,
    agentId: "a-1",
    text: "Thank you for submitting your application, Maria. I'll review your documents and get back to you shortly.",
    timestamp: "2023-09-29T10:15:00Z",
    read: true,
  },
  {
    id: "m-9",
    userId: "u-2",
    sender: "agent" as const,
    agentId: "a-1",
    text: "I've reviewed your documents and everything looks good. Your application is now in the processing stage.",
    timestamp: "2023-09-30T08:45:00Z",
    read: true,
  },
  {
    id: "m-10",
    userId: "u-2",
    sender: "user" as const,
    text: "Thank you for your help",
    timestamp: "2023-09-30T09:10:00Z",
    read: true,
  },
  // Messages for user 3
  {
    id: "m-11",
    userId: "u-3",
    sender: "user" as const,
    text: "Hello, I submitted my documents last week but haven't heard back",
    timestamp: "2023-09-18T11:20:00Z",
    read: true,
  },
  {
    id: "m-12",
    userId: "u-3",
    sender: "system" as const,
    text: "Your message has been received. An agent will be assigned to you shortly.",
    timestamp: "2023-09-18T11:21:00Z",
    read: true,
  },
  {
    id: "m-13",
    userId: "u-3",
    sender: "user" as const,
    text: "It's been two days and I still don't have an agent assigned",
    timestamp: "2023-09-20T16:25:00Z",
    read: true,
  },
  {
    id: "m-14",
    userId: "u-3",
    sender: "user" as const,
    text: "When will my documents be reviewed?",
    timestamp: "2023-09-20T16:30:00Z",
    read: true,
  },
  {
    id: "m-15",
    userId: "u-3",
    sender: "user" as const,
    text: "Hello? Is anyone there?",
    timestamp: "2023-09-21T10:15:00Z",
    read: false,
  },
  {
    id: "m-16",
    userId: "u-3",
    sender: "user" as const,
    text: "I need help with my application",
    timestamp: "2023-09-21T14:30:00Z",
    read: false,
  },
  {
    id: "m-17",
    userId: "u-3",
    sender: "user" as const,
    text: "Please respond to my messages",
    timestamp: "2023-09-22T09:45:00Z",
    read: false,
  },
  {
    id: "m-18",
    userId: "u-3",
    sender: "user" as const,
    text: "This is very frustrating",
    timestamp: "2023-09-23T11:10:00Z",
    read: false,
  },
  {
    id: "m-19",
    userId: "u-3",
    sender: "user" as const,
    text: "I'm still waiting for a response",
    timestamp: "2023-09-24T16:05:00Z",
    read: false,
  },
];

// Interface for message data
interface Message {
  id: string;
  userId: string;
  sender: "user" | "agent" | "system" | "admin";
  agentId?: string;
  text: string;
  timestamp: string;
  read: boolean;
  attachments?: string[];
}

// Interface for user data
interface User {
  id: string;
  name: string;
  email: string;
  status: string;
  avatar: string;
  unreadCount: number;
  lastMessage: string;
  lastMessageTime: string;
  assignedAgent: string | null;
}

// Interface for agent data
interface Agent {
  id: string;
  name: string;
  role: string;
  status: string;
  avatar: string;
}

export default function CommunicationsPage() {
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [agents, setAgents] = useState<Agent[]>(mockAgents);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterAssignment, setFilterAssignment] = useState("all");
  const [hasUnread, setHasUnread] = useState(true);
  const [assignmentModalOpen, setAssignmentModalOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load messages for selected user
  useEffect(() => {
    if (selectedUser) {
      const userMessages = mockMessages.filter(msg => msg.userId === selectedUser);
      setMessages(userMessages);
      
      // Mark messages as read when selected
      const updatedUsers = users.map(user => {
        if (user.id === selectedUser) {
          return { ...user, unreadCount: 0 };
        }
        return user;
      });
      setUsers(updatedUsers);
    }
  }, [selectedUser]);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || user.status === filterStatus;
    
    const matchesAssignment = filterAssignment === 'all' || 
                            (filterAssignment === 'assigned' && user.assignedAgent) ||
                            (filterAssignment === 'unassigned' && !user.assignedAgent);
    
    const matchesUnread = !hasUnread || user.unreadCount > 0;
    
    return matchesSearch && matchesStatus && matchesAssignment && matchesUnread;
  });

  // Function to send message
  const handleSendMessage = () => {
    if (input.trim() && selectedUser) {
      const newMessage: Message = {
        id: `new-${Date.now()}`,
        userId: selectedUser,
        sender: "admin",
        agentId: "admin",
        text: input.trim(),
        timestamp: new Date().toISOString(),
        read: true,
      };
      
      setMessages([...messages, newMessage]);
      setInput("");
      
      // Update last message for user
      const updatedUsers = users.map(user => {
        if (user.id === selectedUser) {
          return {
            ...user,
            lastMessage: input.trim(),
            lastMessageTime: new Date().toISOString(),
          };
        }
        return user;
      });
      setUsers(updatedUsers);
    }
  };

  // Function to assign agent to user
  const assignAgent = (userId: string, agentId: string) => {
    const updatedUsers = users.map(user => {
      if (user.id === userId) {
        return {
          ...user,
          assignedAgent: agentId,
        };
      }
      return user;
    });
    setUsers(updatedUsers);
    setAssignmentModalOpen(false);
    
    // Add system message about assignment
    const agent = agents.find(a => a.id === agentId);
    if (agent) {
      const newMessage: Message = {
        id: `new-${Date.now()}`,
        userId: userId,
        sender: "system",
        text: `${agent.name} has been assigned to this conversation.`,
        timestamp: new Date().toISOString(),
        read: true,
      };
      setMessages([...messages, newMessage]);
    }
  };

  // Format timestamp to readable time
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format timestamp to readable date
  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  };

  // Group messages by date
  const messagesByDate: { [key: string]: Message[] } = {};
  messages.forEach(message => {
    const date = formatDate(message.timestamp);
    if (!messagesByDate[date]) {
      messagesByDate[date] = [];
    }
    messagesByDate[date].push(message);
  });

  // Get selected user info
  const selectedUserInfo = selectedUser ? users.find(u => u.id === selectedUser) : null;
  
  // Get assigned agent info for selected user
  const assignedAgentInfo = selectedUserInfo?.assignedAgent 
    ? agents.find(a => a.id === selectedUserInfo.assignedAgent) 
    : null;

  return (
    <div className="h-full flex flex-col">
      <div className="mb-6">
        <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
          Communications
        </h1>
        <p className="text-gray-600 mt-1">Manage user communications and support tickets</p>
      </div>
      
      <div className="flex-grow flex flex-col md:flex-row gap-6">
        {/* Users List */}
        <GlassCard className="w-full md:w-1/3 flex flex-col overflow-hidden transform transition hover:translate-y-[-2px]">
          <div className="p-4 border-b border-indigo-100/20">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-indigo-400" />
              </div>
              <input
                type="text"
                placeholder="Search users..."
                className="pl-10 pr-3 py-2 w-full border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex-grow overflow-y-auto">
            <div className="divide-y divide-indigo-100/20">
              {filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className={`p-4 flex items-center gap-3 cursor-pointer transition-colors ${
                    selectedUser === user.id 
                      ? "bg-indigo-50/50 border-l-4 border-indigo-500" 
                      : "hover:bg-indigo-50/20"
                  }`}
                  onClick={() => setSelectedUser(user.id)}
                >
                  <div className="relative">
                    <div className={`w-10 h-10 rounded-full bg-gradient-to-br from-indigo-400 to-blue-300 flex items-center justify-center text-white font-medium`}>
                      {user.avatar}
                    </div>
                    {user.status === "active" && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                    )}
                    {user.unreadCount > 0 && (
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full">
                        {user.unreadCount}
                      </div>
                    )}
                  </div>
                  <div className="flex-grow min-w-0">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium text-indigo-800 truncate">{user.name}</h3>
                      <span className="text-xs text-gray-600">
                        {new Date(user.lastMessageTime).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="text-sm truncate text-gray-600">{user.lastMessage}</div>
                  </div>
                  {user.assignedAgent && (
                    <div className="bg-indigo-100/60 backdrop-blur-sm text-indigo-700 text-xs px-2 py-1 rounded-full flex items-center whitespace-nowrap">
                      <UserCheck className="h-3 w-3 mr-1" />
                      {agents.find(a => a.id === user.assignedAgent)?.name.split(' ')[0]}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </GlassCard>
        
        {/* Chat Area */}
        {selectedUser ? (
          <GlassCard className="w-full md:w-2/3 flex flex-col overflow-hidden transform transition hover:translate-y-[-2px]" opacity="high">
            {(() => {
              const user = users.find(u => u.id === selectedUser);
              if (!user) return null;
              
              return (
                <>
                  <div className="p-4 border-b border-indigo-100/20 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 rounded-full bg-gradient-to-br from-indigo-400 to-blue-300 flex items-center justify-center text-white font-medium`}>
                        {user.avatar}
                      </div>
                      <div>
                        <h3 className="font-medium text-indigo-800">{user.name}</h3>
                        <p className="text-xs text-gray-600">{user.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {!user.assignedAgent && (
                        <button
                          onClick={() => setAssignmentModalOpen(true)}
                          className="flex items-center py-1 px-2 bg-indigo-100/60 text-indigo-700 text-sm rounded-lg hover:bg-indigo-200/60 transition-colors"
                        >
                          <UserPlus className="h-4 w-4 mr-1" />
                          Assign
                        </button>
                      )}
                      <Link
                        href={`/admin/users?user=${user.id}`}
                        className="flex items-center py-1 px-2 bg-indigo-50/60 text-indigo-700 text-sm rounded-lg hover:bg-indigo-100/60 transition-colors"
                      >
                        <Users className="h-4 w-4 mr-1" />
                        Profile
                      </Link>
                      <button className="text-indigo-400 hover:text-indigo-700 transition-colors">
                        <MoreHorizontal className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex-grow overflow-y-auto p-4">
                    {user.unreadCount === 0 ? (
                      <div className="h-full flex items-center justify-center">
                        <div className="text-center">
                          <MessageSquare className="h-12 w-12 text-indigo-300 mx-auto mb-2" />
                          <p className="text-indigo-800 font-medium">No messages yet</p>
                          <p className="text-sm text-gray-600">Start a conversation with {user.name}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {(() => {
                          const messagesByDate: { [date: string]: Message[] } = {};
                          
                          messages.forEach(msg => {
                            const date = formatDate(msg.timestamp);
                            if (!messagesByDate[date]) messagesByDate[date] = [];
                            messagesByDate[date].push(msg);
                          });
                          
                          return Object.entries(messagesByDate).map(([date, msgs]) => (
                            <div key={date} className="space-y-4">
                              <div className="flex items-center justify-center">
                                <div className="bg-indigo-100/60 backdrop-blur-sm text-indigo-700 text-xs px-3 py-1 rounded-full">
                                  {date}
                                </div>
                              </div>
                              
                              {msgs.map(message => (
                                <div 
                                  key={message.id} 
                                  className={`flex ${message.sender === 'user' ? 'justify-start' : message.sender === 'system' ? 'justify-center' : 'justify-end'}`}
                                >
                                  {message.sender === 'system' ? (
                                    <div className="bg-indigo-50/60 backdrop-blur-sm text-indigo-700 text-xs px-3 py-1 rounded-full max-w-md">
                                      {message.text}
                                    </div>
                                  ) : (
                                    <div 
                                      className={`max-w-md px-4 py-2 rounded-lg ${
                                        message.sender === 'user' 
                                          ? 'bg-white/80 backdrop-blur-sm text-indigo-800 border border-indigo-100/40' 
                                          : 'bg-gradient-to-r from-blue-600 to-indigo-700 text-white'
                                      }`}
                                    >
                                      <div className="text-sm">{message.text}</div>
                                      <div className="text-xs mt-1 flex justify-end items-center gap-1 opacity-80">
                                        {formatTimestamp(message.timestamp)}
                                        {message.sender !== 'user' && message.read && (
                                          <CheckCircle className="h-3 w-3" />
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          ));
                        })()}
                        
                        <div ref={messagesEndRef} />
                      </div>
                    )}
                  </div>
                  
                  <div className="p-4 border-t border-indigo-100/20">
                    <div className="flex items-end gap-2">
                      <div className="flex-grow bg-white/80 backdrop-blur-sm border border-indigo-200/60 rounded-lg overflow-hidden">
                        <textarea
                          rows={1}
                          className="w-full px-3 py-2 text-sm resize-none focus:outline-none text-indigo-800"
                          placeholder="Type your message..."
                          value={input}
                          onChange={(e) => setInput(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSendMessage();
                            }
                          }}
                        ></textarea>
                        <div className="px-2 py-1 border-t border-indigo-50/60 flex justify-between items-center">
                          <button className="p-1 text-indigo-500 hover:text-indigo-700 transition-colors">
                            <Paperclip className="h-4 w-4" />
                          </button>
                          <div className="text-xs text-gray-600">
                            Press Enter to send
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={handleSendMessage}
                        disabled={input.trim() === ""}
                        className="p-3 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-full hover:from-blue-700 hover:to-indigo-800 transition-colors disabled:opacity-50"
                      >
                        <Send className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  
                  {assignmentModalOpen && (
                    <div className="absolute inset-0 bg-indigo-900/10 backdrop-blur-sm flex items-center justify-center z-10">
                      <GlassCard className="w-full max-w-md" opacity="high">
                        <div className="p-6">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold text-indigo-800">Assign Agent</h3>
                            <button 
                              onClick={() => setAssignmentModalOpen(false)} 
                              className="text-indigo-400 hover:text-indigo-600 transition-colors"
                            >
                              <X className="h-5 w-5" />
                            </button>
                          </div>
                          
                          <div className="space-y-3 mb-4">
                            {agents.map(agent => (
                              <div
                                key={agent.id}
                                onClick={() => assignAgent(selectedUser, agent.id)}
                                className="p-3 flex items-center gap-3 bg-indigo-50/50 rounded-lg cursor-pointer hover:bg-indigo-100/50 transition-colors"
                              >
                                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-400 to-blue-300 flex items-center justify-center text-white font-medium">
                                  {agent.avatar}
                                </div>
                                <div>
                                  <div className="font-medium text-indigo-800">{agent.name}</div>
                                  <div className="text-xs text-gray-600">{agent.role}</div>
                                </div>
                                <div className={`ml-auto rounded-full h-2.5 w-2.5 ${
                                  agent.status === "active" ? "bg-green-500" : "bg-gray-300"
                                }`}></div>
                              </div>
                            ))}
                          </div>
                          
                          <div className="flex justify-end">
                            <button
                              onClick={() => setAssignmentModalOpen(false)}
                              className="px-4 py-2 border border-indigo-200/60 rounded-lg text-indigo-700 bg-white/80 backdrop-blur-sm text-sm hover:bg-indigo-50/70 transition-colors"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      </GlassCard>
                    </div>
                  )}
                </>
              );
            })()}
          </GlassCard>
        ) : (
          <GlassCard className="w-full md:w-2/3 flex items-center justify-center p-10 transform transition hover:translate-y-[-2px]" opacity="high">
            <div className="text-center">
              <div className="bg-indigo-100/70 backdrop-blur-sm w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4">
                <MessageSquare className="h-8 w-8 text-indigo-500" />
              </div>
              <h3 className="text-lg font-semibold text-indigo-800 mb-2">Select a conversation</h3>
              <p className="text-gray-600 max-w-sm mx-auto">
                Choose a user from the list to view their messages and start communicating
              </p>
            </div>
          </GlassCard>
        )}
      </div>
    </div>
  );
} 