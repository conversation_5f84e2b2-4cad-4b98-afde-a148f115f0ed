<!DOCTYPE html>
<html>
<head>
    <title>Profile API Test</title>
</head>
<body>
    <h1>Profile API Test</h1>
    <div id="results"></div>
    
    <script>
        async function testProfileAPI() {
            const results = document.getElementById('results');
            
            try {
                // Test session
                const sessionResponse = await fetch('/api/auth/session');
                const sessionData = await sessionResponse.json();
                results.innerHTML += '<h3>Session:</h3><pre>' + JSON.stringify(sessionData, null, 2) + '</pre>';
                
                // Test user API
                const userResponse = await fetch('/api/user');
                const userData = await userResponse.json();
                results.innerHTML += '<h3>User API:</h3><pre>' + JSON.stringify(userData, null, 2) + '</pre>';
                
                // Test profile API
                const profileResponse = await fetch('/api/user/profile');
                const profileData = await profileResponse.json();
                results.innerHTML += '<h3>Profile API:</h3><pre>' + JSON.stringify(profileData, null, 2) + '</pre>';
                
            } catch (error) {
                results.innerHTML += '<h3>Error:</h3><pre>' + error.toString() + '</pre>';
            }
        }
        
        // Run test when page loads
        testProfileAPI();
    </script>
</body>
</html> 