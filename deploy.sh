#!/bin/bash
set -e

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}Visa Mentor Automated Deployment Script${NC}"
echo -e "${GREEN}========================================${NC}"

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    echo -e "${RED}Error: .env.production file not found!${NC}"
    echo -e "${YELLOW}Creating sample .env.production file...${NC}"
    
    cat > .env.production << EOL
# Database
DATABASE_URL="mysql://user:password@host:port/visa_mentor"

# Authentication
NEXTAUTH_URL="https://your-production-domain.com"
NEXTAUTH_SECRET="$(openssl rand -hex 32)"

# API Keys (Add your service API keys below)
# STRIPE_SECRET_KEY=""
# SENDGRID_API_KEY=""
EOL
    
    echo -e "${YELLOW}Please update the .env.production file with your configuration values and run this script again.${NC}"
    exit 1
fi

echo -e "${GREEN}Step 1/6: Installing dependencies...${NC}"
npm ci

echo -e "${GREEN}Step 2/6: Running database migrations...${NC}"
npx prisma generate
npx prisma migrate deploy

echo -e "${GREEN}Step 3/6: Running linting and type checks...${NC}"
npm run lint
if [ $? -ne 0 ]; then
    echo -e "${RED}Linting failed! Please fix the issues and try again.${NC}"
    exit 1
fi

echo -e "${GREEN}Step 4/6: Building the application...${NC}"
npm run build
if [ $? -ne 0 ]; then
    echo -e "${RED}Build failed! Please fix the issues and try again.${NC}"
    exit 1
fi

echo -e "${GREEN}Step 5/6: Running post-build verifications...${NC}"
# Add custom verification steps here if needed

echo -e "${GREEN}Step 6/6: Preparing for deployment...${NC}"
mkdir -p dist
cp -r .next dist/
cp -r public dist/
cp package.json dist/
cp package-lock.json dist/
cp next.config.js dist/
cp .env.production dist/.env
cp -r prisma dist/

echo -e "${GREEN}==============================================${NC}"
echo -e "${GREEN}Build successful! Deployment package created.${NC}"
echo -e "${GREEN}==============================================${NC}"
echo -e "${YELLOW}To deploy manually, upload the dist/ folder to your hosting provider.${NC}"
echo -e "${YELLOW}For Vercel deployment, run: vercel --prod${NC}"
echo -e "${YELLOW}For automated CI/CD deployment, push to your main branch.${NC}"

# Detect hosting platform tools and offer direct deployment
if command -v vercel &> /dev/null; then
    echo -e "${YELLOW}Vercel CLI detected. Deploy now? (y/N)${NC}"
    read -n 1 -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        vercel --prod
    fi
elif command -v netlify &> /dev/null; then
    echo -e "${YELLOW}Netlify CLI detected. Deploy now? (y/N)${NC}"
    read -n 1 -r
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        netlify deploy --prod
    fi
fi

exit 0 