"use client";

import React from "react";
import Link from "next/link";
import { ChevronLeft } from "lucide-react";

interface SettingsHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
}

const SettingsHeader = ({ title, description, actions }: SettingsHeaderProps) => (
  <div className="mb-6">
    <div className="flex justify-between items-start">
      <div>
        <div className="flex items-center mb-2">
          <Link href="/admin/settings" className="text-gray-500 hover:text-gray-700 mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
        </div>
        {description && <p className="text-gray-600">{description}</p>}
      </div>
      {actions && (
        <div className="flex gap-2">
          {actions}
        </div>
      )}
    </div>
  </div>
);

export default SettingsHeader; 