"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { toast } from 'sonner';

// Define the steps in the profile completion process
export enum PROFILE_STEPS {
  PERSONAL = 'personal',
  CONTACT = 'contact',
  VISA_DETAILS = 'visa_details',
  DOCUMENTS = 'documents',
  PREFERENCES = 'preferences',
}

// Define the shape of the context
export interface ProfileCompletionContextType {
  currentStep: PROFILE_STEPS;
  completedSteps: Record<string, boolean>;
  progress: number;
  goToStep: (step: PROFILE_STEPS) => void;
  moveToNextStep: () => void;
  moveToPrevStep: () => void;
  markStepComplete: (step: PROFILE_STEPS) => void;
  saveAndExit: () => void;
  completeProfile: () => Promise<void>;
}

// Create the context with a default value
const ProfileCompletionContext = createContext<ProfileCompletionContextType | undefined>(undefined);

// Provider component
export function ProfileCompletionProvider({ children }: { children: ReactNode }) {
  // Remove session dependency since authentication is removed
  const [currentStep, setCurrentStep] = useState<PROFILE_STEPS>(PROFILE_STEPS.PERSONAL);
  const [completedSteps, setCompletedSteps] = useState<Record<string, boolean>>({
    [PROFILE_STEPS.PERSONAL]: false,
    [PROFILE_STEPS.CONTACT]: false,
    [PROFILE_STEPS.VISA_DETAILS]: false,
    [PROFILE_STEPS.DOCUMENTS]: false,
    [PROFILE_STEPS.PREFERENCES]: false,
  });
  const [progress, setProgress] = useState(20); // Start with 20% progress

  // Load user progress from API when component mounts
  useEffect(() => {
    const fetchUserProgress = async () => {
      try {
        const response = await fetch('/api/user/profile');
        if (response.ok) {
          const userData = await response.json();
          
          // Update completed steps based on user data
          if (userData.completedSteps) {
            setCompletedSteps(userData.completedSteps);
          }
          
          // Update progress
          if (userData.profileProgress) {
            setProgress(userData.profileProgress);
          }
        }
      } catch (error) {
        console.error('Failed to fetch user progress:', error);
      }
    };

    fetchUserProgress();
  }, []);

  // Calculate the next step based on the current step
  const getNextStep = (current: PROFILE_STEPS): PROFILE_STEPS => {
    switch (current) {
      case PROFILE_STEPS.PERSONAL:
        return PROFILE_STEPS.CONTACT;
      case PROFILE_STEPS.CONTACT:
        return PROFILE_STEPS.VISA_DETAILS;
      case PROFILE_STEPS.VISA_DETAILS:
        return PROFILE_STEPS.DOCUMENTS;
      case PROFILE_STEPS.DOCUMENTS:
        return PROFILE_STEPS.PREFERENCES;
      default:
        return PROFILE_STEPS.PREFERENCES;
    }
  };

  // Calculate the previous step based on the current step
  const getPrevStep = (current: PROFILE_STEPS): PROFILE_STEPS => {
    switch (current) {
      case PROFILE_STEPS.CONTACT:
        return PROFILE_STEPS.PERSONAL;
      case PROFILE_STEPS.VISA_DETAILS:
        return PROFILE_STEPS.CONTACT;
      case PROFILE_STEPS.DOCUMENTS:
        return PROFILE_STEPS.VISA_DETAILS;
      case PROFILE_STEPS.PREFERENCES:
        return PROFILE_STEPS.DOCUMENTS;
      default:
        return PROFILE_STEPS.PERSONAL;
    }
  };

  // Move to the next step
  const moveToNextStep = () => {
    setCurrentStep(getNextStep(currentStep));
  };

  // Move to the previous step
  const moveToPrevStep = () => {
    setCurrentStep(getPrevStep(currentStep));
  };

  // Go to a specific step
  const goToStep = (step: PROFILE_STEPS) => {
    setCurrentStep(step);
  };

  // Mark a step as complete and update progress
  const markStepComplete = async (step: PROFILE_STEPS) => {
    const updatedSteps = {
      ...completedSteps,
      [step]: true,
    };
    
    setCompletedSteps(updatedSteps);
    
    // Calculate new progress (20% base + 16% per completed step)
    const completedCount = Object.values(updatedSteps).filter(Boolean).length;
    const newProgress = Math.min(20 + (completedCount * 16), 100);
    setProgress(newProgress);
    
    // Update progress in the database
    try {
      await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          completedSteps: updatedSteps,
          profileProgress: newProgress,
        }),
      });
    } catch (error) {
      console.error('Failed to update progress:', error);
    }
  };

  // Save progress and exit to dashboard
  const saveAndExit = () => {
    toast.info('Progress saved. You can continue later.');
    window.location.href = '/user/dashboard';
  };

  // Mark the profile as complete
  const completeProfile = async () => {
    try {
      const response = await fetch('/api/user/profile/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        throw new Error('Failed to complete profile');
      }

      // Update session data
      await update({
        ...session,
        user: {
          ...session?.user,
          profileCompleted: true,
        },
      });

      return Promise.resolve();
    } catch (error) {
      console.error('Failed to complete profile:', error);
      return Promise.reject(error);
    }
  };

  // Create the context value
  const contextValue: ProfileCompletionContextType = {
    currentStep,
    completedSteps,
    progress,
    goToStep,
    moveToNextStep,
    moveToPrevStep,
    markStepComplete,
    saveAndExit,
    completeProfile,
  };

  return (
    <ProfileCompletionContext.Provider value={contextValue}>
      {children}
    </ProfileCompletionContext.Provider>
  );
}

// Custom hook to use the profile completion context
export function useProfileCompletion() {
  const context = useContext(ProfileCompletionContext);
  
  if (context === undefined) {
    throw new Error('useProfileCompletion must be used within a ProfileCompletionProvider');
  }
  
  return context;
} 