/**
 * Routing Rule model for determining lead assignment
 */
export interface RoutingRule {
  id: string;
  name: string;
  description: string;
  priority: number;
  conditions: any;
  assigneeId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  actions?: any;
}

/**
 * Lead Routing History entry
 */
export interface RoutingHistory {
  leadId: string;
  timestamp: Date;
  fromAssigneeId?: string;
  toAssigneeId: string;
  ruleId: string;
  reason: string;
}

/**
 * Lead matching function that determines if a lead matches routing rule conditions
 */
export const matchesConditions = (
  lead: Record<string, any>, 
  conditions: any
): boolean => {
  if (!conditions || typeof conditions !== 'object') return false;
  
  return Object.entries(conditions).every(([key, value]) => {
    // Check if the lead has the property
    if (!(key in lead)) return false;
    
    // Handle different condition types
    if (typeof value === 'object' && value !== null) {
      if ('equals' in value) return lead[key] === value.equals;
      if ('contains' in value && typeof value.contains === 'string') 
        return String(lead[key]).includes(value.contains as string);
      if ('greaterThan' in value && typeof value.greaterThan === 'number') 
        return lead[key] > (value.greaterThan as number);
      if ('lessThan' in value && typeof value.lessThan === 'number') 
        return lead[key] < (value.lessThan as number);
    } else {
      // Direct equality comparison
      return lead[key] === value;
    }
    
    return false;
  });
};

/**
 * Assigns a lead based on routing rules
 */
export const assignLead = (
  lead: Record<string, any>,
  rules: any[],
  currentAssigneeId?: string
): { assigneeId: string; ruleId: string; reason: string } | null => {
  // Sort rules by priority (higher numbers = higher priority)
  const sortedRules = [...rules]
    .filter(rule => rule.isActive)
    .sort((a, b) => b.priority - a.priority);
  
  // Try to find a matching rule
  for (const rule of sortedRules) {
    if (matchesConditions(lead, rule.conditions)) {
      // Don't reassign if already assigned to the same person
      if (currentAssigneeId && currentAssigneeId === rule.assigneeId) {
        return null;
      }
      
      return {
        assigneeId: rule.assigneeId,
        ruleId: rule.id,
        reason: `Matched rule: ${rule.name}`
      };
    }
  }
  
  return null;
}; 