"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { io, Socket } from "socket.io-client";

// SVG Icons
const SendIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 12 3.269 3.125A59.769 59.769 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.875L5.999 12Zm0 0h7.5" />
  </svg>
);

const AttachmentIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m18.375 12.739-7.693 7.693a4.5 4.5 0 0 1-6.364-6.364l10.94-10.94A3 3 0 1 1 19.5 7.372L8.552 18.32m.009-.01-.01.01m5.699-9.941-7.81 7.81a1.5 1.5 0 0 0 2.112 2.13" />
  </svg>
);

const ChevronLeftIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
  </svg>
);

const UserIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
  </svg>
);

function GlassCard({ children, className = "" }: { children: React.ReactNode; className?: string }) {
  return (
    <div
      className={`rounded-2xl p-6 shadow-lg ${className}`}
      style={{
        background: "linear-gradient(135deg, #f5f7fa55 0%, #c3cfe255 100%)",
        backdropFilter: "blur(12px)",
        boxShadow: "8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff",
        border: "1px solid rgba(255,255,255,0.15)",
      }}
    >
      {children}
    </div>
  );
}

// Define attachment interface
interface Attachment {
  filename: string;
  originalName: string;
  url: string;
  size: number;
  mimetype: string;
}

interface Message {
  id: string;
  sender: "user" | "agent" | "system";
  text: string;
  timestamp: string;
  attachments?: Attachment[];
  read?: boolean;
}

// Mock counselor information
const counselorInfo = {
  id: "counselor-001",
  name: "Priya Sharma",
  role: "Senior Visa Counselor",
  avatar: null, // No avatar yet
  status: "online",
  specialization: "EB-1 and Student Visas",
  availableHours: "9:00 AM - 5:00 PM IST"
};

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(true);
  const [hasAttachment, setHasAttachment] = useState(false);
  const [attachmentFile, setAttachmentFile] = useState<File | null>(null);
  const [uploadingAttachment, setUploadingAttachment] = useState(false);
  const [showCounselorInfo, setShowCounselorInfo] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [socketConnected, setSocketConnected] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const [notifications, setNotifications] = useState<{id: string, message: string}[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const socketRef = useRef<Socket | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Initialize socket connection
    const initializeSocket = async () => {
      try {
        // Initialize socket connection
        await fetch('/api/socketio');
        
        // Connect to socket
        const socket = io({
          path: '/api/socketio'
        });
        
        socket.on('connect', () => {
          console.log('Socket connected with ID:', socket.id);
          setSocketConnected(true);
          
          // Join the conversation
          const conversationId = `mock-user-id-counselor-001`;
          socket.emit('join-conversation', conversationId);
        });
        
        socket.on('new-message', (message: Message) => {
          console.log('Received new message:', message);
          setMessages(prev => {
            // Avoid duplicate messages
            if (prev.some(m => m.id === message.id)) return prev;
            return [...prev, message];
          });
        });
        
        socket.on('message-status-updated', ({ id, read }) => {
          console.log('Message status updated:', id, read);
          setMessages(prev => 
            prev.map(msg => (msg.id === id ? { ...msg, read } : msg))
          );
        });
        
        socket.on('user-typing', ({ userId, isTyping }) => {
          console.log('User typing status:', userId, isTyping);
          if (userId !== 'mock-user-id') {
            setIsTyping(isTyping);
          }
        });
        
        socket.on('disconnect', () => {
          console.log('Socket disconnected');
          setSocketConnected(false);
        });
        
        socket.on('connect_error', (err) => {
          console.error('Socket connection error:', err);
          setSocketConnected(false);
        });
        
        socketRef.current = socket;
        
        // Cleanup on unmount
        return () => {
          if (socketRef.current) {
            socketRef.current.emit('leave-conversation', `mock-user-id-counselor-001`);
            socketRef.current.disconnect();
          }
        };
      } catch (error) {
        console.error('Failed to initialize socket:', error);
      }
    };
    
    fetchMessages();
    initializeSocket();
    
    // No longer need polling with WebSockets
    // const interval = setInterval(fetchMessages, 30000);
    // return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  async function fetchMessages() {
    try {
      setLoading(true);
      const res = await fetch("/api/chat-messages?userId=mock-user-id");
      if (res.ok) {
        const data = await res.json();
        setMessages(data);
      }
    } catch (error) {
      console.error("Failed to fetch messages:", error);
    } finally {
      setLoading(false);
    }
  }

  async function uploadAttachment(file: File): Promise<Attachment | null> {
    try {
      setUploadingAttachment(true);
      
      const formData = new FormData();
      formData.append("file", file);
      
      const response = await fetch("/api/upload-message-attachment", {
        method: "POST",
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to upload file");
      }
      
      const data = await response.json();
      return {
        filename: data.filename,
        originalName: data.originalName,
        url: data.url,
        size: data.size,
        mimetype: data.mimetype
      };
    } catch (error) {
      console.error("Error uploading attachment:", error);
      return null;
    } finally {
      setUploadingAttachment(false);
    }
  }

  async function handleSend(e: React.FormEvent) {
    e.preventDefault();
    if ((input.trim() || attachmentFile) && !uploadingAttachment) {
      try {
        let attachments: Attachment[] = [];
        
        // Upload attachment if exists
        if (attachmentFile) {
          const uploadedAttachment = await uploadAttachment(attachmentFile);
          if (uploadedAttachment) {
            attachments = [uploadedAttachment];
          } else {
            // If upload failed, show error and return
            alert("Failed to upload attachment. Please try again.");
            return;
          }
        }
        
        // Send message to API
        const response = await fetch("/api/chat-messages", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ 
            userId: "mock-user-id", 
            sender: "user", 
            text: input.trim() || "Sent an attachment",
            attachments,
            receiverId: "counselor-001" // Add receiver ID
          }),
        });
        
        const newMessage = await response.json();
        
        // Optimistically add message to UI
        setMessages(prev => [...prev, newMessage]);
        
        setInput("");
        setAttachmentFile(null);
        setHasAttachment(false);
        
        // Stop typing indicator when sending a message
        handleTyping(false);
      } catch (error) {
        console.error("Failed to send message:", error);
      }
    }
  }
  
  function handleTyping(isActive: boolean) {
    if (!socketRef.current || !socketConnected) return;
    
    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Emit typing event
    socketRef.current.emit('typing', {
      conversationId: 'mock-user-id-agent',
      userId: 'mock-user-id',
      isTyping: isActive
    });
    
    // Set timeout to stop typing indicator after 3 seconds of inactivity
    if (isActive) {
      typingTimeoutRef.current = setTimeout(() => {
        socketRef.current?.emit('typing', {
          conversationId: 'mock-user-id-agent',
          userId: 'mock-user-id',
          isTyping: false
        });
      }, 3000);
    }
  }
  
  function handleInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    setInput(e.target.value);
    handleTyping(e.target.value.length > 0);
  }

  function formatTimestamp(timestamp: string) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  function formatMessageDate(timestamp: string) {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  }

  function handleAttachmentClick() {
    fileInputRef.current?.click();
  }

  function handleFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setAttachmentFile(file);
      setHasAttachment(true);
    }
  }

  function handleCancelAttachment() {
    setAttachmentFile(null);
    setHasAttachment(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }

  function handleSearch() {
    if (!searchQuery.trim()) return;
    
    // Filter messages by search query
    const filteredMessages = messages.filter(message => 
      message.text.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    // Highlight the search results
    if (filteredMessages.length > 0) {
      // Scroll to the first match
      const firstMatchId = filteredMessages[0].id;
      const element = document.getElementById(`message-${firstMatchId}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        element.classList.add('bg-yellow-100');
        setTimeout(() => element.classList.remove('bg-yellow-100'), 2000);
      }
    } else {
      // No matches found
      alert('No messages found matching your search.');
    }
  }

  function showNotification(message: string) {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { id, message }]);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 5000);
  }

  // Effect to show notifications when new messages arrive
  useEffect(() => {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.sender === 'agent' && document.visibilityState !== 'visible') {
      showNotification(`New message: ${lastMessage.text.substring(0, 50)}${lastMessage.text.length > 50 ? '...' : ''}`);
      
      // Try to use browser notifications if permitted
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('New Message', {
          body: lastMessage.text.substring(0, 100),
          icon: '/favicon.ico'
        });
      }
    }
  }, [messages]);

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission !== 'granted' && Notification.permission !== 'denied') {
      Notification.requestPermission();
    }
  }, []);

  // Group messages by date
  const messagesByDate: { [key: string]: Message[] } = {};
  const messagesToDisplay = searchQuery.trim() 
    ? messages.filter(message => message.text.toLowerCase().includes(searchQuery.toLowerCase())) 
    : messages;
    
  messagesToDisplay.forEach(message => {
    const date = formatMessageDate(message.timestamp);
    if (!messagesByDate[date]) {
      messagesByDate[date] = [];
    }
    messagesByDate[date].push(message);
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e6eeff] to-[#f5f7ff] py-6 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <Link href="/user" className="flex items-center text-blue-600 hover:text-blue-800">
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            <span>Back to Dashboard</span>
          </Link>
          <h1 className="text-2xl font-bold">Support Chat</h1>
          <div className="flex gap-2">
            <button 
              onClick={() => setShowSearch(!showSearch)}
              className={`w-9 h-9 flex items-center justify-center rounded-full ${
                showSearch ? 'bg-indigo-100 text-indigo-600' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
              </svg>
            </button>
            <button 
              onClick={() => setShowCounselorInfo(!showCounselorInfo)}
              className={`w-9 h-9 flex items-center justify-center rounded-full ${
                showCounselorInfo ? 'bg-indigo-100 text-indigo-600' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <UserIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
        
        {showSearch && (
          <div className="mb-4 flex">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search messages..."
              className="flex-1 p-2 rounded-l-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <button
              onClick={handleSearch}
              className="bg-indigo-600 text-white px-4 py-2 rounded-r-lg hover:bg-indigo-700"
            >
              Search
            </button>
          </div>
        )}
        
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <GlassCard className="flex flex-col h-[calc(100vh-180px)]">
              <div className="flex-1 overflow-y-auto px-2">
                {loading && messages.length === 0 ? (
                  <div className="flex justify-center items-center h-full">
                    <div className="animate-pulse text-gray-500">Loading messages...</div>
                  </div>
                ) : messagesToDisplay.length === 0 ? (
                  <div className="flex justify-center items-center h-full">
                    <div className="text-center text-gray-500">
                      <p className="mb-2">No messages yet</p>
                      <p className="text-sm">Start a conversation with your visa counselor</p>
                    </div>
                  </div>
                ) : (
                  Object.entries(messagesByDate).map(([date, dayMessages]) => (
                    <div key={date} className="mb-6">
                      <div className="flex justify-center mb-4">
                        <span className="bg-gray-200 text-gray-700 rounded-full px-4 py-1 text-xs">
                          {date}
                        </span>
                      </div>
                      {dayMessages.map((message) => (
                        <div
                          id={`message-${message.id}`}
                          key={message.id}
                          className={`flex mb-4 ${message.sender === "user" ? "justify-end" : "justify-start"}`}
                        >
                          {message.sender === "system" ? (
                            <div className="bg-gray-100 text-gray-600 rounded-lg p-3 max-w-[80%] text-sm">
                              <p>{message.text}</p>
                              <span className="text-xs text-gray-500 mt-1 block text-right">
                                {formatTimestamp(message.timestamp)}
                              </span>
                            </div>
                          ) : (
                            <div
                              className={`rounded-lg p-3 max-w-[80%] ${
                                message.sender === "user"
                                  ? "bg-indigo-600 text-white"
                                  : "bg-white text-gray-800"
                              }`}
                            >
                              <p>{message.text}</p>
                              {message.attachments && message.attachments.length > 0 && (
                                <div className="mt-2 space-y-2">
                                  {message.attachments.map((attachment, index) => (
                                    <div key={index} className="flex items-center text-sm">
                                      <a 
                                        href={attachment.url} 
                                        target="_blank" 
                                        rel="noopener noreferrer"
                                        className={`flex items-center border rounded px-2 py-1 ${
                                          message.sender === "user" ? "border-indigo-400 hover:bg-indigo-700" : "border-gray-300 hover:bg-gray-100"
                                        }`}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4 mr-1">
                                          <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                                        </svg>
                                        <span
                                          className={
                                            message.sender === "user" ? "text-indigo-200" : "text-gray-600"
                                          }
                                        >
                                          {attachment.originalName}
                                        </span>
                                      </a>
                                    </div>
                                  ))}
                                </div>
                              )}
                              <div className="flex justify-between items-center mt-1">
                                <span
                                  className={`text-xs ${
                                    message.sender === "user" ? "text-indigo-200" : "text-gray-500"
                                  }`}
                                >
                                  {formatTimestamp(message.timestamp)}
                                </span>
                                {message.sender === "user" && (
                                  <span className="text-xs text-indigo-200">
                                    {message.read ? "Read" : "Delivered"}
                                  </span>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ))
                )}
                {isTyping && (
                  <div className="flex justify-start mb-4">
                    <div className="bg-white text-gray-800 rounded-lg p-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                        <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce" style={{ animationDelay: '200ms' }}></div>
                        <div className="w-2 h-2 rounded-full bg-gray-500 animate-bounce" style={{ animationDelay: '400ms' }}></div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
              
              <form onSubmit={handleSend} className="border-t border-gray-200 pt-4 mt-4">
                {hasAttachment && attachmentFile && (
                  <div className="mb-2 px-2">
                    <div className="flex items-center justify-between bg-indigo-50 rounded p-2">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-indigo-600 mr-2">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                        </svg>
                        <span className="text-sm truncate max-w-[200px]">{attachmentFile.name}</span>
                      </div>
                      <button
                        type="button"
                        className="text-red-500 hover:text-red-700"
                        onClick={handleCancelAttachment}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                )}
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={handleAttachmentClick}
                    className="p-2 text-gray-500 hover:text-indigo-600"
                    disabled={loading || uploadingAttachment || hasAttachment}
                  >
                    <AttachmentIcon className="h-5 w-5" />
                    <input
                      ref={fileInputRef}
                      type="file"
                      className="hidden"
                      onChange={handleFileChange}
                      disabled={loading || uploadingAttachment}
                    />
                  </button>
                  <input
                    type="text"
                    value={input}
                    onChange={handleInputChange}
                    placeholder="Type a message..."
                    className="flex-1 p-2 border-0 focus:ring-0 focus:outline-none"
                    disabled={loading || uploadingAttachment}
                  />
                  <button
                    type="submit"
                    className={`p-2 rounded-full ${
                      (input.trim() || hasAttachment) && !uploadingAttachment
                        ? "text-indigo-600 hover:bg-indigo-50"
                        : "text-gray-400 cursor-not-allowed"
                    }`}
                    disabled={loading || uploadingAttachment || (!input.trim() && !hasAttachment)}
                  >
                    {uploadingAttachment ? (
                      <svg className="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      <SendIcon className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </form>
            </GlassCard>
          </div>
          
          {showCounselorInfo && (
            <div className="w-full md:w-64">
              <GlassCard className="h-full">
                <div className="text-center mb-4">
                  <div className="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 mx-auto mb-3">
                    {counselorInfo.avatar ? (
                      <img src={counselorInfo.avatar} alt={counselorInfo.name} className="w-20 h-20 rounded-full" />
                    ) : (
                      <UserIcon className="h-10 w-10" />
                    )}
                  </div>
                  <h3 className="font-semibold text-lg">{counselorInfo.name}</h3>
                  <p className="text-gray-600 text-sm">{counselorInfo.role}</p>
                  <div className="mt-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {counselorInfo.status}
                  </div>
                </div>
                
                <div className="text-sm text-gray-600 space-y-3">
                  <div>
                    <p className="font-medium text-gray-700">Specialization</p>
                    <p>{counselorInfo.specialization}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Available Hours</p>
                    <p>{counselorInfo.availableHours}</p>
                  </div>
                  
                  <div className="pt-3 border-t border-gray-200 mt-4">
                    <button className="w-full bg-indigo-600 text-white rounded-lg py-2 text-sm hover:bg-indigo-700 transition">
                      Schedule a Call
                    </button>
                  </div>
                </div>
              </GlassCard>
            </div>
          )}
        </div>
        
        <div className="text-center mt-4 text-sm text-gray-600">
          <p>
            {socketConnected ? (
              <span className="flex items-center justify-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Connected
              </span>
            ) : (
              <span className="flex items-center justify-center">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                Connecting...
              </span>
            )}
          </p>
        </div>
      </div>
      
      {/* Notifications */}
      <div className="fixed bottom-4 right-4 space-y-2 z-50">
        {notifications.map(notification => (
          <div 
            key={notification.id}
            className="bg-white rounded-lg shadow-lg p-4 max-w-xs animate-slide-in"
            style={{
              animation: "slide-in 0.3s ease-out forwards",
            }}
          >
            <div className="flex justify-between items-start">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 mr-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium">New Message</p>
                  <p className="text-sm text-gray-600">{notification.message}</p>
                </div>
              </div>
              <button 
                onClick={() => setNotifications(prev => prev.filter(n => n.id !== notification.id))}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        ))}
      </div>

      <style jsx>{`
        @keyframes slide-in {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
} 