import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { Check } from "lucide-react"
import GlassCard from "@/components/GlassCard"

export default function AboutPage() {
  const team = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      role: "Founder & CEO",
      image: "/placeholder.svg?height=300&width=300",
      bio: "Former immigration officer with 15+ years of experience helping students and professionals navigate global visa systems.",
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      role: "Head of Counseling",
      image: "/placeholder.svg?height=300&width=300",
      bio: "Education specialist with expertise in university admissions across the US, UK, and Canada.",
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Legal Advisor",
      image: "/placeholder.svg?height=300&width=300",
      bio: "Immigration attorney specializing in talent-based visas and complex immigration cases.",
    },
    {
      id: 4,
      name: "<PERSON><PERSON>",
      role: "Technology Director",
      image: "/placeholder.svg?height=300&width=300",
      bio: "Tech innovator focused on creating seamless digital experiences for visa applicants.",
    },
  ]

  const values = [
    {
      id: 1,
      title: "Integrity",
      description: "Transparent, ethical, and user-first service in everything we do.",
    },
    {
      id: 2,
      title: "Expertise",
      description: "Deep knowledge of global immigration and education systems.",
    },
    {
      id: 3,
      title: "Innovation",
      description: "Leveraging technology for smarter, faster solutions.",
    },
    {
      id: 4,
      title: "Empathy",
      description: "Personalized support at every step of your journey.",
    },
  ]

  return (
    <div className="py-12 md:py-16 bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container max-w-7xl mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Our Story
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Visa Mentor was founded to make global education and career opportunities accessible, transparent, and
            stress-free for everyone. We believe that every aspiring student and professional deserves expert guidance
            and a trusted partner on their immigration journey.
          </p>
        </div>

        {/* Mission Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center mb-12">
          <GlassCard className="transform transition hover:translate-y-[-4px]">
            <div className="relative h-[400px] rounded-lg overflow-hidden">
            <Image src="/placeholder.svg?height=800&width=600" alt="Our mission" fill className="object-cover" />
          </div>
          </GlassCard>
          <div>
            <h2 className="text-lg font-semibold text-indigo-800 mb-6">Our Mission</h2>
            <p className="text-sm text-gray-600 mb-6">
              To empower individuals and organizations with the knowledge, tools, and support they need to achieve their
              international dreams.
            </p>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="mt-1 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full p-1">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-sm text-gray-600">We simplify complex immigration processes</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="mt-1 bg-gradient-to-r from-indigo-600 to-purple-700 rounded-full p-1">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-sm text-gray-600">We provide personalized guidance based on your unique profile</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="mt-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full p-1">
                  <Check className="h-4 w-4 text-white" />
                </div>
                <p className="text-sm text-gray-600">We stay updated with the latest immigration policies and university requirements</p>
              </div>
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-10 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value) => (
              <GlassCard key={value.id} className="transform transition hover:translate-y-[-4px]">
                <CardContent className="p-6 text-center">
                  <h3 className="text-lg font-semibold text-indigo-800 mb-3">{value.title}</h3>
                  <p className="text-sm text-gray-600">{value.description}</p>
                </CardContent>
              </GlassCard>
            ))}
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-10 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">Meet the Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {team.map((member) => (
              <GlassCard key={member.id} className="transform transition hover:translate-y-[-4px]">
                <CardContent className="p-6 text-center">
                  <div className="w-32 h-32 mx-auto rounded-full overflow-hidden mb-4 border border-white/20 shadow-lg">
                    <Image
                      src={member.image || "/placeholder.svg"}
                      alt={member.name}
                      width={128}
                      height={128}
                      className="object-cover"
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-indigo-800 mb-1">{member.name}</h3>
                  <p className="text-xs text-indigo-600 font-medium mb-3">{member.role}</p>
                  <p className="text-sm text-gray-600">{member.bio}</p>
                </CardContent>
              </GlassCard>
            ))}
          </div>
        </div>

        {/* Process Section */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-10 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">Our Process</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <GlassCard className="transform transition hover:translate-y-[-4px]">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                1
              </div>
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Comprehensive Assessment</h3>
                <p className="text-sm text-gray-600">We analyze your goals and background to understand your unique situation.</p>
              </CardContent>
            </GlassCard>
            <GlassCard className="transform transition hover:translate-y-[-4px]">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-700 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                2
              </div>
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Personalized Roadmap</h3>
                <p className="text-sm text-gray-600">
                Custom strategy tailored specifically for your unique situation and goals.
              </p>
              </CardContent>
            </GlassCard>
            <GlassCard className="transform transition hover:translate-y-[-4px]">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                3
              </div>
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Ongoing Support</h3>
                <p className="text-sm text-gray-600">
                From first consultation to final approval and beyond, we're with you every step.
              </p>
              </CardContent>
            </GlassCard>
          </div>
        </div>

        {/* Impact Section */}
        <GlassCard className="transform transition hover:translate-y-[-4px]">
          <div className="p-8 md:p-12">
            <h2 className="text-3xl font-bold text-center mb-10 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">Our Impact</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="bg-indigo-50/50 rounded-lg p-4 text-center">
                <div className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 mb-2">8,000+</div>
                <p className="text-sm text-gray-600">Visas approved</p>
              </div>
              <div className="bg-indigo-50/50 rounded-lg p-4 text-center">
                <div className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-700 mb-2">$11,560</div>
                <p className="text-sm text-gray-600">Average scholarship secured</p>
            </div>
              <div className="bg-indigo-50/50 rounded-lg p-4 text-center">
                <div className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600 mb-2">97</div>
                <p className="text-sm text-gray-600">Fully funded admits this year</p>
            </div>
            </div>
          </div>
        </GlassCard>
      </div>
    </div>
  )
}
