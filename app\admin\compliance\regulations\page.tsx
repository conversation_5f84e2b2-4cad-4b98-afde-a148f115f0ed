"use client";

import { useState } from "react";
import Link from "next/link";
import {
  ChevronLeft,
  Search,
  Filter,
  CheckCircle,
  AlertTriangle,
  Clock,
  Shield,
  ChevronDown,
  ExternalLink,
  Download,
  FileText,
  Plus,
  ArrowUpDown,
} from "lucide-react";

// Mock data for regulations
const mockRegulations = [
  {
    id: "reg-1",
    name: "GDPR Data Subject Rights",
    category: "Data Privacy",
    status: "compliant",
    lastUpdated: "2023-09-15T10:30:00Z",
    description: "Requirements for handling data subject access requests and rights to erasure.",
    dueDate: "2023-12-15T00:00:00Z",
    assignee: "<PERSON>",
    priority: "high"
  },
  {
    id: "reg-2",
    name: "Application Data Security",
    category: "Security",
    status: "compliant",
    lastUpdated: "2023-09-10T14:20:00Z",
    description: "Security measures for protecting visa application data during processing.",
    dueDate: "2023-11-30T00:00:00Z",
    assignee: "<PERSON>",
    priority: "high"
  },
  {
    id: "reg-3",
    name: "Document Retention Policy",
    category: "Records Management",
    status: "at-risk",
    lastUpdated: "2023-09-05T09:15:00Z",
    description: "Requirements for maintaining and eventually destroying visa application documents.",
    dueDate: "2023-10-20T00:00:00Z",
    assignee: "Unassigned",
    priority: "medium"
  },
  {
    id: "reg-4",
    name: "Biometric Data Processing",
    category: "Data Privacy",
    status: "non-compliant",
    lastUpdated: "2023-08-28T16:40:00Z",
    description: "Rules for collecting, storing, and processing biometric data from visa applicants.",
    dueDate: "2023-10-10T00:00:00Z",
    assignee: "Emma Thompson",
    priority: "high"
  },
  {
    id: "reg-5",
    name: "Visa Fee Transparency",
    category: "Financial",
    status: "compliant",
    lastUpdated: "2023-09-20T11:10:00Z",
    description: "Requirements for clear communication of all visa application fees and charges.",
    dueDate: "2024-01-15T00:00:00Z",
    assignee: "Sarah Johnson",
    priority: "medium"
  },
  {
    id: "reg-6",
    name: "Background Check Procedures",
    category: "Security",
    status: "in-progress",
    lastUpdated: "2023-09-12T13:45:00Z",
    description: "Standardized procedures for conducting applicant background checks.",
    dueDate: "2023-11-05T00:00:00Z",
    assignee: "Michael Chen",
    priority: "medium"
  },
  {
    id: "reg-7",
    name: "Data Breach Notification",
    category: "Security",
    status: "compliant",
    lastUpdated: "2023-09-08T10:20:00Z",
    description: "Requirements for timely notification of data breaches to authorities and affected individuals.",
    dueDate: "2023-12-20T00:00:00Z",
    assignee: "Emma Thompson",
    priority: "high"
  },
  {
    id: "reg-8",
    name: "Third-party Data Sharing",
    category: "Data Privacy",
    status: "in-progress",
    lastUpdated: "2023-09-18T15:30:00Z",
    description: "Guidelines for sharing visa application data with third parties and partner agencies.",
    dueDate: "2023-10-30T00:00:00Z",
    assignee: "Unassigned",
    priority: "medium"
  }
];

// Categorize regulations by status for the summary cards
const complianceStats = {
  total: mockRegulations.length,
  compliant: mockRegulations.filter(reg => reg.status === "compliant").length,
  atRisk: mockRegulations.filter(reg => reg.status === "at-risk").length,
  nonCompliant: mockRegulations.filter(reg => reg.status === "non-compliant").length,
  inProgress: mockRegulations.filter(reg => reg.status === "in-progress").length
};

export default function RegulationsPage() {
  const [regulations, setRegulations] = useState(mockRegulations);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedPriority, setSelectedPriority] = useState("all");
  const [sortBy, setSortBy] = useState("dueDate");
  const [sortOrder, setSortOrder] = useState("asc");
  const [detailView, setDetailView] = useState<string | null>(null);

  // Get unique categories for filter dropdown
  const categories = ["all", ...new Set(mockRegulations.map(reg => reg.category))];
  
  // Handle sorting
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("asc");
    }
  };

  // Filter regulations based on search and filters
  const filteredRegulations = regulations.filter(regulation => {
    const matchesSearch = 
      regulation.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      regulation.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === "all" || regulation.category === selectedCategory;
    const matchesStatus = selectedStatus === "all" || regulation.status === selectedStatus;
    const matchesPriority = selectedPriority === "all" || regulation.priority === selectedPriority;
    
    return matchesSearch && matchesCategory && matchesStatus && matchesPriority;
  });

  // Sort filtered regulations
  const sortedRegulations = [...filteredRegulations].sort((a, b) => {
    if (sortBy === "name") {
      return sortOrder === "asc" 
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } else if (sortBy === "dueDate") {
      return sortOrder === "asc"
        ? new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
        : new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
    } else if (sortBy === "priority") {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return sortOrder === "asc"
        ? priorityOrder[a.priority as keyof typeof priorityOrder] - priorityOrder[b.priority as keyof typeof priorityOrder]
        : priorityOrder[b.priority as keyof typeof priorityOrder] - priorityOrder[a.priority as keyof typeof priorityOrder];
    }
    return 0;
  });

  // Get regulation details
  const selectedRegulation = detailView 
    ? regulations.find(reg => reg.id === detailView) 
    : null;

  // Calculate days remaining for compliance deadline
  const getDaysRemaining = (dueDate: string) => {
    const today = new Date();
    const deadline = new Date(dueDate);
    const diffTime = deadline.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "compliant":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "at-risk":
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case "non-compliant":
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case "in-progress":
        return <Clock className="h-5 w-5 text-blue-500" />;
      default:
        return <Shield className="h-5 w-5 text-gray-500" />;
    }
  };

  // Get status badge class
  const getStatusClass = (status: string) => {
    switch (status) {
      case "compliant":
        return "bg-green-100 text-green-700";
      case "at-risk":
        return "bg-amber-100 text-amber-700";
      case "non-compliant":
        return "bg-red-100 text-red-700";
      case "in-progress":
        return "bg-blue-100 text-blue-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  // Format status label
  const formatStatus = (status: string) => {
    switch (status) {
      case "at-risk":
        return "At Risk";
      case "non-compliant":
        return "Non-Compliant";
      case "in-progress":
        return "In Progress";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Link href="/admin/compliance" className="text-gray-500 hover:text-gray-700 mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-800">Regulations & Requirements</h1>
        </div>
        <p className="text-gray-600">Track and manage regulatory compliance requirements</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500 font-medium">Total Requirements</p>
            <Shield className="h-5 w-5 text-gray-400" />
          </div>
          <h3 className="text-2xl font-bold mt-1">{complianceStats.total}</h3>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500 font-medium">Compliant</p>
            <CheckCircle className="h-5 w-5 text-green-500" />
          </div>
          <h3 className="text-2xl font-bold mt-1">{complianceStats.compliant}</h3>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500 font-medium">At Risk</p>
            <AlertTriangle className="h-5 w-5 text-amber-500" />
          </div>
          <h3 className="text-2xl font-bold mt-1">{complianceStats.atRisk}</h3>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500 font-medium">Non-Compliant</p>
            <AlertTriangle className="h-5 w-5 text-red-500" />
          </div>
          <h3 className="text-2xl font-bold mt-1">{complianceStats.nonCompliant}</h3>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500 font-medium">In Progress</p>
            <Clock className="h-5 w-5 text-blue-500" />
          </div>
          <h3 className="text-2xl font-bold mt-1">{complianceStats.inProgress}</h3>
        </div>
      </div>

      {/* Detail View */}
      {detailView && selectedRegulation && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 mb-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <div className="flex items-center">
                <button 
                  onClick={() => setDetailView(null)}
                  className="mr-2 text-gray-500 hover:text-gray-700"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                <h2 className="text-xl font-semibold">{selectedRegulation.name}</h2>
              </div>
              <div className="mt-2 flex items-center gap-3">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(selectedRegulation.status)}`}>
                  {getStatusIcon(selectedRegulation.status)}
                  <span className="ml-1">{formatStatus(selectedRegulation.status)}</span>
                </span>
                <span className="text-sm text-gray-500">
                  Category: {selectedRegulation.category}
                </span>
                <span className="text-sm text-gray-500">
                  Priority: {selectedRegulation.priority.charAt(0).toUpperCase() + selectedRegulation.priority.slice(1)}
                </span>
              </div>
            </div>
            <div className="flex gap-2">
              <button className="px-3 py-1.5 text-sm bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors">
                Edit
              </button>
              <button className="px-3 py-1.5 text-sm bg-white border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-50 transition-colors">
                <Download className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2 space-y-5">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                <p className="text-gray-800">{selectedRegulation.description}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Compliance Requirements</h3>
                <ul className="space-y-2 pl-5 list-disc text-gray-800">
                  <li>Documentation of processes and procedures</li>
                  <li>Staff training on compliance protocols</li>
                  <li>Regular audits and reviews</li>
                  <li>Implementation of technical safeguards</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Related Documents</h3>
                <div className="space-y-2">
                  <div className="flex items-center p-2 rounded border border-gray-200 hover:bg-gray-50 transition-colors">
                    <FileText className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-gray-800">Compliance Policy.pdf</span>
                    <button className="ml-auto text-gray-400 hover:text-gray-600">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                  <div className="flex items-center p-2 rounded border border-gray-200 hover:bg-gray-50 transition-colors">
                    <FileText className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-gray-800">Implementation Guide.docx</span>
                    <button className="ml-auto text-gray-400 hover:text-gray-600">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-5">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-500 mb-3">Compliance Timeline</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-xs text-gray-500">Due Date</span>
                    <div className="flex items-center mt-1">
                      <Clock className="h-4 w-4 text-gray-500 mr-1.5" />
                      <span className="text-sm text-gray-800">{new Date(selectedRegulation.dueDate).toLocaleDateString()}</span>
                      <span className="text-xs text-amber-600 ml-2">
                        ({getDaysRemaining(selectedRegulation.dueDate)} days left)
                      </span>
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500">Last Updated</span>
                    <div className="flex items-center mt-1">
                      <Clock className="h-4 w-4 text-gray-500 mr-1.5" />
                      <span className="text-sm text-gray-800">{new Date(selectedRegulation.lastUpdated).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-500 mb-3">Assignment</h3>
                <div>
                  <span className="text-xs text-gray-500">Assigned To</span>
                  <div className="flex items-center mt-1">
                    {selectedRegulation.assignee !== "Unassigned" ? (
                      <div className="flex items-center">
                        <div className="h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 text-xs mr-2">
                          {selectedRegulation.assignee.split(' ').map(name => name[0]).join('')}
                        </div>
                        <span className="text-sm text-gray-800">{selectedRegulation.assignee}</span>
                      </div>
                    ) : (
                      <button className="text-xs text-indigo-600 bg-indigo-50 px-2 py-1 rounded hover:bg-indigo-100 transition-colors">
                        Assign Responsibility
                      </button>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-500 mb-3">Actions</h3>
                <div className="space-y-2">
                  <button className="w-full text-sm text-indigo-600 bg-indigo-50 px-3 py-1.5 rounded hover:bg-indigo-100 transition-colors flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 mr-1.5" />
                    Mark as Compliant
                  </button>
                  <button className="w-full text-sm text-blue-600 bg-blue-50 px-3 py-1.5 rounded hover:bg-blue-100 transition-colors flex items-center justify-center">
                    <FileText className="h-4 w-4 mr-1.5" />
                    Add Documentation
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      {!detailView && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search regulations..."
                className="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="flex gap-2">
              <div className="relative">
                <select
                  className="appearance-none pl-3 pr-8 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-500 bg-white"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="all">All Categories</option>
                  {categories.filter(c => c !== "all").map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
              
              <div className="relative">
                <select
                  className="appearance-none pl-3 pr-8 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-500 bg-white"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                >
                  <option value="all">All Statuses</option>
                  <option value="compliant">Compliant</option>
                  <option value="at-risk">At Risk</option>
                  <option value="non-compliant">Non-Compliant</option>
                  <option value="in-progress">In Progress</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
              
              <div className="relative">
                <select
                  className="appearance-none pl-3 pr-8 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-500 bg-white"
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value)}
                >
                  <option value="all">All Priorities</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
            
            <button className="flex items-center justify-center gap-1 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
              <Plus className="h-4 w-4" />
              <span>New Requirement</span>
            </button>
          </div>
        </div>
      )}

      {/* List of Regulations */}
      {!detailView && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center gap-1"
                      onClick={() => handleSort("name")}
                    >
                      Requirement
                      {sortBy === "name" && (
                        <ArrowUpDown className="h-3 w-3" />
                      )}
                    </button>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center gap-1"
                      onClick={() => handleSort("priority")}
                    >
                      Priority
                      {sortBy === "priority" && (
                        <ArrowUpDown className="h-3 w-3" />
                      )}
                    </button>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      className="flex items-center gap-1"
                      onClick={() => handleSort("dueDate")}
                    >
                      Due Date
                      {sortBy === "dueDate" && (
                        <ArrowUpDown className="h-3 w-3" />
                      )}
                    </button>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assignee
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedRegulations.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-10 text-center text-gray-500">
                      No regulations found matching your criteria
                    </td>
                  </tr>
                ) : (
                  sortedRegulations.map((regulation) => (
                    <tr 
                      key={regulation.id} 
                      className="hover:bg-gray-50 cursor-pointer"
                      onClick={() => setDetailView(regulation.id)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(regulation.status)}
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">{regulation.name}</div>
                            <div className="text-xs text-gray-500 truncate max-w-xs">{regulation.description}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{regulation.category}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(regulation.status)}`}>
                          {formatStatus(regulation.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          regulation.priority === "high" ? "bg-red-100 text-red-800" :
                          regulation.priority === "medium" ? "bg-amber-100 text-amber-800" :
                          "bg-green-100 text-green-800"
                        }`}>
                          {regulation.priority.charAt(0).toUpperCase() + regulation.priority.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{new Date(regulation.dueDate).toLocaleDateString()}</div>
                        <div className="text-xs text-gray-500">
                          {getDaysRemaining(regulation.dueDate)} days remaining
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {regulation.assignee !== "Unassigned" ? (
                          <div className="flex items-center">
                            <div className="h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 text-xs mr-2">
                              {regulation.assignee.split(' ').map(name => name[0]).join('')}
                            </div>
                            <span className="text-sm text-gray-900">{regulation.assignee}</span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">Unassigned</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setDetailView(regulation.id);
                          }}
                          className="text-indigo-600 hover:text-indigo-900 mr-3"
                        >
                          View
                        </button>
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle edit action
                          }}
                          className="text-gray-600 hover:text-gray-900"
                        >
                          Edit
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
} 