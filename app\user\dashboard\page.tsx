"use client"

import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON> from '@/components/PieChart';
import <PERSON>ant<PERSON><PERSON>hart from '@/components/GanttChart';
import GlassCard from '@/components/GlassCard';

export default function DashboardPage() {
  const [documents, setDocuments] = useState<any[]>([]);
  const [progressStage, setProgressStage] = useState(1);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDocuments();
    fetchVisaProgress();
  }, []);

  async function fetchDocuments() {
    const res = await fetch("/api/user-documents?userId=mock-user-id");
    if (res.ok) {
      setDocuments(await res.json());
    }
  }

  async function fetchVisaProgress() {
    const res = await fetch("/api/visa-progress?userId=mock-user-id");
    if (res.ok) {
      const data = await res.json();
      setProgressStage(data.progressStage);
    }
  }

  const documentStatus = documents.reduce((acc, doc) => {
    acc[doc.status] = (acc[doc.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e6eeff] to-[#f5f7ff] py-12 px-4">
      <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
        <GlassCard className="p-6">
          <h2 className="text-xl font-bold mb-4">Document Status</h2>
          <div className="h-64 flex items-center justify-center">
            <PieChart data={Object.entries(documentStatus).map(([label, value]) => ({ label, value }))} />
          </div>
        </GlassCard>
        <GlassCard className="p-6">
          <h2 className="text-xl font-bold mb-4">Visa Progress Tracker</h2>
          <div className="h-64 flex items-center justify-center">
            <GanttChart currentStage={progressStage} />
          </div>
        </GlassCard>
      </div>
    </div>
  );
} 