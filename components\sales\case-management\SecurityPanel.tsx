import React, { useState } from 'react';
import { 
  Lock, 
  Shield, 
  Users, 
  Eye, 
  FileText, 
  Database, 
  Key, 
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Server,
  UserCheck,
  Fingerprint,
  FileDigit,
  Settings
} from 'lucide-react';

export default function SecurityPanel() {
  const [activeTab, setActiveTab] = useState("encryption");
  
  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-sm p-4">
        <div className="flex flex-wrap gap-2">
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              activeTab === "encryption" 
                ? "bg-indigo-100 text-indigo-700" 
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            onClick={() => setActiveTab("encryption")}
          >
            <div className="flex items-center">
              <Lock className="h-4 w-4 mr-2" />
              Data Encryption
            </div>
          </button>
          
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              activeTab === "rbac" 
                ? "bg-indigo-100 text-indigo-700" 
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            onClick={() => setActiveTab("rbac")}
          >
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Access Control
            </div>
          </button>
          
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              activeTab === "audit" 
                ? "bg-indigo-100 text-indigo-700" 
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            onClick={() => setActiveTab("audit")}
          >
            <div className="flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              Audit Logs
            </div>
          </button>
        </div>
      </div>
      
      {/* Content based on active tab */}
      {activeTab === "encryption" && (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">AES-256 Encryption Status</h3>
              <div className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium flex items-center">
                <Shield className="h-3 w-3 mr-1" />
                Active
              </div>
            </div>
            <p className="text-sm text-gray-600 mb-6">
              All sensitive client data is encrypted with AES-256 encryption both at rest and in transit
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Shield className="h-5 w-5 text-green-600 mr-2" />
                  <h4 className="font-medium">Client Identifiers</h4>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Encryption</span>
                  <span className="text-sm font-medium text-green-600">Active</span>
                </div>
                <div className="mt-1 flex justify-between items-center">
                  <span className="text-sm text-gray-600">Key Rotation</span>
                  <span className="text-sm font-medium text-green-600">30 days</span>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Database className="h-5 w-5 text-green-600 mr-2" />
                  <h4 className="font-medium">Financial Data</h4>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Encryption</span>
                  <span className="text-sm font-medium text-green-600">Active</span>
                </div>
                <div className="mt-1 flex justify-between items-center">
                  <span className="text-sm text-gray-600">Key Rotation</span>
                  <span className="text-sm font-medium text-green-600">15 days</span>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <Fingerprint className="h-5 w-5 text-green-600 mr-2" />
                  <h4 className="font-medium">Biometric Data</h4>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Encryption</span>
                  <span className="text-sm font-medium text-green-600">Active</span>
                </div>
                <div className="mt-1 flex justify-between items-center">
                  <span className="text-sm text-gray-600">Key Rotation</span>
                  <span className="text-sm font-medium text-green-600">7 days</span>
                </div>
              </div>
            </div>
            
            <div className="border-t border-gray-200 pt-5">
              <h4 className="font-medium mb-3">Last Encryption Audit</h4>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="p-2 rounded-full bg-green-100 text-green-600 mr-2">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">All systems secure</p>
                    <p className="text-xs text-gray-500">Checked on May 15, 2023 at 10:32 AM</p>
                  </div>
                </div>
                <button className="px-3 py-1 bg-indigo-600 text-white rounded-md text-sm font-medium flex items-center">
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Run Check
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Data Security Recommendations</h3>
            
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start">
                  <div className="p-2 bg-amber-100 text-amber-600 rounded-lg mt-0.5 mr-3">
                    <AlertCircle className="h-5 w-5" />
                  </div>
                  <div>
                    <h4 className="font-medium">Enable Two-Factor Authentication</h4>
                    <p className="text-sm text-gray-600 mt-1">Add an extra layer of security by enabling 2FA for all team members</p>
                    <button className="px-3 py-1 bg-indigo-600 text-white rounded-md text-sm font-medium mt-3">
                      Configure 2FA
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start">
                  <div className="p-2 bg-blue-100 text-blue-600 rounded-lg mt-0.5 mr-3">
                    <Server className="h-5 w-5" />
                  </div>
                  <div>
                    <h4 className="font-medium">Schedule Regular Backups</h4>
                    <p className="text-sm text-gray-600 mt-1">Ensure data is backed up securely and regularly with encryption</p>
                    <div className="mt-2 flex items-center">
                      <span className="text-xs text-gray-500 mr-4">Current backup schedule: Daily at 2:00 AM</span>
                      <button className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md text-sm font-medium">
                        Modify Schedule
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {activeTab === "rbac" && (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Role-Based Access Control</h3>
            <p className="text-sm text-gray-600 mb-6">
              Configure user roles and document-level permissions for secure access management
            </p>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Users Assigned
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Document Access
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client Data Access
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {roles.map((role, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`p-1.5 rounded-md ${role.colorClass} mr-2`}>
                            <Users className="h-4 w-4" />
                          </div>
                          <div className="text-sm font-medium">{role.name}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {role.usersCount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {role.documentAccess.map((access, i) => (
                          <span key={i} className="inline-block px-2 py-1 mr-1 mb-1 bg-gray-100 rounded text-xs">
                            {access}
                          </span>
                        ))}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {role.clientDataAccess}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button className="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                        <button className="text-gray-600 hover:text-gray-900">View</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button className="px-4 py-2 bg-indigo-600 text-white rounded-md font-medium">
                Create New Role
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold mb-4">Document-Level Permissions</h3>
              
              <div className="space-y-4">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium mb-2">Client Financial Documents</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 text-gray-500 mr-2" />
                        <span className="text-sm text-gray-600">View</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Admin</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Senior Counselor</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Finance</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 text-gray-500 mr-2" />
                        <span className="text-sm text-gray-600">Edit</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Admin</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Finance</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium mb-2">Visa Application Documents</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 text-gray-500 mr-2" />
                        <span className="text-sm text-gray-600">View</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Admin</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">All Counselors</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 text-gray-500 mr-2" />
                        <span className="text-sm text-gray-600">Edit</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Admin</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">Assigned Counselor</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold mb-4">User Management</h3>
              
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search users..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-md mb-4"
                />
              </div>
              
              <div className="space-y-3">
                {users.map((user, index) => (
                  <div key={index} className="flex items-center justify-between border-b border-gray-100 pb-3 last:border-0">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 font-medium mr-3">
                        {user.initials}
                      </div>
                      <div>
                        <p className="text-sm font-medium">{user.name}</p>
                        <p className="text-xs text-gray-500">{user.role}</p>
                      </div>
                    </div>
                    <div>
                      <button className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 mr-2">
                        Edit Role
                      </button>
                      <button className="px-3 py-1 bg-red-50 border border-red-200 rounded-md text-xs text-red-600">
                        Revoke
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 text-center">
                <button className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                  View All Users
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {activeTab === "audit" && (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Security Audit Logs</h3>
              <div className="flex items-center">
                <select className="px-3 py-1 border border-gray-300 rounded-md text-sm mr-2">
                  <option>Last 7 days</option>
                  <option>Last 30 days</option>
                  <option>Last 90 days</option>
                  <option>Custom range</option>
                </select>
                <button className="px-3 py-1 bg-gray-100 rounded-md text-sm">
                  <RefreshCw className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timestamp
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Resource
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IP Address
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {auditLogs.map((log, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.timestamp}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 font-medium text-xs mr-2">
                            {log.userInitials}
                          </div>
                          <div className="text-sm text-gray-900">{log.user}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getActionBadgeColor(log.action)}`}>
                          {log.action}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.resource}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.ipAddress}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {log.status === "Success" ? (
                          <div className="text-green-600 flex items-center">
                            <CheckCircle className="h-4 w-4 mr-1" />
                            <span className="text-sm">Success</span>
                          </div>
                        ) : (
                          <div className="text-red-600 flex items-center">
                            <AlertCircle className="h-4 w-4 mr-1" />
                            <span className="text-sm">Failed</span>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <div className="mt-6 flex justify-between items-center">
              <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium">
                Export Logs
              </button>
              <div className="flex items-center space-x-2">
                <button className="px-3 py-1 border border-gray-300 rounded-md text-sm">
                  Previous
                </button>
                <span className="text-sm text-gray-600">Page 1 of 3</span>
                <button className="px-3 py-1 border border-gray-300 rounded-md text-sm">
                  Next
                </button>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Security Alert Settings</h3>
            
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 bg-red-100 text-red-600 rounded-lg mr-3">
                      <Key className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">Failed Login Attempts</h4>
                      <p className="text-sm text-gray-600">Alert after multiple failed login attempts</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm text-gray-600 mr-3">Threshold: 3 attempts</span>
                    <button className="p-1 rounded-md bg-gray-100">
                      <Settings className="h-4 w-4 text-gray-500" />
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 bg-amber-100 text-amber-600 rounded-lg mr-3">
                      <FileDigit className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">Sensitive Document Access</h4>
                      <p className="text-sm text-gray-600">Alert on unusual document access patterns</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm text-gray-600 mr-3">Status: Active</span>
                    <button className="p-1 rounded-md bg-gray-100">
                      <Settings className="h-4 w-4 text-gray-500" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function for audit log action colors
function getActionBadgeColor(action: string) {
  switch (action) {
    case "View":
      return "bg-blue-100 text-blue-800";
    case "Edit":
      return "bg-amber-100 text-amber-800";
    case "Delete":
      return "bg-red-100 text-red-800";
    case "Create":
      return "bg-green-100 text-green-800";
    case "Login":
      return "bg-indigo-100 text-indigo-800";
    case "Logout":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Mock data
const roles = [
  {
    name: "Administrator",
    usersCount: 3,
    documentAccess: ["View All", "Edit All", "Delete"],
    clientDataAccess: "Full Access",
    colorClass: "bg-indigo-100 text-indigo-600"
  },
  {
    name: "Senior Visa Counselor",
    usersCount: 5,
    documentAccess: ["View All", "Edit Assigned"],
    clientDataAccess: "Assigned Clients + Read Only Others",
    colorClass: "bg-blue-100 text-blue-600"
  },
  {
    name: "Junior Visa Counselor",
    usersCount: 8,
    documentAccess: ["View Assigned", "Edit Assigned"],
    clientDataAccess: "Assigned Clients Only",
    colorClass: "bg-green-100 text-green-600"
  },
  {
    name: "Finance Department",
    usersCount: 2,
    documentAccess: ["View Financial", "Edit Financial"],
    clientDataAccess: "Financial Data Only",
    colorClass: "bg-amber-100 text-amber-600"
  },
  {
    name: "Document Processor",
    usersCount: 4,
    documentAccess: ["View All", "Format Only"],
    clientDataAccess: "Limited Data",
    colorClass: "bg-purple-100 text-purple-600"
  }
];

const users = [
  {
    name: "Priya Sharma",
    initials: "PS",
    role: "Senior Visa Counselor"
  },
  {
    name: "Amit Kumar",
    initials: "AK",
    role: "Junior Visa Counselor"
  },
  {
    name: "Raj Verma",
    initials: "RV",
    role: "Administrator"
  },
  {
    name: "Neha Singh",
    initials: "NS",
    role: "Finance Department"
  },
  {
    name: "Deepak Patel",
    initials: "DP",
    role: "Document Processor"
  }
];

const auditLogs = [
  {
    timestamp: "May 18, 2023 10:32 AM",
    user: "Priya Sharma",
    userInitials: "PS",
    action: "View",
    resource: "Client Financial Statement",
    ipAddress: "*************",
    status: "Success"
  },
  {
    timestamp: "May 18, 2023 09:45 AM",
    user: "Raj Verma",
    userInitials: "RV",
    action: "Edit",
    resource: "User Permissions",
    ipAddress: "*************",
    status: "Success"
  },
  {
    timestamp: "May 17, 2023 04:22 PM",
    user: "Unknown",
    userInitials: "?",
    action: "Login",
    resource: "System",
    ipAddress: "************",
    status: "Failed"
  },
  {
    timestamp: "May 17, 2023 03:18 PM",
    user: "Amit Kumar",
    userInitials: "AK",
    action: "Create",
    resource: "New Client Record",
    ipAddress: "*************",
    status: "Success"
  },
  {
    timestamp: "May 17, 2023 01:45 PM",
    user: "Neha Singh",
    userInitials: "NS",
    action: "Edit",
    resource: "Invoice #INV-2023-054",
    ipAddress: "*************",
    status: "Success"
  },
  {
    timestamp: "May 17, 2023 11:32 AM",
    user: "Deepak Patel",
    userInitials: "DP",
    action: "Delete",
    resource: "Duplicate Document",
    ipAddress: "*************",
    status: "Success"
  },
  {
    timestamp: "May 16, 2023 05:14 PM",
    user: "Priya Sharma",
    userInitials: "PS",
    action: "Logout",
    resource: "System",
    ipAddress: "*************",
    status: "Success"
  }
]; 