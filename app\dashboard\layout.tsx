import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { redirect } from "next/navigation"
import DashboardSidebar from "@/components/dashboard/sidebar"
import DashboardHeader from "@/components/dashboard/header"

export const metadata: Metadata = {
  title: "Dashboard | Visa Mentor",
  description: "Manage your visa applications and track your progress",
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // This would normally check for authentication
  const isAuthenticated = true

  if (!isAuthenticated) {
    redirect("/login")
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      <DashboardSidebar />
      <div className="flex-1 flex flex-col">
        <DashboardHeader />
        <main className="flex-1 p-6">{children}</main>
      </div>
    </div>
  )
}
