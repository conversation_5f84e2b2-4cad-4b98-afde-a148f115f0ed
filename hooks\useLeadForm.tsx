'use client';

import { useState } from 'react';
import LeadForm from '@/components/leads/LeadForm';

type FormType = 'consultation' | 'counseling' | 'mock-interview' | 'general';

export default function useLeadForm(options: {
  defaultService?: string;
  source?: string;
  formType?: FormType;
} = {}) {
  const [isOpen, setIsOpen] = useState(false);
  
  const openLeadForm = () => setIsOpen(true);
  const closeLeadForm = () => setIsOpen(false);
  
  const LeadFormComponent = () => (
    <LeadForm 
      isOpen={isOpen} 
      onClose={closeLeadForm} 
      defaultService={options.defaultService || "General Inquiry"}
      source={options.source || "Website"}
      formType={options.formType || 'general'}
    />
  );
  
  return {
    openLeadForm,
    closeLeadForm,
    LeadFormComponent
  };
} 