"use client";

import { useState } from "react";
import { Smartphone, Bell, Cog, Shield, QrCode, Zap, Database, AppWindow, ToggleLeft } from "lucide-react";

// Import our reusable components
import SettingsHeader from "@/app/components/admin/SettingsHeader";
import SettingsSection from "@/app/components/admin/SettingsSection";
import Form<PERSON>ield from "@/app/components/admin/FormField";
import SaveButton from "@/app/components/admin/SaveButton";
import SuccessMessage from "@/app/components/admin/SuccessMessage";
import TabNav from "@/app/components/admin/TabNav";

export default function MobileAppSettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  
  // Mobile app settings
  const [appSettings, setAppSettings] = useState({
    enableMobileApp: true,
    version: "1.2.0",
    minRequiredVersion: "1.0.0",
    androidUrl: "https://play.google.com/store/apps/details?id=com.visamentor",
    iosUrl: "https://apps.apple.com/app/visa-mentor/id1234567890",
    pushEnabled: true,
    offlineMode: true,
    cacheSize: "50"
  });
  
  // Save settings
  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  // Handle switch toggle
  const handleToggle = (field: string) => {
    setAppSettings(prev => ({
      ...prev,
      [field]: !prev[field as keyof typeof prev]
    }));
  };

  // Handle form field changes
  const handleChange = (field: string, value: string) => {
    setAppSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Define tabs for the tab navigation
  const tabs = [
    { id: "general", label: "General" },
    { id: "push", label: "Push Notifications" },
    { id: "offline", label: "Offline Mode" },
    { id: "security", label: "Security" },
  ];

  return (
    <div>
      {/* Header */}
      <SettingsHeader 
        title="Mobile App" 
        description="Configure mobile app settings, push notifications, and app features"
        actions={
          <SaveButton isLoading={isSaving} onClick={saveSettings} />
        }
      />
      
      {/* Success Message */}
      {showSuccess && (
        <SuccessMessage message="Mobile app settings have been successfully saved." />
      )}
      
      {/* Tabs */}
      <TabNav tabs={tabs} activeTab={activeTab} onChange={setActiveTab} />
      
      {/* General Tab */}
      {activeTab === "general" && (
        <>
          <SettingsSection 
            title="App Configuration" 
            description="Basic mobile app configuration settings"
          >
            <FormField 
              label="Enable Mobile App" 
              helper="Toggle mobile app functionality on/off"
            >
              <div className="flex items-center">
                <button
                  type="button"
                  className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                    appSettings.enableMobileApp ? 'bg-indigo-600' : 'bg-gray-200'
                  }`}
                  onClick={() => handleToggle('enableMobileApp')}
                >
                  <span
                    className={`${
                      appSettings.enableMobileApp ? 'translate-x-6' : 'translate-x-1'
                    } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                  />
                </button>
                <span className="ml-3 text-sm">
                  {appSettings.enableMobileApp ? 'Enabled' : 'Disabled'}
                </span>
              </div>
            </FormField>
            
            <FormField 
              label="App Version" 
              helper="Current version of your mobile app"
            >
              <input
                type="text"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={appSettings.version}
                onChange={(e) => handleChange('version', e.target.value)}
              />
            </FormField>
            
            <FormField 
              label="Minimum Required Version" 
              helper="Users with versions below this will be forced to update"
            >
              <input
                type="text"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={appSettings.minRequiredVersion}
                onChange={(e) => handleChange('minRequiredVersion', e.target.value)}
              />
            </FormField>
          </SettingsSection>
          
          <SettingsSection 
            title="App Store Links" 
            description="Links to your mobile app in the app stores"
          >
            <FormField 
              label="Android Play Store URL" 
              helper="Link to your app in the Google Play Store"
            >
              <input
                type="url"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={appSettings.androidUrl}
                onChange={(e) => handleChange('androidUrl', e.target.value)}
              />
            </FormField>
            
            <FormField 
              label="iOS App Store URL" 
              helper="Link to your app in the Apple App Store"
            >
              <input
                type="url"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={appSettings.iosUrl}
                onChange={(e) => handleChange('iosUrl', e.target.value)}
              />
            </FormField>
          </SettingsSection>
          
          <SettingsSection 
            title="QR Code Access" 
            description="Allow users to quickly access the mobile app"
          >
            <div className="flex flex-col md:flex-row gap-6 items-center p-4">
              <div className="bg-white p-2 border rounded-lg shadow-sm">
                <div className="bg-gray-100 w-32 h-32 flex items-center justify-center">
                  <QrCode className="w-16 h-16 text-gray-400" />
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="font-medium">Download App QR Code</h3>
                <p className="text-sm text-gray-500">
                  This QR code will redirect users to the appropriate app store based on their device.
                </p>
                <div className="flex space-x-2">
                  <button className="bg-indigo-600 text-white px-3 py-2 rounded-md text-sm hover:bg-indigo-700">
                    Download QR Code
                  </button>
                  <button className="bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-200">
                    Regenerate
                  </button>
                </div>
              </div>
            </div>
          </SettingsSection>
        </>
      )}
      
      {/* Push Notifications Tab */}
      {activeTab === "push" && (
        <SettingsSection 
          title="Push Notification Settings" 
          description="Configure how push notifications are sent to mobile devices"
        >
          <FormField 
            label="Enable Push Notifications" 
            helper="Allow sending push notifications to mobile app users"
          >
            <div className="flex items-center">
              <button
                type="button"
                className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                  appSettings.pushEnabled ? 'bg-indigo-600' : 'bg-gray-200'
                }`}
                onClick={() => handleToggle('pushEnabled')}
              >
                <span
                  className={`${
                    appSettings.pushEnabled ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                />
              </button>
              <span className="ml-3 text-sm">
                {appSettings.pushEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </FormField>
          
          <FormField 
            label="Notification Types" 
            helper="Types of push notifications that can be sent"
          >
            <div className="space-y-2">
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="notification-updates" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="notification-updates" className="ml-2 block text-sm text-gray-700">
                  Application Updates
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="notification-status" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="notification-status" className="ml-2 block text-sm text-gray-700">
                  Visa Status Changes
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="notification-documents" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="notification-documents" className="ml-2 block text-sm text-gray-700">
                  Document Requests
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="notification-appointments" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="notification-appointments" className="ml-2 block text-sm text-gray-700">
                  Appointment Reminders
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="notification-marketing" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="notification-marketing" className="ml-2 block text-sm text-gray-700">
                  Marketing Messages
                </label>
              </div>
            </div>
          </FormField>
          
          <FormField 
            label="Daily Notification Limit" 
            helper="Maximum number of push notifications sent per day"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="2">2 per day</option>
              <option value="5" selected>5 per day</option>
              <option value="10">10 per day</option>
              <option value="unlimited">Unlimited</option>
            </select>
          </FormField>
          
          <FormField 
            label="Quiet Hours" 
            helper="Hours during which push notifications will not be sent"
          >
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-600 mb-1">From</label>
                <input 
                  type="time" 
                  className="w-full p-2 border border-gray-300 rounded-md"
                  defaultValue="22:00" 
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">To</label>
                <input 
                  type="time" 
                  className="w-full p-2 border border-gray-300 rounded-md"
                  defaultValue="07:00" 
                />
              </div>
            </div>
          </FormField>
        </SettingsSection>
      )}
      
      {/* Offline Mode Tab */}
      {activeTab === "offline" && (
        <SettingsSection 
          title="Offline Mode Settings" 
          description="Configure how the app behaves when offline"
        >
          <FormField 
            label="Enable Offline Mode" 
            helper="Allow users to access the app features when offline"
          >
            <div className="flex items-center">
              <button
                type="button"
                className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                  appSettings.offlineMode ? 'bg-indigo-600' : 'bg-gray-200'
                }`}
                onClick={() => handleToggle('offlineMode')}
              >
                <span
                  className={`${
                    appSettings.offlineMode ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                />
              </button>
              <span className="ml-3 text-sm">
                {appSettings.offlineMode ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </FormField>
          
          <FormField 
            label="Offline Data Access" 
            helper="Select which data types can be accessed offline"
          >
            <div className="space-y-2">
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="offline-profile" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="offline-profile" className="ml-2 block text-sm text-gray-700">
                  Profile Information
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="offline-documents" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="offline-documents" className="ml-2 block text-sm text-gray-700">
                  Uploaded Documents
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="offline-status" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="offline-status" className="ml-2 block text-sm text-gray-700">
                  Visa Status Information
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="offline-forms" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="offline-forms" className="ml-2 block text-sm text-gray-700">
                  Saved Forms & Applications
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="offline-guides" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="offline-guides" className="ml-2 block text-sm text-gray-700">
                  Visa Guides & Resources
                </label>
              </div>
            </div>
          </FormField>
          
          <FormField 
            label="Data Sync Frequency" 
            helper="How often data is synced when connection is restored"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="immediately">Immediately</option>
              <option value="15min">Every 15 minutes</option>
              <option value="hourly">Hourly</option>
              <option value="manual">Manual sync only</option>
            </select>
          </FormField>
          
          <FormField 
            label="Cache Size Limit (MB)" 
            helper="Maximum storage space used for offline data"
          >
            <input
              type="number"
              className="w-full p-2 border border-gray-300 rounded-md"
              value={appSettings.cacheSize}
              onChange={(e) => handleChange('cacheSize', e.target.value)}
              min="10"
              max="500"
            />
          </FormField>
        </SettingsSection>
      )}
      
      {/* Security Tab */}
      {activeTab === "security" && (
        <SettingsSection 
          title="Mobile App Security" 
          description="Configure security settings for the mobile application"
        >
          <FormField 
            label="App Lock" 
            helper="Require authentication to access the app after a period of inactivity"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="none">Disabled</option>
              <option value="1min">After 1 minute</option>
              <option value="5min" selected>After 5 minutes</option>
              <option value="15min">After 15 minutes</option>
              <option value="30min">After 30 minutes</option>
              <option value="1hour">After 1 hour</option>
              <option value="always">Always require authentication</option>
            </select>
          </FormField>
          
          <FormField 
            label="Authentication Methods" 
            helper="Select which authentication methods are allowed on mobile"
          >
            <div className="space-y-2">
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="auth-biometric" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="auth-biometric" className="ml-2 block text-sm text-gray-700">
                  Biometric Authentication (Fingerprint/Face ID)
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="auth-pin" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="auth-pin" className="ml-2 block text-sm text-gray-700">
                  PIN Code
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="auth-password" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="auth-password" className="ml-2 block text-sm text-gray-700">
                  Password
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="auth-pattern" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="auth-pattern" className="ml-2 block text-sm text-gray-700">
                  Pattern Lock (Android only)
                </label>
              </div>
            </div>
          </FormField>
          
          <FormField 
            label="Sensitive Data Access" 
            helper="Control access to sensitive information on mobile"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="always-auth">Always require authentication</option>
              <option value="session">Once per session</option>
              <option value="remember">Remember for 30 days</option>
            </select>
          </FormField>
          
          <FormField 
            label="Device Restrictions" 
            helper="Limit which devices can access the mobile app"
          >
            <div className="space-y-2">
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="restrict-rooted" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="restrict-rooted" className="ml-2 block text-sm text-gray-700">
                  Block rooted/jailbroken devices
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="restrict-emulators" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="restrict-emulators" className="ml-2 block text-sm text-gray-700">
                  Block emulators and virtual devices
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="restrict-outdated" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="restrict-outdated" className="ml-2 block text-sm text-gray-700">
                  Block outdated OS versions
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="number" 
                  id="max-devices" 
                  className="w-16 p-1 border border-gray-300 rounded-md mr-2" 
                  defaultValue="5"
                />
                <label htmlFor="max-devices" className="block text-sm text-gray-700">
                  Maximum devices per user
                </label>
              </div>
            </div>
          </FormField>
        </SettingsSection>
      )}
    </div>
  );
} 