"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Download,
  Filter,
  ChevronDown,
  TrendingUp,
  Users,
  Target,
  DollarSign,
  Star,
  Clock,
  FileText,
  Map,
  Award,
  AlertTriangle,
  RefreshCcw,
  ChevronRight,
  Repeat,
  BarChart,
  CheckCircle
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock data for personal performance analytics
const personalPerformanceData = {
  revenue: {
    current: 985000,
    target: 1500000,
    previousPeriod: 780000,
    trend: 26.3,
    monthly: [
      { month: "Jan", value: 520000 },
      { month: "Feb", value: 640000 },
      { month: "Mar", value: 710000 },
      { month: "Apr", value: 780000 },
      { month: "May", value: 985000 }
    ]
  },
  conversionRate: {
    current: 72,
    target: 75,
    previousPeriod: 64,
    trend: 12.5,
    monthly: [
      { month: "Jan", value: 58 },
      { month: "Feb", value: 61 },
      { month: "Mar", value: 63 },
      { month: "Apr", value: 64 },
      { month: "May", value: 72 }
    ]
  },
  leads: {
    current: 18,
    previousPeriod: 15,
    assigned: 24,
    active: 18,
    trend: 20,
    monthly: [
      { month: "Jan", value: 12 },
      { month: "Feb", value: 14 },
      { month: "Mar", value: 13 },
      { month: "Apr", value: 15 },
      { month: "May", value: 18 }
    ]
  },
  responseTime: {
    current: 3.2,
    target: 4,
    previousPeriod: 4.1,
    trend: -22,
    monthly: [
      { month: "Jan", value: 4.8 },
      { month: "Feb", value: 4.5 },
      { month: "Mar", value: 4.2 },
      { month: "Apr", value: 4.1 },
      { month: "May", value: 3.2 }
    ]
  },
  clientSatisfaction: {
    current: 4.7,
    previousPeriod: 4.4,
    trend: 6.8,
    monthly: [
      { month: "Jan", value: 4.2 },
      { month: "Feb", value: 4.3 },
      { month: "Mar", value: 4.3 },
      { month: "Apr", value: 4.4 },
      { month: "May", value: 4.7 }
    ]
  }
};

// Visa type distribution
const visaTypeDistribution = [
  { type: "EB-1", count: 5, percentage: 28, color: "from-blue-500 to-indigo-600" },
  { type: "O-1", count: 4, percentage: 22, color: "from-indigo-500 to-purple-600" },
  { type: "H-1B", count: 6, percentage: 33, color: "from-purple-500 to-pink-600" },
  { type: "Student", count: 3, percentage: 17, color: "from-blue-400 to-cyan-500" }
];

// Client conversion funnel
const clientConversionFunnel = [
  { stage: "Initial Contact", count: 32, percentage: 100 },
  { stage: "Consultation", count: 28, percentage: 88 },
  { stage: "Application Started", count: 22, percentage: 69 },
  { stage: "Documents Submitted", count: 18, percentage: 56 },
  { stage: "Visa Approved", count: 13, percentage: 41 }
];

// Monthly targets progress
const monthlyTargets = [
  { name: "Revenue", current: 985000, target: 1500000, percentage: 65.7, color: "from-green-500 to-emerald-400" },
  { name: "Leads", current: 18, target: 25, percentage: 72, color: "from-blue-500 to-indigo-400" },
  { name: "Conversions", current: 13, target: 18, percentage: 72.2, color: "from-indigo-500 to-purple-400" },
  { name: "Client Satisfaction", current: 4.7, target: 4.8, percentage: 97.9, color: "from-purple-500 to-pink-400" }
];

// Client activity timeline
const clientActivities = [
  { 
    clientName: "Aditya Mehta", 
    action: "Documents Approved", 
    visaType: "EB-1", 
    time: "Today at 10:30 AM",
    status: "success"
  },
  { 
    clientName: "Sneha Reddy", 
    action: "Interview Scheduled", 
    visaType: "Student Visa", 
    time: "Today at 9:15 AM",
    status: "info"
  },
  { 
    clientName: "Rajat Gupta", 
    action: "Document Submission", 
    visaType: "H1-B", 
    time: "Yesterday at 3:30 PM",
    status: "success"
  },
  { 
    clientName: "Meera Patel", 
    action: "Document Rejected", 
    visaType: "O-1", 
    time: "Yesterday at 11:45 AM",
    status: "error"
  },
  { 
    clientName: "Vikram Malhotra", 
    action: "Consultation Scheduled", 
    visaType: "EB-2", 
    time: "May 15, 2023",
    status: "info"
  }
];

// Country distribution of clients
const countryDistribution = [
  { country: "India", count: 12, percentage: 67 },
  { country: "China", count: 3, percentage: 17 },
  { country: "Pakistan", count: 2, percentage: 11 },
  { country: "Bangladesh", count: 1, percentage: 5 }
];

export default function AnalyticsPage() {
  const [timeframe, setTimeframe] = useState("This Month");
  const [comparisonPeriod, setComparisonPeriod] = useState("Previous Month");
  const [selectedMetric, setSelectedMetric] = useState("revenue");

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(value);
  };

  // Function to get appropriate trend indicator
  const getTrendIndicator = (trend: number, isInverse: boolean = false) => {
    const isPositive = trend > 0;
    const isGood = isInverse ? !isPositive : isPositive;
    
    return (
      <p className={`text-sm font-medium flex items-center ${isGood ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? (
          <ArrowUpRight size={16} className="mr-1" />
        ) : (
          <ArrowDownRight size={16} className="mr-1" />
        )}
        {Math.abs(trend)}%
      </p>
    );
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 md:gap-0">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Personal Analytics
          </h1>
          <p className="text-gray-600 mt-1">Track your performance metrics and client analytics</p>
        </div>

        <div className="flex flex-wrap items-center gap-3">
          <div className="relative">
            <select
              className="appearance-none bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 py-2 px-4 pr-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 shadow-sm"
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
            >
              <option>Today</option>
              <option>This Week</option>
              <option>This Month</option>
              <option>This Quarter</option>
              <option>This Year</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <ChevronDown className="h-4 w-4 text-indigo-500" />
            </div>
          </div>
          <button className="flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 py-2 px-4 rounded-lg hover:bg-indigo-50/80 transition-colors shadow-sm">
            <Download className="h-4 w-4" />
            <span>Export</span>
          </button>
          <button className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-2 px-4 rounded-lg hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md">
            <RefreshCcw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Key Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Revenue Card */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Revenue</p>
              <p className="text-2xl font-bold text-indigo-800">{formatCurrency(personalPerformanceData.revenue.current)}</p>
              <p className="text-xs text-indigo-600 mt-1">Target: {formatCurrency(personalPerformanceData.revenue.target)}</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-green-400 to-emerald-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <DollarSign size={24} />
            </div>
          </div>
          <div className="mt-3">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
              {getTrendIndicator(personalPerformanceData.revenue.trend)}
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-green-500 to-emerald-400 rounded-full" 
                style={{width: `${Math.min(100, (personalPerformanceData.revenue.current / personalPerformanceData.revenue.target) * 100)}%`}}
              ></div>
            </div>
          </div>
        </GlassCard>

        {/* Conversion Rate Card */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Conversion Rate</p>
              <p className="text-2xl font-bold text-indigo-800">{personalPerformanceData.conversionRate.current}%</p>
              <p className="text-xs text-indigo-600 mt-1">Target: {personalPerformanceData.conversionRate.target}%</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-blue-400 to-indigo-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Target size={24} />
            </div>
          </div>
          <div className="mt-3">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
              {getTrendIndicator(personalPerformanceData.conversionRate.trend)}
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-blue-500 to-indigo-400 rounded-full" 
                style={{width: `${Math.min(100, (personalPerformanceData.conversionRate.current / personalPerformanceData.conversionRate.target) * 100)}%`}}
              ></div>
            </div>
          </div>
        </GlassCard>

        {/* Active Leads Card */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Active Leads</p>
              <p className="text-2xl font-bold text-indigo-800">{personalPerformanceData.leads.current}</p>
              <p className="text-xs text-indigo-600 mt-1">Assigned: {personalPerformanceData.leads.assigned}</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Users size={24} />
            </div>
          </div>
          <div className="mt-3">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
              {getTrendIndicator(personalPerformanceData.leads.trend)}
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-indigo-500 to-blue-400 rounded-full" 
                style={{width: `${Math.min(100, (personalPerformanceData.leads.active / personalPerformanceData.leads.assigned) * 100)}%`}}
              ></div>
            </div>
          </div>
        </GlassCard>

        {/* Response Time Card */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Avg. Response Time</p>
              <p className="text-2xl font-bold text-indigo-800">{personalPerformanceData.responseTime.current}h</p>
              <p className="text-xs text-indigo-600 mt-1">Target: {personalPerformanceData.responseTime.target}h</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-purple-400 to-pink-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Clock size={24} />
            </div>
          </div>
          <div className="mt-3">
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
              {getTrendIndicator(personalPerformanceData.responseTime.trend, true)}
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-purple-500 to-pink-400 rounded-full" 
                style={{width: `${Math.min(100, 100 - (personalPerformanceData.responseTime.current / personalPerformanceData.responseTime.target) * 100)}%`}}
              ></div>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Main Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Performance Trend Chart */}
        <GlassCard className="lg:col-span-2 p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex flex-wrap justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-indigo-800">Performance Trend</h2>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <select
                  className="appearance-none bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-sm text-indigo-700 py-1.5 px-3 pr-8 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  value={selectedMetric}
                  onChange={(e) => setSelectedMetric(e.target.value)}
                >
                  <option value="revenue">Revenue</option>
                  <option value="conversionRate">Conversion Rate</option>
                  <option value="leads">Leads</option>
                  <option value="responseTime">Response Time</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
            </div>
          </div>
          
          {/* Visualization placeholder - in a real app, this would be a chart */}
          <div className="h-64 bg-indigo-50/50 backdrop-blur-sm rounded-lg flex items-center justify-center">
            <div className="text-center">
              <LineChart className="h-12 w-12 text-indigo-300 mx-auto mb-2" />
              <p className="text-indigo-600 font-medium">Performance Trend Visualization</p>
              <p className="text-sm text-indigo-400">Monthly trend data would be rendered here</p>
            </div>
          </div>
          
          {/* Monthly data display */}
          <div className="mt-4 grid grid-cols-5 gap-2">
            {personalPerformanceData[selectedMetric as keyof typeof personalPerformanceData].monthly.map((item) => (
              <div key={item.month} className="text-center">
                <p className="text-xs text-indigo-600 font-medium">{item.month}</p>
                <p className="text-sm text-indigo-800">
                  {selectedMetric === 'revenue' 
                    ? formatCurrency(item.value).replace('₹', '').replace(',000', 'K')
                    : selectedMetric === 'responseTime'
                      ? `${item.value}h`
                      : item.value}
                </p>
              </div>
            ))}
          </div>
        </GlassCard>

        {/* Visa Type Distribution */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <h2 className="text-lg font-semibold text-indigo-800 mb-6">Visa Type Distribution</h2>
          
          {/* Visualization placeholder */}
          <div className="h-48 flex items-center justify-center mb-4">
            <PieChart className="h-32 w-32 text-indigo-300" />
          </div>
          
          <div className="space-y-3">
            {visaTypeDistribution.map((item) => (
              <div key={item.type}>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">{item.type}</span>
                  <span className="font-medium text-indigo-800">{item.count} ({item.percentage}%)</span>
                </div>
                <div className="w-full bg-indigo-100/40 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full bg-gradient-to-r ${item.color}`} 
                    style={{ width: `${item.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </GlassCard>
      </div>

      {/* Monthly Targets Progress & Client Conversion */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Monthly Targets */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-indigo-800">Monthly Targets Progress</h2>
            <span className="text-xs text-indigo-600 px-2 py-1 bg-indigo-50/70 backdrop-blur-sm rounded-lg">May 2023</span>
          </div>
          
          <div className="space-y-6">
            {monthlyTargets.map((target) => (
              <div key={target.name} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">{target.name}</span>
                  <div className="flex items-center">
                    <span className="font-medium text-indigo-800">
                      {target.name === 'Revenue'
                        ? `${formatCurrency(target.current).replace('₹', '').replace(',000', 'K')}`
                        : target.name === 'Client Satisfaction'
                          ? `${target.current}★`
                          : target.current}
                    </span>
                    <span className="mx-1 text-gray-500">/</span>
                    <span className="text-indigo-600">
                      {target.name === 'Revenue'
                        ? `${formatCurrency(target.target).replace('₹', '').replace(',000', 'K')}`
                        : target.name === 'Client Satisfaction'
                          ? `${target.target}★`
                          : target.target}
                    </span>
                  </div>
                </div>
                <div className="w-full h-3 bg-indigo-100/40 rounded-full overflow-hidden">
                  <div 
                    className={`h-3 bg-gradient-to-r ${target.color} rounded-full`} 
                    style={{ width: `${target.percentage}%` }}
                  ></div>
                </div>
                <div className="text-right text-xs font-medium text-indigo-700">
                  {target.percentage}% completed
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-indigo-50/50 rounded-lg">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-indigo-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-indigo-800">Performance Ranking</p>
                <p className="text-xs text-gray-600">You're ranked <span className="font-bold text-indigo-700">2nd</span> among all sales executives this month</p>
              </div>
            </div>
          </div>
        </GlassCard>

        {/* Client Conversion Funnel */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <h2 className="text-lg font-semibold text-indigo-800 mb-6">Client Conversion Funnel</h2>
          
          <div className="space-y-4">
            {clientConversionFunnel.map((stage, index) => (
              <div key={stage.stage}>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">{stage.stage}</span>
                  <span className="font-medium text-indigo-800">{stage.count} ({stage.percentage}%)</span>
                </div>
                <div className="w-full bg-indigo-100/40 rounded-lg h-8 relative overflow-hidden">
                  <div 
                    className={`h-8 rounded-lg ${
                      index === 0 ? 'bg-gradient-to-r from-blue-500 to-indigo-600' :
                      index === 1 ? 'bg-gradient-to-r from-indigo-500 to-purple-600' :
                      index === 2 ? 'bg-gradient-to-r from-purple-500 to-pink-600' :
                      index === 3 ? 'bg-gradient-to-r from-pink-500 to-rose-600' :
                      'bg-gradient-to-r from-green-500 to-emerald-400'
                    }`} 
                    style={{ width: `${stage.percentage}%` }}
                  >
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                      {stage.stage}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-indigo-600 font-medium">Lead-to-Approval Conversion</p>
                <p className="text-xl font-bold text-indigo-800">41%</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white shadow-sm">
                <TrendingUp className="h-5 w-5" />
              </div>
            </div>
            <div className="mt-2">
              <p className="text-xs text-gray-600">Your conversion rate from initial contact to visa approval</p>
            </div>
          </div>
        </GlassCard>

        {/* Recent Activities */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-indigo-800">Recent Client Activities</h2>
            <button className="text-xs text-indigo-600 flex items-center hover:text-indigo-800 transition-colors">
              View All
              <ChevronRight className="h-3 w-3 ml-1" />
            </button>
          </div>
          
          <div className="space-y-4">
            {clientActivities.map((activity, index) => (
              <div key={index} className="flex items-start">
                <div 
                  className={`h-8 w-8 rounded-full flex items-center justify-center shadow-sm mr-3 ${
                    activity.status === 'success' ? 'bg-gradient-to-br from-green-400 to-emerald-300 text-white' :
                    activity.status === 'error' ? 'bg-gradient-to-br from-red-400 to-rose-300 text-white' :
                    'bg-gradient-to-br from-blue-400 to-indigo-300 text-white'
                  }`}
                >
                  {activity.status === 'success' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : activity.status === 'error' ? (
                    <AlertTriangle className="h-4 w-4" />
                  ) : (
                    <Calendar className="h-4 w-4" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <p className="text-sm font-medium text-indigo-800">{activity.clientName}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                  <p className="text-xs text-gray-600">{activity.action}</p>
                  <div className="mt-1">
                    <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
                      {activity.visaType}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </GlassCard>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Client Satisfaction */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-indigo-800">Client Satisfaction</h2>
            <div className="text-2xl font-bold text-indigo-800 flex items-center">
              {personalPerformanceData.clientSatisfaction.current}
              <Star className="h-5 w-5 text-yellow-500 ml-1" />
            </div>
          </div>
          
          {/* Visualization placeholder */}
          <div className="h-48 bg-indigo-50/50 backdrop-blur-sm rounded-lg flex items-center justify-center mb-4">
            <div className="text-center">
              <BarChart className="h-12 w-12 text-indigo-300 mx-auto mb-2" />
              <p className="text-indigo-600 font-medium">Rating Distribution</p>
              <p className="text-sm text-indigo-400">Monthly satisfaction trend</p>
            </div>
          </div>
          
          <div className="flex justify-between items-center mt-3">
            <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
            {getTrendIndicator(personalPerformanceData.clientSatisfaction.trend)}
          </div>
          
          <div className="mt-4 grid grid-cols-5 gap-2">
            {personalPerformanceData.clientSatisfaction.monthly.map((item) => (
              <div key={item.month} className="text-center">
                <p className="text-xs text-indigo-600 font-medium">{item.month}</p>
                <p className="text-sm text-indigo-800">{item.value}</p>
              </div>
            ))}
          </div>
        </GlassCard>

        {/* Country Distribution */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <h2 className="text-lg font-semibold text-indigo-800 mb-6">Client Countries</h2>
          
          <div className="flex items-center justify-center mb-4">
            <Map className="h-16 w-16 text-indigo-300" />
          </div>
          
          <div className="space-y-3 mt-2">
            {countryDistribution.map((item) => (
              <div key={item.country}>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">{item.country}</span>
                  <span className="font-medium text-indigo-800">{item.count} ({item.percentage}%)</span>
                </div>
                <div className="w-full bg-indigo-100/40 rounded-full h-2">
                  <div 
                    className="h-2 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600" 
                    style={{ width: `${item.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 p-3 bg-indigo-50/50 backdrop-blur-sm rounded-lg">
            <p className="text-xs text-center text-indigo-600">
              Most of your clients are from India, making up 67% of your portfolio
            </p>
          </div>
        </GlassCard>

        {/* Document Completion Rate */}
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <h2 className="text-lg font-semibold text-indigo-800 mb-6">Document Completion Rate</h2>
          
          <div className="flex justify-center">
            <div className="relative h-44 w-44">
              {/* This would be a circular progress chart in a real app */}
              <div className="h-full w-full rounded-full border-8 border-indigo-100/40 flex items-center justify-center">
                <div className="absolute inset-0 rounded-full border-8 border-indigo-500 border-t-transparent border-r-transparent border-b-transparent transform -rotate-45"></div>
                <div className="text-center">
                  <p className="text-3xl font-bold text-indigo-800">78%</p>
                  <p className="text-xs text-indigo-600">completion rate</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-6 space-y-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <FileText className="h-4 w-4 text-indigo-600 mr-2" />
                <span className="text-sm text-indigo-800">Documents Required</span>
              </div>
              <span className="text-sm font-medium text-indigo-800">45</span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                <span className="text-sm text-indigo-800">Documents Submitted</span>
              </div>
              <span className="text-sm font-medium text-indigo-800">35</span>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 text-amber-600 mr-2" />
                <span className="text-sm text-indigo-800">Pending Documents</span>
              </div>
              <span className="text-sm font-medium text-amber-700">10</span>
            </div>
          </div>
          
          <button className="w-full mt-4 bg-white/80 backdrop-blur-sm border border-indigo-200/60 py-2 px-4 rounded-lg text-indigo-700 text-sm font-medium hover:bg-indigo-50/70 transition-colors flex items-center justify-center">
            <Repeat className="h-4 w-4 mr-1.5" />
            Request Missing Documents
          </button>
        </GlassCard>
      </div>
    </div>
  );
} 