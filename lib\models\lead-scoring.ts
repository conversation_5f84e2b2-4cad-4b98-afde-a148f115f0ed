import { EnhancedLead } from '@prisma/client';

// Define the weight categories for lead scoring
export interface ScoreWeights {
  visaType: Record<string, number>;
  documentCompleteness: number;
  responseTime: number;
  financialCapacity: number;
  [key: string]: any;
}

// Default weights as specified in the integration plan
export const defaultWeights: ScoreWeights = {
  visaType: { 'EB-1': 0.4, 'O-1': 0.3, Student: 0.2 },
  documentCompleteness: 0.25,
  responseTime: 0.15,
  financialCapacity: 0.2
};

/**
 * Normalizes a value to a score between 0 and 1
 */
export const normalizeValue = (value: any): number => {
  if (typeof value === 'number') {
    return Math.min(Math.max(value, 0), 1);
  }
  
  if (typeof value === 'boolean') {
    return value ? 1 : 0;
  }
  
  return 0.5; // Default for unknown types
};

/**
 * Calculates a lead score based on the provided weights
 */
export const calculateLeadScore = (lead: EnhancedLead, weights: ScoreWeights = defaultWeights): number => {
  return Object.entries(lead).reduce((score, [key, value]) => {
    if (key === 'visaType' && weights.visaType && typeof value === 'string') {
      return score + (weights.visaType[value] || 0);
    }
    return score + (weights[key] || 0) * normalizeValue(value);
  }, 0);
};

/**
 * Categorizes leads based on score
 */
export const categorizeLead = (score: number): 'hot' | 'warm' | 'cold' => {
  if (score >= 0.7) return 'hot';
  if (score >= 0.4) return 'warm';
  return 'cold';
}; 