import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { TaskService } from '@/lib/services/taskService';
import { UserService } from '@/lib/services/userService';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get tasks for user
    const tasks = await TaskService.getUserTasks(user.id);

    return NextResponse.json({ tasks });
  } catch (error) {
    console.error('API: Error fetching tasks:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const data = await request.json();

    if (!data.title) {
      return NextResponse.json({ error: 'Task title is required' }, { status: 400 });
    }

    // Create new task
    const task = await TaskService.createTask({
      userId: user.id,
      ...data,
      dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
    });

    return NextResponse.json({ task }, { status: 201 });
  } catch (error) {
    console.error('API: Error creating task:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
