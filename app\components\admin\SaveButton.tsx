"use client";

import React from "react";
import { Save } from "lucide-react";

interface SaveButtonProps {
  isLoading?: boolean;
  onClick: () => void;
  label?: string;
}

const SaveButton = ({ isLoading = false, onClick, label = "Save Changes" }: SaveButtonProps) => (
  <button 
    onClick={onClick}
    className={`rounded-lg flex items-center gap-2 py-2 px-4 font-medium shadow-md transition ${
      isLoading 
        ? "bg-gray-200 text-gray-600 cursor-not-allowed" 
        : "bg-gradient-to-r from-blue-600 to-indigo-700 text-white hover:from-blue-700 hover:to-indigo-800"
    }`}
    disabled={isLoading}
  >
    {isLoading ? (
      <>
        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
        <span>Saving...</span>
      </>
    ) : (
      <>
        <Save className="h-4 w-4" />
        <span>{label}</span>
      </>
    )}
  </button>
);

export default SaveButton; 