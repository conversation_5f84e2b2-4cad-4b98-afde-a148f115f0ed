"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  Area,
  AreaChart,
  PieChart,
  Pie,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from "recharts";
import {
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Users,
  UserCheck,
  Calendar,
  Filter,
  Download,
  ChevronRight,
  Pie<PERSON>hart as PieChartIcon,
  Bar<PERSON>hart as BarChartIcon,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  Clock
} from "lucide-react";

// Mock data for analytics
const overviewMetrics = {
  totalClients: 1248,
  activeEngagements: 376,
  conversionRate: 32.8,
  avgClientValue: "₹42,500",
  clientRetention: 86.5,
  leadToClientTime: "34 days"
};

// Mock data for conversion funnel
const conversionFunnelData = [
  { name: "Leads", value: 2450 },
  { name: "Qualified", value: 1840 },
  { name: "Proposals", value: 1120 },
  { name: "Negotiations", value: 720 },
  { name: "Converted", value: 490 }
];

// Mock data for engagement trends
const engagementTrendsData = [
  { month: "Jan", meetings: 145, emails: 320, calls: 87 },
  { month: "Feb", meetings: 158, emails: 342, calls: 92 },
  { month: "Mar", meetings: 162, emails: 356, calls: 105 },
  { month: "Apr", meetings: 170, emails: 368, calls: 98 },
  { month: "May", meetings: 180, emails: 385, calls: 112 },
  { month: "Jun", meetings: 195, emails: 400, calls: 120 }
];

// Mock data for client segments
const clientSegmentsData = [
  { name: "Students", value: 35, color: "#4f46e5" },
  { name: "Professionals", value: 25, color: "#0ea5e9" },
  { name: "Families", value: 20, color: "#10b981" },
  { name: "Business", value: 15, color: "#f59e0b" },
  { name: "Tourists", value: 5, color: "#6366f1" }
];

// Mock data for service performance
const servicePerformanceData = [
  { service: "Student Visa", revenue: 1250000, clients: 180, growth: 12.5 },
  { service: "Work Visa", revenue: 980000, clients: 145, growth: 8.2 },
  { service: "Family Visa", revenue: 720000, clients: 95, growth: 5.4 },
  { service: "Business Visa", revenue: 650000, clients: 75, growth: 15.8 },
  { service: "Tourist Visa", revenue: 420000, clients: 120, growth: -2.5 },
  { service: "PR Visa", revenue: 1580000, clients: 110, growth: 18.6 }
];

// Mock data for timeline analytics
const timelineAnalyticsData = [
  { month: "Jan", conversions: 32, revenue: 1350000 },
  { month: "Feb", conversions: 38, revenue: 1580000 },
  { month: "Mar", conversions: 42, revenue: 1780000 },
  { month: "Apr", conversions: 35, revenue: 1490000 },
  { month: "May", conversions: 48, revenue: 1920000 },
  { month: "Jun", conversions: 52, revenue: 2180000 }
];

// Mock data for regional performance
const regionalPerformanceData = [
  { region: "North India", clients: 380, revenue: 15800000, growth: 12.5 },
  { region: "South India", clients: 420, revenue: 17500000, growth: 15.8 },
  { region: "East India", clients: 180, revenue: 7600000, growth: 5.2 },
  { region: "West India", clients: 268, revenue: 11200000, growth: 8.6 }
];

export default function AnalyticsPage() {
  const [dateRange, setDateRange] = useState("last6Months");

  const getStatusChange = (value: number, prevValue: number) => {
    const change = ((value - prevValue) / prevValue) * 100;
    if (change > 0) {
      return (
        <div className="flex items-center text-green-600">
          <ArrowUpRight className="h-4 w-4 mr-1" />
          <span>{change.toFixed(1)}%</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center text-red-600">
          <ArrowDownRight className="h-4 w-4 mr-1" />
          <span>{Math.abs(change).toFixed(1)}%</span>
        </div>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Date Range Controls */}
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h2>
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className={dateRange === "last30Days" ? "bg-muted" : ""} onClick={() => setDateRange("last30Days")}>
              Last 30 Days
            </Button>
            <Button variant="outline" size="sm" className={dateRange === "last3Months" ? "bg-muted" : ""} onClick={() => setDateRange("last3Months")}>
              Last 3 Months
            </Button>
            <Button variant="outline" size="sm" className={dateRange === "last6Months" ? "bg-muted" : ""} onClick={() => setDateRange("last6Months")}>
              Last 6 Months
            </Button>
            <Button variant="outline" size="sm" className={dateRange === "ytd" ? "bg-muted" : ""} onClick={() => setDateRange("ytd")}>
              Year to Date
            </Button>
          </div>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.totalClients}</div>
            <div className="text-xs text-muted-foreground mt-1 flex items-center">
              {getStatusChange(overviewMetrics.totalClients, 1180)}
              <span className="ml-1">vs previous period</span>
            </div>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Engagements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.activeEngagements}</div>
            <div className="text-xs text-muted-foreground mt-1 flex items-center">
              {getStatusChange(overviewMetrics.activeEngagements, 340)}
              <span className="ml-1">vs previous period</span>
            </div>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.conversionRate}%</div>
            <div className="text-xs text-muted-foreground mt-1 flex items-center">
              {getStatusChange(overviewMetrics.conversionRate, 30.2)}
              <span className="ml-1">vs previous period</span>
            </div>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Avg Client Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.avgClientValue}</div>
            <div className="text-xs text-muted-foreground mt-1 flex items-center">
              {getStatusChange(42500, 39800)}
              <span className="ml-1">vs previous period</span>
            </div>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Client Retention</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.clientRetention}%</div>
            <div className="text-xs text-muted-foreground mt-1 flex items-center">
              {getStatusChange(overviewMetrics.clientRetention, 84.2)}
              <span className="ml-1">vs previous period</span>
            </div>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Lead to Client</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overviewMetrics.leadToClientTime}</div>
            <div className="text-xs text-muted-foreground mt-1 flex items-center">
              {getStatusChange(34, 38)}
              <span className="ml-1">vs previous period</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Conversion Funnel */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <TrendingDown className="mr-2 h-5 w-5 text-muted-foreground" />
              Conversion Funnel
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                layout="vertical"
                data={conversionFunnelData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" />
                <Tooltip />
                <Bar dataKey="value" fill="#4f46e5" radius={[0, 4, 4, 0]} />
              </BarChart>
            </ResponsiveContainer>
            <div className="mt-4 text-sm text-center">
              <div className="font-medium">Funnel Conversion: 20.0%</div>
              <div className="text-muted-foreground text-xs">Leads to Client conversion rate</div>
            </div>
          </CardContent>
        </Card>

        {/* Engagement Trends */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <LineChartIcon className="mr-2 h-5 w-5 text-muted-foreground" />
              Engagement Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart
                data={engagementTrendsData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="meetings" stroke="#4f46e5" activeDot={{ r: 8 }} />
                <Line type="monotone" dataKey="emails" stroke="#10b981" />
                <Line type="monotone" dataKey="calls" stroke="#f59e0b" />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-4 grid grid-cols-3 gap-4 text-sm text-center">
              <div>
                <div className="font-medium text-[#4f46e5]">Meetings</div>
                <div className="text-xl">+12.5%</div>
              </div>
              <div>
                <div className="font-medium text-[#10b981]">Emails</div>
                <div className="text-xl">+8.2%</div>
              </div>
              <div>
                <div className="font-medium text-[#f59e0b]">Calls</div>
                <div className="text-xl">+15.3%</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Client Segments and Service Performance */}
        <Tabs defaultValue="segments" className="lg:col-span-3">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="segments">Client Segments</TabsTrigger>
            <TabsTrigger value="services">Service Performance</TabsTrigger>
          </TabsList>
          <TabsContent value="segments" className="p-0 mt-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="lg:col-span-1">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <PieChartIcon className="mr-2 h-5 w-5 text-muted-foreground" />
                    Client Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={clientSegmentsData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {clientSegmentsData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Users className="mr-2 h-5 w-5 text-muted-foreground" />
                    Client Segment Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-8">
                    {clientSegmentsData.map((segment) => (
                      <div key={segment.name} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center">
                            <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: segment.color }}></div>
                            <span className="font-medium">{segment.name}</span>
                          </div>
                          <div className="text-sm">{segment.value}% of clients</div>
                        </div>
                        
                        <div className="text-sm grid grid-cols-3 gap-2">
                          <div>
                            <div className="text-muted-foreground">Avg. Value</div>
                            <div className="font-medium">
                              ₹{(30000 + Math.random() * 20000).toFixed(0)}
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Conversion</div>
                            <div className="font-medium">
                              {(20 + Math.random() * 30).toFixed(1)}%
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Growth</div>
                            <div className="font-medium">
                              {(Math.random() * 20).toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="services" className="p-0 mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <BarChartIcon className="mr-2 h-5 w-5 text-muted-foreground" />
                  Service Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart
                          data={servicePerformanceData}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="service" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="clients" fill="#4f46e5" name="Clients" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                    <div>
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart
                          data={servicePerformanceData}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="service" />
                          <YAxis />
                          <Tooltip formatter={(value) => `₹${value.toLocaleString()}`} />
                          <Bar dataKey="revenue" fill="#10b981" name="Revenue" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <div className="font-medium text-lg mb-2">Service Details</div>
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left py-2 font-medium">Service</th>
                            <th className="text-right py-2 font-medium">Clients</th>
                            <th className="text-right py-2 font-medium">Revenue</th>
                            <th className="text-right py-2 font-medium">Growth</th>
                          </tr>
                        </thead>
                        <tbody>
                          {servicePerformanceData.map((service) => (
                            <tr key={service.service} className="border-b">
                              <td className="py-2">{service.service}</td>
                              <td className="text-right py-2">{service.clients}</td>
                              <td className="text-right py-2">₹{service.revenue.toLocaleString()}</td>
                              <td className="text-right py-2">
                                <span className={service.growth >= 0 ? "text-green-600" : "text-red-600"}>
                                  {service.growth >= 0 ? "+" : ""}{service.growth}%
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Timeline Analytics and Regional Performance */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Calendar className="mr-2 h-5 w-5 text-muted-foreground" />
              Timeline Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart
                data={timelineAnalyticsData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" orientation="left" stroke="#4f46e5" />
                <YAxis yAxisId="right" orientation="right" stroke="#10b981" />
                <Tooltip />
                <Legend />
                <Area yAxisId="left" type="monotone" dataKey="conversions" stroke="#4f46e5" fill="#4f46e520" name="Conversions" />
                <Area yAxisId="right" type="monotone" dataKey="revenue" stroke="#10b981" fill="#10b98120" name="Revenue (₹)" />
              </AreaChart>
            </ResponsiveContainer>
            <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground mb-1">Total Conversions</div>
                <div className="text-2xl font-bold">247</div>
                <div className="flex items-center text-green-600 text-xs">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  <span>15.3% vs previous period</span>
                </div>
              </div>
              <div>
                <div className="text-muted-foreground mb-1">Total Revenue</div>
                <div className="text-2xl font-bold">₹10.3M</div>
                <div className="flex items-center text-green-600 text-xs">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  <span>18.7% vs previous period</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <TrendingUp className="mr-2 h-5 w-5 text-muted-foreground" />
              Regional Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {regionalPerformanceData.map((region) => (
                <div key={region.region} className="space-y-2">
                  <div className="font-medium">{region.region}</div>
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div>
                      <div className="text-muted-foreground text-xs">Clients</div>
                      <div>{region.clients}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground text-xs">Revenue</div>
                      <div>₹{(region.revenue / 1000000).toFixed(1)}M</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground text-xs">Growth</div>
                      <div className={region.growth >= 0 ? "text-green-600" : "text-red-600"}>
                        {region.growth >= 0 ? "+" : ""}{region.growth}%
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div 
                      className="bg-indigo-600 h-1.5 rounded-full" 
                      style={{ width: `${(region.revenue / 20000000) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t">
              <div className="flex justify-between items-center mb-2">
                <div className="font-medium">Regional Distribution</div>
                <div className="text-sm text-muted-foreground">by client count</div>
              </div>
              <ResponsiveContainer width="100%" height={140}>
                <PieChart>
                  <Pie
                    data={regionalPerformanceData}
                    cx="50%"
                    cy="50%"
                    innerRadius={30}
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="clients"
                    nameKey="region"
                  >
                    <Cell fill="#4f46e5" />
                    <Cell fill="#0ea5e9" />
                    <Cell fill="#10b981" />
                    <Cell fill="#f59e0b" />
                  </Pie>
                  <Tooltip 
                    formatter={(value, name) => [`${value} clients`, name]}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tools */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Advanced Analytics Tools</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <UserCheck className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Client Predictions</span>
              <span className="text-xs text-gray-500 mt-1">AI-powered insights</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <Clock className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Conversion Analysis</span>
              <span className="text-xs text-gray-500 mt-1">Optimize your funnel</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Custom Reports</span>
              <span className="text-xs text-gray-500 mt-1">Build your own views</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <Download className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Export Analytics</span>
              <span className="text-xs text-gray-500 mt-1">CSV, PDF, Excel</span>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 