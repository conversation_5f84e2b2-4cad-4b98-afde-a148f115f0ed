"use client";

import { useState, useRef, useEffect } from "react";
import { 
  Search, 
  MessageSquare, 
  User, 
  Plus, 
  Send, 
  ArrowUpCircle, 
  Clock, 
  CheckCircle, 
  Paperclip, 
  Phone, 
  Video, 
  MoreVertical, 
  UserCheck, 
  Smile, 
  Calendar,
  FileText,
  X,
  ChevronDown,
  Image,
  ChevronRight
} from "lucide-react";
import { io, Socket } from "socket.io-client";
import GlassCard from "@/components/GlassCard";

// Mock client data
const mockClients = [
  {
    id: "client-001",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/aditya.jpg",
    lastMessage: "I've uploaded my passport documents",
    lastMessageTime: "10:30 AM",
    unreadCount: 2,
    visaType: "EB-1",
    status: "online", // online, offline, busy
    stage: "Document Collection",
    lastActive: "Just now",
  },
  {
    id: "client-002",
    name: "<PERSON><PERSON><PERSON> <PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/sneha.jpg",
    lastMessage: "When is my interview scheduled?",
    lastMessageTime: "Yesterday",
    unreadCount: 0,
    visaType: "Student Visa",
    status: "offline",
    stage: "Interview Preparation",
    lastActive: "3 hours ago",
  },
  {
    id: "client-003",
    name: "Rajat Gupta",
    email: "<EMAIL>",
    avatar: "/avatars/rajat.jpg",
    lastMessage: "Thank you for your help with the documents",
    lastMessageTime: "Yesterday",
    unreadCount: 0,
    visaType: "H1-B",
    status: "busy",
    stage: "Application Review",
    lastActive: "Yesterday",
  },
  {
    id: "client-004",
    name: "Meera Patel",
    email: "<EMAIL>",
    avatar: "/avatars/meera.jpg",
    lastMessage: "Do I need any additional documents?",
    lastMessageTime: "2 days ago",
    unreadCount: 0,
    visaType: "O-1",
    status: "offline",
    stage: "Initial Consultation",
    lastActive: "2 days ago",
  },
];

// Mock messages for each conversation
const mockConversations: Record<string, Array<{
  id: string;
  senderId: string;
  receiverId: string;
  text: string;
  timestamp: string;
  status: 'sent' | 'delivered' | 'read';
  attachments?: Array<{name: string, url: string, type: string}>;
}>> = {
  "client-001": [
    {
      id: "msg-001",
      senderId: "counselor-001",
      receiverId: "client-001",
      text: "Hello Aditya, I've reviewed your profile and you're an excellent candidate for the EB-1 visa. Let's prepare your documentation.",
      timestamp: "2023-05-17T09:30:00",
      status: "read"
    },
    {
      id: "msg-002",
      senderId: "client-001",
      receiverId: "counselor-001",
      text: "Thank you for the positive feedback! I'm excited to proceed. What documents do I need to prepare first?",
      timestamp: "2023-05-17T09:40:00",
      status: "read"
    },
    {
      id: "msg-003",
      senderId: "counselor-001",
      receiverId: "client-001",
      text: "For the EB-1 visa, we need to demonstrate your extraordinary ability. Please prepare your CV, publication list, citation records, and recommendation letters from experts in your field.",
      timestamp: "2023-05-17T09:45:00",
      status: "read"
    },
    {
      id: "msg-004",
      senderId: "client-001",
      receiverId: "counselor-001",
      text: "I have my CV and publication list ready. I'll work on getting the recommendation letters. How many letters would be ideal?",
      timestamp: "2023-05-17T10:00:00",
      status: "read"
    },
    {
      id: "msg-005",
      senderId: "counselor-001",
      receiverId: "client-001",
      text: "Aim for at least 5-6 recommendation letters from peers or superiors who can speak to your contributions to the field. Quality is more important than quantity though.",
      timestamp: "2023-05-17T10:05:00",
      status: "read"
    },
    {
      id: "msg-006",
      senderId: "client-001",
      receiverId: "counselor-001",
      text: "I've uploaded my passport documents",
      timestamp: "2023-05-18T10:30:00",
      status: "delivered"
    },
    {
      id: "msg-007",
      senderId: "client-001",
      receiverId: "counselor-001",
      text: "And also my CV. Can you please review it?",
      timestamp: "2023-05-18T10:32:00",
      status: "delivered",
      attachments: [{
        name: "Aditya_Mehta_CV.pdf",
        url: "/documents/cv.pdf",
        type: "application/pdf"
      }]
    }
  ],
  "client-002": [
    {
      id: "msg-101",
      senderId: "counselor-001",
      receiverId: "client-002",
      text: "Hi Sneha, congratulations on your acceptance to Stanford! Let's prepare for your student visa interview.",
      timestamp: "2023-05-16T14:00:00",
      status: "read"
    },
    {
      id: "msg-102",
      senderId: "client-002",
      receiverId: "counselor-001",
      text: "Thank you! I'm a bit nervous about the interview. What kind of questions should I expect?",
      timestamp: "2023-05-16T14:10:00",
      status: "read"
    },
    {
      id: "msg-103",
      senderId: "counselor-001",
      receiverId: "client-002",
      text: "Don't worry, we'll prepare you thoroughly. They typically ask about your course, why you chose this university, your future plans, and how you'll fund your education.",
      timestamp: "2023-05-16T14:15:00",
      status: "read"
    },
    {
      id: "msg-104",
      senderId: "client-002",
      receiverId: "counselor-001",
      text: "When is my interview scheduled?",
      timestamp: "2023-05-17T16:45:00",
      status: "read"
    }
  ],
  "client-003": [
    {
      id: "msg-201",
      senderId: "counselor-001",
      receiverId: "client-003",
      text: "Hello Rajat, I've reviewed your H1-B application and everything looks good so far. We need to finalize your employer documentation.",
      timestamp: "2023-05-15T11:00:00",
      status: "read"
    },
    {
      id: "msg-202",
      senderId: "client-003",
      receiverId: "counselor-001",
      text: "Great to hear that! My employer has sent me the job offer letter and employment contract. I'll upload them now.",
      timestamp: "2023-05-15T11:15:00",
      status: "read",
      attachments: [
        {
          name: "Job_Offer_Letter.pdf",
          url: "/documents/offer.pdf",
          type: "application/pdf"
        },
        {
          name: "Employment_Contract.pdf",
          url: "/documents/contract.pdf",
          type: "application/pdf"
        }
      ]
    },
    {
      id: "msg-203",
      senderId: "counselor-001",
      receiverId: "client-003",
      text: "Perfect! I've received the documents and they look excellent. Let me know if you have any questions about the next steps.",
      timestamp: "2023-05-15T11:30:00",
      status: "read"
    },
    {
      id: "msg-204",
      senderId: "client-003",
      receiverId: "counselor-001",
      text: "Thank you for your help with the documents",
      timestamp: "2023-05-17T13:20:00",
      status: "read"
    }
  ],
  "client-004": [
    {
      id: "msg-301",
      senderId: "counselor-001",
      receiverId: "client-004",
      text: "Hi Meera, I've reviewed your profile and I believe you have a strong case for an O-1 visa given your artistic achievements.",
      timestamp: "2023-05-14T15:30:00",
      status: "read"
    },
    {
      id: "msg-302",
      senderId: "client-004",
      receiverId: "counselor-001",
      text: "Thank you, I'm glad to hear that. What kind of evidence would I need to provide for my achievements?",
      timestamp: "2023-05-14T15:45:00",
      status: "read"
    },
    {
      id: "msg-303",
      senderId: "counselor-001",
      receiverId: "client-004",
      text: "For an O-1 visa, we need to demonstrate your extraordinary ability in the arts. This includes press coverage, awards, exhibition catalogs, and testimonials from experts in your field.",
      timestamp: "2023-05-14T16:00:00",
      status: "read"
    },
    {
      id: "msg-304",
      senderId: "client-004",
      receiverId: "counselor-001",
      text: "Do I need any additional documents?",
      timestamp: "2023-05-16T10:15:00",
      status: "read"
    }
  ]
};

// Quick Response Templates
const quickResponses = [
  "Thank you for uploading your documents. I'll review them shortly.",
  "Your documents have been received and approved.",
  "Please provide additional documentation for verification.",
  "Your visa application is progressing well. The next step is...",
  "I've scheduled a consultation call for us on [DATE]. Does this work for you?",
  "Don't hesitate to reach out if you have any questions or concerns.",
  "I'm currently reviewing your case and will get back to you within 24 hours.",
  "Please complete your profile information for faster processing."
];

// Define attachment interface
interface Attachment {
  filename: string;
  originalName: string;
  url: string;
  size: number;
  mimetype: string;
}

export default function CommunicationsPage() {
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [pendingAttachment, setPendingAttachment] = useState<File | null>(null);
  const [attachmentPreview, setAttachmentPreview] = useState<string | null>(null);
  const [socket, setSocket] = useState<Socket | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Quick responses for common queries
  const quickResponses = [
    "Could you please upload your latest documents?",
    "Your appointment is scheduled for tomorrow at 10:00 AM.",
    "I've reviewed your documents and everything looks good.",
    "Please provide additional information about your work experience.",
    "Would you like to schedule a call to discuss your application?",
  ];
  
  // Filter clients based on search term
  const filteredClients = mockClients.filter(client => 
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Initialize conversation when a client is selected
  useEffect(() => {
    if (selectedClient) {
      const conversation = mockConversations[selectedClient] || [];
      setMessages(conversation);
      // Mark messages as read
      const updatedConversation = conversation.map(msg => {
        if (msg.senderId !== "counselor-001") {
          return { ...msg, status: 'read' as const };
        }
        return msg;
      });
      
      mockConversations[selectedClient] = updatedConversation;
    }
  }, [selectedClient]);
  
  // Scroll to bottom when messages update
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);
  
  // Socket connection setup
  useEffect(() => {
    const initializeSocket = async () => {
      try {
        // In a real app, you would connect to your actual server
        // const socketInstance = io("https://api.visamanager.com");
        
        // For demo purposes, we'll just simulate the socket behavior
        console.log("Socket connection would be initialized here");
        
        // Simulated socket
        const mockSocket = {
          emit: (event: string, data: any) => {
            console.log(`Emitting event: ${event}`, data);
            
            // Simulate message sent confirmation
            if (event === "send_message") {
              setTimeout(() => {
                // Update message status to delivered
                setMessages(prev => 
                  prev.map(msg => 
                    msg.id === data.id ? { ...msg, status: 'delivered' } : msg
                  )
                );
              }, 1000);
            }
          },
          on: (event: string, callback: Function) => {
            console.log(`Registered listener for event: ${event}`);
          },
          disconnect: () => {
            console.log("Socket disconnected");
          }
        };
        
        // setSocket(socketInstance); // In a real app
        setSocket(mockSocket as any);
        
        return () => {
          // socketInstance.disconnect(); // In a real app
          mockSocket.disconnect();
        };
      } catch (error) {
        console.error("Failed to connect to socket:", error);
      }
    };
    
    initializeSocket();
  }, []);

  // Handle search
  const handleSearch = () => {
    // Filter clients based on search term
    console.log("Searching for:", searchTerm);
  };
  
  // Upload attachment function
  async function uploadAttachment(file: File): Promise<Attachment | null> {
    // In a real app, this would upload to your server
    try {
      console.log(`Uploading file: ${file.name} (${file.size} bytes)`);
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock successful upload
      const attachment: Attachment = {
        filename: `${Date.now()}_${file.name.replace(/\s+/g, '_')}`,
        originalName: file.name,
        url: URL.createObjectURL(file), // In a real app, this would be the server URL
        size: file.size,
        mimetype: file.type
      };
      
      return attachment;
    } catch (error) {
      console.error("Error uploading file:", error);
      return null;
    }
  }

  // Handle send message
  const handleSendMessage = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if ((!newMessage.trim() && !pendingAttachment) || !selectedClient) return;
    
    const now = new Date();
    const messageId = `msg-${Date.now()}`;
    
    let attachments;
    
    if (pendingAttachment) {
      const uploaded = await uploadAttachment(pendingAttachment);
      
      if (uploaded) {
        attachments = [{
          name: uploaded.originalName,
          url: uploaded.url,
          type: pendingAttachment.type
        }];
      }
      
      // Clear attachment state
      setPendingAttachment(null);
      setAttachmentPreview(null);
    }
    
    const newMsg = {
      id: messageId,
        senderId: "counselor-001",
        receiverId: selectedClient,
      text: newMessage,
      timestamp: now.toISOString(),
      status: 'sent' as const,
      ...(attachments && { attachments }),
    };
    
    // Update local state immediately
    setMessages(prev => [...prev, newMsg]);
    setNewMessage("");
    
    // Update mock conversation store
    mockConversations[selectedClient] = [
      ...(mockConversations[selectedClient] || []),
      newMsg
    ];
    
    // Update the last message preview in client list
    const clientIndex = mockClients.findIndex(c => c.id === selectedClient);
    if (clientIndex !== -1) {
      mockClients[clientIndex] = {
        ...mockClients[clientIndex],
        lastMessage: newMessage || "Attachment",
        lastMessageTime: "Just now",
        unreadCount: 0
      };
    }
    
    // Emit to socket
    if (socket) {
      socket.emit("send_message", newMsg);
    }
  };
  
  // Handle file selection for attachment
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setPendingAttachment(file);
      setAttachmentPreview(URL.createObjectURL(file));
    }
  };
  
  // Cancel attachment
  const handleCancelAttachment = () => {
    setPendingAttachment(null);
    setAttachmentPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };
  
  // Handle typing indicator
  const handleTyping = (isActive: boolean) => {
    setIsTyping(isActive);
    
    if (socket && selectedClient) {
      socket.emit("typing", {
        senderId: "counselor-001",
        receiverId: selectedClient,
      isTyping: isActive
    });
    }
  };
  
  // Function to get status icon for messages
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Clock className="h-3 w-3 text-indigo-400" />;
      case 'delivered':
        return <CheckCircle className="h-3 w-3 text-indigo-400" />;
      case 'read':
        return <CheckCircle className="h-3 w-3 text-indigo-600" />;
      default:
        return null;
    }
  };
  
  // Function to handle quick responses
  const handleSelectQuickResponse = (response: string) => {
    setNewMessage(response);
  };
  
  // Helper to format message date
  const formatMessageDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ' at ' + 
        date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 md:gap-0">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Communications
          </h1>
          <p className="text-gray-600 mt-1">Chat with clients and manage visa application communication</p>
        </div>
      </div>
      
      <div className="flex flex-col h-[calc(100vh-220px)] rounded-xl shadow-lg overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-4 h-full">
          {/* Contacts Panel */}
          <div className="lg:col-span-1 border-r border-indigo-100/30 bg-white/90 backdrop-blur-lg">
            <div className="p-4 border-b border-indigo-100/30">
            <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-indigo-400" size={18} />
              <input
                type="text"
                  placeholder="Search contacts..."
                  className="pl-10 pr-4 py-2 border border-indigo-200/60 rounded-lg w-full bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyUp={(e) => e.key === "Enter" && handleSearch()}
                />
            </div>
          </div>
          
            <div className="overflow-y-auto h-[calc(100vh-320px)]">
              <div className="p-3 bg-indigo-50/50 text-xs font-medium text-indigo-600 uppercase">
                Recent Conversations
              </div>
              <ul className="divide-y divide-indigo-100/30">
                {filteredClients.map((client) => (
                  <li 
                  key={client.id}
                    className={`cursor-pointer transition-all duration-200 ${
                      selectedClient === client.id 
                        ? 'bg-indigo-50/70 border-l-4 border-l-indigo-700' 
                        : 'hover:bg-indigo-50/30 border-l-4 border-l-transparent'
                  }`}
                  onClick={() => setSelectedClient(client.id)}
                >
                    <div className="p-3">
                      <div className="flex items-center">
                        <div className="relative">
                          <div className="h-12 w-12 rounded-full bg-gradient-to-br from-indigo-400 to-blue-300 flex items-center justify-center text-white font-medium shadow-sm">
                            {client.name.split(' ').map(n => n[0]).join('')}
                    </div>
                          <span 
                            className={`absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white ${
                      client.status === 'online' ? 'bg-green-500' : 
                              client.status === 'busy' ? 'bg-amber-500' : 
                              'bg-gray-400'
                            }`}
                          />
                  </div>
                        <div className="ml-3 flex-1 min-w-0">
                          <div className="flex justify-between">
                            <span className="text-sm font-medium text-indigo-800 truncate">{client.name}</span>
                            <span className="text-xs text-gray-500">{client.lastMessageTime}</span>
                    </div>
                          <p className="text-xs text-indigo-600 truncate">{client.lastMessage}</p>
                  </div>
                  {client.unreadCount > 0 && (
                          <div className="ml-2 bg-gradient-to-r from-blue-600 to-indigo-700 text-white text-xs font-medium h-5 w-5 flex items-center justify-center rounded-full">
                      {client.unreadCount}
                    </div>
            )}
          </div>
          
                      <div className="mt-1.5 flex items-center justify-between">
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100/70 text-indigo-800">
                          {client.visaType}
                        </span>
                        <span className="text-xs text-indigo-600">
                          {client.stage}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
          </div>
        </div>
        
          {/* Chat Panel */}
          <div className="lg:col-span-3 flex flex-col bg-gradient-to-br from-blue-50 via-white to-indigo-50">
          {selectedClient ? (
            <>
                {/* Chat Header */}
                <div className="bg-white/80 backdrop-blur-lg border-b border-indigo-100/30 p-4">
                  <div className="flex justify-between items-center">
                <div className="flex items-center">
                      <div className="relative">
                        <div className="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-400 to-blue-300 flex items-center justify-center text-white font-medium shadow-sm">
                          {mockClients.find(c => c.id === selectedClient)?.name.split(' ').map(n => n[0]).join('')}
                  </div>
                        <span 
                          className={`absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-white ${
                            mockClients.find(c => c.id === selectedClient)?.status === 'online' ? 'bg-green-500' : 
                            mockClients.find(c => c.id === selectedClient)?.status === 'busy' ? 'bg-amber-500' : 
                            'bg-gray-400'
                          }`}
                        />
                    </div>
                      <div className="ml-3">
                        <h2 className="text-md font-semibold text-indigo-800">{mockClients.find(c => c.id === selectedClient)?.name}</h2>
                        <div className="flex items-center">
                          <span className="text-xs text-indigo-600">{mockClients.find(c => c.id === selectedClient)?.visaType}</span>
                          <span className="mx-2 text-indigo-300">•</span>
                          <span className="text-xs text-gray-500">{mockClients.find(c => c.id === selectedClient)?.status === 'online' ? 'Online' : `Last active: ${mockClients.find(c => c.id === selectedClient)?.lastActive}`}</span>
                    </div>
                  </div>
                </div>
                
                    <div className="flex gap-2">
                      <button className="p-1.5 bg-indigo-50/70 backdrop-blur-sm rounded-lg text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                        <Phone className="h-5 w-5" />
                  </button>
                      <button className="p-1.5 bg-indigo-50/70 backdrop-blur-sm rounded-lg text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                        <Video className="h-5 w-5" />
                  </button>
                      <button className="p-1.5 bg-indigo-50/70 backdrop-blur-sm rounded-lg text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                        <Calendar className="h-5 w-5" />
                  </button>
                      <button className="p-1.5 bg-indigo-50/70 backdrop-blur-sm rounded-lg text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                        <FileText className="h-5 w-5" />
                      </button>
                      <button className="p-1.5 bg-indigo-50/70 backdrop-blur-sm rounded-lg text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                        <MoreVertical className="h-5 w-5" />
                  </button>
                    </div>
                </div>
              </div>
              
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {messages.map((message, index) => {
                    const isFromMe = message.senderId === "counselor-001";
                    return (
                      <div 
                        key={message.id}
                        className={`flex ${isFromMe ? 'justify-end' : 'justify-start'}`}
                      >
                        <div 
                          className={`rounded-xl max-w-[80%] shadow-sm overflow-hidden
                            ${isFromMe 
                              ? 'bg-gradient-to-br from-blue-600 to-indigo-700 text-white shadow-md' 
                              : 'bg-white/80 backdrop-blur-lg border border-indigo-100/30 text-indigo-800'
                            }
                          `}
                        >
                          <div className="p-3">
                            <div>{message.text}</div>
                            
                            {message.attachments && message.attachments.length > 0 && (
                              <div className="mt-2">
                                {message.attachments.map((attachment: any, i: number) => (
                                  <div 
                                    key={i}
                                    className={`flex items-center p-2 rounded-lg ${
                                      isFromMe ? 'bg-indigo-800/30' : 'bg-indigo-50/70'
                                    }`}
                                  >
                                    <div className="p-2 rounded-lg bg-white/20 mr-3">
                                      <FileText className={`h-5 w-5 ${isFromMe ? 'text-white' : 'text-indigo-600'}`} />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <p className={`text-sm font-medium truncate ${isFromMe ? 'text-white' : 'text-indigo-800'}`}>
                                        {attachment.name}
                                      </p>
                                    </div>
                                    <a 
                              href={attachment.url}
                              target="_blank"
                                      rel="noopener noreferrer"
                                      className={`ml-2 p-1 rounded-md ${
                                        isFromMe ? 'bg-white/20 hover:bg-white/30' : 'bg-indigo-100/70 hover:bg-indigo-200/70'
                                      } transition-colors`}
                                    >
                                      <ArrowUpCircle className={`h-4 w-4 ${isFromMe ? 'text-white' : 'text-indigo-600'}`} />
                                    </a>
                                  </div>
                          ))}
                        </div>
                      )}
                      
                            <div className={`text-xs mt-1 flex justify-end items-center gap-1 
                              ${isFromMe ? 'text-indigo-200' : 'text-gray-500'}`}
                            >
                              {formatMessageDate(message.timestamp)}
                              {isFromMe && getStatusIcon(message.status)}
                      </div>
                    </div>
                  </div>
                      </div>
                    );
                  })}
                <div ref={messagesEndRef} />
              </div>
              
                {/* Attachment Preview */}
                {pendingAttachment && (
                  <div className="p-3 bg-white/80 backdrop-blur-lg border-t border-indigo-100/30">
                    <div className="flex items-center justify-between p-2 rounded-lg bg-indigo-50/70">
                      <div className="flex items-center">
                        <div className="p-2 rounded-lg bg-indigo-100/70 mr-3">
                          <FileText className="h-5 w-5 text-indigo-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-indigo-800">
                            {pendingAttachment.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {(pendingAttachment.size / 1024).toFixed(1)} KB
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={handleCancelAttachment}
                        className="p-1.5 bg-red-50/70 rounded-lg text-red-600 hover:bg-red-100/70 transition-colors"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
                
                {/* Quick Responses */}
                <div className="px-3 py-2 bg-white/80 backdrop-blur-lg border-t border-indigo-100/30 overflow-x-auto">
                  <div className="flex gap-2">
                    {quickResponses.map((response, index) => (
                      <button
                        key={index}
                        className="px-3 py-1.5 bg-indigo-50/70 hover:bg-indigo-100/70 text-indigo-700 rounded-full text-sm whitespace-nowrap transition-colors"
                        onClick={() => handleSelectQuickResponse(response)}
                      >
                        {response.length > 30 ? response.substring(0, 27) + '...' : response}
                      </button>
                    ))}
                  </div>
                </div>
                
                {/* Message Input */}
                <div className="bg-white/80 backdrop-blur-lg border-t border-indigo-100/30 p-3">
                  <form onSubmit={handleSendMessage} className="flex items-end gap-2">
                    <button 
                      type="button" 
                      className="p-2 text-indigo-600 hover:text-indigo-800 transition-colors"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Paperclip className="h-5 w-5" />
                    </button>
                      <input
                        type="file"
                      ref={fileInputRef}
                        className="hidden"
                        onChange={handleFileChange}
                    />
                    <div className="flex-1 rounded-lg border border-indigo-200/60 bg-white/80 backdrop-blur-sm focus-within:ring-2 focus-within:ring-indigo-500 focus-within:border-transparent">
                      <textarea
                        className="w-full px-3 py-2 text-sm text-indigo-800 bg-transparent outline-none resize-none"
                        placeholder="Type your message..."
                        rows={1}
                        value={newMessage}
                      onChange={(e) => {
                          setNewMessage(e.target.value);
                        handleTyping(e.target.value.length > 0);
                      }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            if (newMessage.trim() || pendingAttachment) {
                              handleSendMessage(e as any);
                            }
                          }
                        }}
                      />
                    </div>
                  <button
                    type="submit"
                      disabled={!newMessage.trim() && !pendingAttachment}
                      className={`p-2.5 rounded-lg ${
                        newMessage.trim() || pendingAttachment
                          ? 'bg-gradient-to-r from-blue-600 to-indigo-700 text-white shadow-md hover:from-blue-700 hover:to-indigo-800'
                          : 'bg-indigo-100/70 text-indigo-400'
                      } transition-colors`}
                    >
                      <Send className="h-5 w-5" />
                  </button>
                </form>
              </div>
            </>
          ) : (
              <div className="flex-1 flex flex-col items-center justify-center p-6">
                <div className="mb-4">
                  <div className="h-16 w-16 bg-indigo-100/70 rounded-full flex items-center justify-center mb-3">
                    <MessageSquare className="h-8 w-8 text-indigo-600" />
            </div>
        </div>
                <h2 className="text-lg font-semibold text-indigo-800 mb-2">Select a conversation</h2>
                <p className="text-gray-600 text-center max-w-md mb-4">
                  Choose a client from the list to view your conversation history and send messages.
                </p>
            </div>
                  )}
                </div>
                </div>
      </div>
    </div>
  );
} 