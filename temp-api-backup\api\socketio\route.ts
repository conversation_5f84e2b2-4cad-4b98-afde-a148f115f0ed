import { NextRequest } from 'next/server';
import { Server as ServerIO } from 'socket.io';
import { createServer } from 'http';
import { NextApiResponseWithSocket } from '@/lib/socket';

// This route enables the Socket.IO server to work with Next.js
export async function GET(req: NextRequest, res: any) {
  // Create HTTP server if needed
  if (!res.socket.server.io) {
    // Adapt the NextApiResponse to be compatible with Socket.IO
    const httpServer = createServer();
    const io = new ServerIO(httpServer, {
      path: '/api/socketio',
      addTrailingSlash: false,
    });

    // Handle socket connections
    io.on('connection', (socket) => {
      console.log('New client connected:', socket.id);
      
      // Handle joining conversation rooms
      socket.on('join-conversation', (conversationId) => {
        socket.join(conversationId);
        console.log(`Client ${socket.id} joined conversation: ${conversationId}`);
      });
      
      // <PERSON>le leaving conversation rooms
      socket.on('leave-conversation', (conversationId) => {
        socket.leave(conversationId);
        console.log(`Client ${socket.id} left conversation: ${conversationId}`);
      });
      
      // Handle message sending
      socket.on('send-message', (message) => {
        const { conversationId } = message;
        io.to(conversationId).emit('new-message', message);
      });
      
      // Handle typing indicators
      socket.on('typing', ({ conversationId, userId, isTyping }) => {
        socket.to(conversationId).emit('user-typing', { userId, isTyping });
      });
      
      // Handle disconnections
      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
      });
    });

    // Attach to the Next.js server
    res.socket.server.io = io;
  }

  // Send a dummy response to make Next.js happy
  const responseBody = {
    success: true,
    message: 'Socket.IO server initialized',
  };

  return new Response(JSON.stringify(responseBody), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

export const dynamic = 'force-dynamic'; 