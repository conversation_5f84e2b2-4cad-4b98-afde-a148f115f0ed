"use client";

import { ArrowUpRight, ArrowDownRight } from "lucide-react";

export interface Executive {
  id: string;
  name: string;
  avatar: string;
  metrics: {
    conversions: number;
    revenue: string;
    satisfaction: number;
  };
  status: 'exceeding' | 'meeting' | 'below';
  lastActivity: string;
  workload: number;
}

interface TeamOverviewProps {
  executives: Executive[];
}

export function TeamOverview({ executives }: TeamOverviewProps) {
  return (
    <div className="space-y-4">
      {executives.map((executive) => (
        <div 
          key={executive.id} 
          className="p-4 rounded-lg border border-indigo-100/30 bg-white/70 backdrop-blur-sm hover:bg-white/80 transition-all duration-300 hover:shadow-md transform hover:translate-y-[-2px]"
        >
          <div className="flex items-center gap-3 mb-2">
            <div className="h-10 w-10 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white font-medium shadow-sm">
              {executive.name.split(' ').map(n => n[0]).join('')}
            </div>
            <div>
              <p className="font-medium text-indigo-800">{executive.name}</p>
              <p className="text-xs text-gray-600">Active {executive.lastActivity}</p>
            </div>
            <span 
              className={`ml-auto text-xs font-medium rounded-full px-2.5 py-1 backdrop-blur-sm ${
                executive.status === 'exceeding' ? 'bg-green-100/70 text-green-800' :
                executive.status === 'meeting' ? 'bg-blue-100/70 text-blue-800' :
                'bg-orange-100/70 text-orange-800'
              }`}
            >
              {executive.status === 'exceeding' ? 'Exceeding' :
               executive.status === 'meeting' ? 'On Target' :
               'Below Target'}
            </span>
          </div>
          
          <div className="grid grid-cols-3 gap-2 mt-3">
            <div className="text-center p-2 rounded bg-indigo-50/50 backdrop-blur-sm">
              <p className="text-xs text-indigo-600">Conversions</p>
              <p className="font-medium text-indigo-800">{executive.metrics.conversions}</p>
            </div>
            <div className="text-center p-2 rounded bg-indigo-50/50 backdrop-blur-sm">
              <p className="text-xs text-indigo-600">Revenue</p>
              <p className="font-medium text-indigo-800">{executive.metrics.revenue}</p>
            </div>
            <div className="text-center p-2 rounded bg-indigo-50/50 backdrop-blur-sm">
              <p className="text-xs text-indigo-600">Satisfaction</p>
              <p className="font-medium text-indigo-800">{executive.metrics.satisfaction}★</p>
            </div>
          </div>
          
          <div className="mt-3">
            <div className="flex justify-between text-xs mb-1">
              <span className="text-gray-600">Workload</span>
              <span className="font-medium text-indigo-800">
                {executive.workload} leads
                {executive.workload >= 15 ? 
                  <span className="text-amber-600 ml-1">(High)</span> : 
                  executive.workload <= 8 ? 
                  <span className="text-blue-600 ml-1">(Low)</span> : null
                }
              </span>
            </div>
            <div className="w-full bg-indigo-100/40 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  executive.workload >= 15 ? 'bg-gradient-to-r from-amber-500 to-orange-400' :
                  executive.workload >= 12 ? 'bg-gradient-to-r from-blue-500 to-indigo-400' :
                  'bg-gradient-to-r from-green-500 to-emerald-400'
                }`}
                style={{ width: `${(executive.workload / 20) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
} 