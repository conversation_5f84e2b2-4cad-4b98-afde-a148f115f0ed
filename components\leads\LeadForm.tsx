'use client';

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckCircle, X } from "lucide-react";

interface LeadFormProps {
  isOpen: boolean;
  onClose: () => void;
  defaultService?: string; // Optional pre-selected service
  source?: string; // Where this lead came from (page path, button, etc.)
  formType?: 'consultation' | 'counseling' | 'mock-interview' | 'general'; // Type of form
}

// Visa/Service types available for selection
const serviceTypes = [
  "General Inquiry",
  "EB-1 Visa",
  "O-1 Visa",
  "H-1B Visa",
  "Student Visa",
  "Tourist Visa",
  "Business Visa",
  "EB-5 Visa",
  "J-1 Visa",
  "Family/Spouse Visa",
  "Research Paper Publishing",
  "University Application",
  "Mock Interview",
  "Profile Building"
];

const LeadForm: React.FC<LeadFormProps> = ({ 
  isOpen, 
  onClose, 
  defaultService = "General Inquiry",
  source = "Website",
  formType = 'general'
}) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    mobile: "",
    service: defaultService,
    message: "",
    priority: "medium",
    referralSource: source
  });
  
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  if (!isOpen) return null;

  // Get form title based on type
  const getFormTitle = () => {
    switch(formType) {
      case 'consultation': return 'Book a Consultation';
      case 'counseling': return 'Book a Counseling Session';
      case 'mock-interview': return 'Book a Mock Interview';
      default: return 'Get Expert Assistance';
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormData({
      ...formData,
      [id]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");
    
    try {
      // Call our API endpoint
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Show success message
        setFormSubmitted(true);
        
        // Auto-close after 3 seconds
        setTimeout(() => {
          onClose();
          setFormSubmitted(false);
          setFormData({
            name: "",
            email: "",
            mobile: "",
            service: defaultService,
            message: "",
            priority: "medium",
            referralSource: source
          });
        }, 3000);
      } else {
        // Handle error
        setError(result.message || 'Something went wrong');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-md overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-5 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">{getFormTitle()}</h2>
          <button 
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Form Content */}
        <div className="p-6">
          {formSubmitted ? (
            <div className="text-center py-8">
              <div className="flex justify-center mb-4">
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="h-10 w-10 text-green-600" />
                </div>
              </div>
              <h3 className="text-xl font-medium text-green-800 mb-2">Thank You!</h3>
              <p className="text-green-700">
                Your request has been received. One of our visa experts will contact you shortly.
              </p>
            </div>
          ) : (
            <form className="space-y-4" onSubmit={handleSubmit}>
              {error && (
                <div className="p-3 bg-red-50 border-l-4 border-red-500 text-red-700 text-sm">
                  {error}
                </div>
              )}
              
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Name*
                </label>
                <Input 
                  id="name" 
                  placeholder="Your name" 
                  required 
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1">
                  Email*
                </label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="Your email" 
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </div>
              
              <div>
                <label htmlFor="mobile" className="block text-sm font-medium mb-1">
                  Mobile*
                </label>
                <Input 
                  id="mobile" 
                  placeholder="Your mobile number" 
                  required
                  value={formData.mobile}
                  onChange={handleInputChange}
                />
              </div>
              
              <div>
                <label htmlFor="service" className="block text-sm font-medium mb-1">
                  Service Interested In*
                </label>
                <select
                  id="service"
                  className="w-full px-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={formData.service}
                  onChange={handleInputChange}
                  required
                >
                  {serviceTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label htmlFor="message" className="block text-sm font-medium mb-1">
                  Message
                </label>
                <Textarea 
                  id="message" 
                  placeholder="Please provide details about your inquiry" 
                  rows={3} 
                  value={formData.message}
                  onChange={handleInputChange}
                />
              </div>
              
              <div>
                <label htmlFor="priority" className="block text-sm font-medium mb-1">
                  How soon do you need assistance?
                </label>
                <select
                  id="priority"
                  className="w-full px-3 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={formData.priority}
                  onChange={handleInputChange}
                >
                  <option value="low">Within the next few months</option>
                  <option value="medium">Within the next few weeks</option>
                  <option value="high">Urgently (within days)</option>
                </select>
              </div>
              
              <div className="flex items-start space-x-2 mt-4">
                <Checkbox id="consent" required />
                <label htmlFor="consent" className="text-sm text-gray-600">
                  I agree to receive communications about visa services. I understand my data will be processed in accordance with the Privacy Policy.
                </label>
              </div>
              
              <Button 
                type="submit" 
                className="w-full bg-indigo-600 hover:bg-indigo-700"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Inquiry'}
              </Button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default LeadForm; 