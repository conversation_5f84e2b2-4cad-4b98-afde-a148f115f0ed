"use client"

import { useState } from "react"
import { BookingForm } from "@/components/services/booking-form"
import { ServiceHeader } from "@/components/services/service-header"
import { ServiceFeatures } from "@/components/services/service-features"
import { ServiceProcess } from "@/components/services/service-process"
import { ServiceTestimonials } from "@/components/services/service-testimonials"
import { ServiceFAQ } from "@/components/services/service-faq"
import { MessageCircle, Users, FileText, Calendar, CheckCircle } from "lucide-react"

const features = [
  {
    title: "Personalized Guidance",
    description: "One-on-one counseling sessions tailored to your specific needs and goals.",
    icon: <MessageCircle className="w-6 h-6" />,
  },
  {
    title: "Expert Consultation",
    description: "Get advice from experienced visa consultants with proven track records.",
    icon: <Users className="w-6 h-6" />,
  },
  {
    title: "Document Review",
    description: "Thorough review of your documents to ensure they meet all requirements.",
    icon: <FileText className="w-6 h-6" />,
  },
  {
    title: "Timeline Planning",
    description: "Strategic planning to help you meet all deadlines and requirements.",
    icon: <Calendar className="w-6 h-6" />,
  },
]

const processSteps = [
  {
    title: "Initial Consultation",
    description: "Schedule a free initial consultation to discuss your visa goals and requirements.",
    icon: <MessageCircle className="w-6 h-6" />,
  },
  {
    title: "Document Assessment",
    description: "Our experts will review your documents and provide detailed feedback.",
    icon: <FileText className="w-6 h-6" />,
  },
  {
    title: "Strategy Development",
    description: "We'll create a personalized strategy to maximize your chances of success.",
    icon: <CheckCircle className="w-6 h-6" />,
  },
  {
    title: "Ongoing Support",
    description: "Receive continuous guidance and support throughout your visa application process.",
    icon: <Users className="w-6 h-6" />,
  },
]

const testimonials = [
  {
    name: "Sarah Johnson",
    role: "Student Visa Applicant",
    content: "The counseling service was incredibly helpful. They guided me through every step of my student visa application process.",
    rating: 5,
  },
  {
    name: "Michael Chen",
    role: "Work Visa Applicant",
    content: "Professional and knowledgeable team. They helped me understand the complex requirements and prepare a strong application.",
    rating: 5,
  },
  {
    name: "Emma Rodriguez",
    role: "Family Visa Applicant",
    content: "I'm grateful for their expertise and support. They made the visa application process much less stressful.",
    rating: 5,
  },
]

const faqs = [
  {
    question: "What documents do I need for the initial consultation?",
    answer: "For the initial consultation, you'll need your passport, current visa status (if any), and any relevant documents related to your visa application. We'll provide a detailed checklist during the consultation.",
  },
  {
    question: "How long does the counseling process take?",
    answer: "The duration varies depending on your specific case and visa type. Typically, the initial consultation takes 1-2 hours, and the complete process can range from a few weeks to several months.",
  },
  {
    question: "Do you offer remote counseling sessions?",
    answer: "Yes, we offer both in-person and remote counseling sessions via video conferencing. This allows us to serve clients worldwide while maintaining the same level of quality service.",
  },
  {
    question: "What makes your counseling service different?",
    answer: "Our service stands out due to our personalized approach, experienced consultants, and comprehensive support throughout the entire process. We focus on understanding your unique situation and providing tailored solutions.",
  },
]

export default function CounselingPage() {
  const [open, setOpen] = useState(false)
  return (
    <main>
      <ServiceHeader
        title="Visa Counseling Services"
        description="Get expert guidance and support for your visa application process. Our experienced consultants will help you navigate the complexities of visa requirements and maximize your chances of success."
        onGetStarted={() => setOpen(true)}
      />

      <BookingForm isOpen={open} onClose={() => setOpen(false)} />

      <ServiceFeatures
        features={features}
        title="Why Choose Our Counseling Service"
        description="Our comprehensive counseling service provides you with the guidance and support you need to successfully navigate the visa application process."
      />

      <ServiceProcess steps={processSteps} />
      <ServiceTestimonials testimonials={testimonials} />
      <ServiceFAQ faqs={faqs} />
    </main>
  )
} 