import { Prisma } from '@prisma/client';

// Extend JsonValue to be compatible with Record<string, any>
declare global {
  namespace PrismaJson {
    type JsonValue = string | number | boolean | null | JsonObject | JsonArray;
    interface JsonObject {
      [key: string]: JsonValue;
    }
    interface JsonArray extends Array<JsonValue> {}
  }
}

// Add module augmentation for Prisma namespace
declare namespace Prisma {
  // Make Json type compatible with Record<string, any>
  type Json = PrismaJson.JsonValue;
}

// Export augmented types
export {}; 