import { Star, Quote } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ReactNode } from "react"

interface GlassCardProps {
  children: ReactNode;
  className?: string;
}

function GlassCard({ children, className = "" }: GlassCardProps) {
  return (
    <div
      className={`rounded-xl p-4 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl ${className}`}
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {children}
    </div>
  )
}

export default function TestimonialsSection() {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON>",
      avatar: "/placeholder.svg?height=100&width=100",
      role: "Student, Stanford University",
      content: "<PERSON> <PERSON><PERSON> secured my visa slot and boosted my confidence for the interview. Highly recommended!",
      rating: 5,
    },
    {
      id: 2,
      name: "<PERSON><PERSON> Mehrotra",
      avatar: "/placeholder.svg?height=100&width=100",
      role: "Software Engineer, Canada",
      content: "The team's expertise made my application process seamless. I received my admit and scholarship!",
      rating: 5,
    },
  ]

  return (
    <section className="py-16">
      <div className="container max-w-7xl mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">Testimonials</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {testimonials.map((testimonial) => (
            <GlassCard key={testimonial.id} className="transform transition hover:translate-y-[-4px]">
              <div className="p-6">
                <div className="flex gap-1 mb-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < testimonial.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
                <div className="bg-indigo-50/50 rounded-lg p-4 mb-6 relative">
                  <Quote className="absolute top-2 left-2 h-4 w-4 text-indigo-300 opacity-50" />
                  <p className="text-sm text-gray-600 pl-6">"{testimonial.content}"</p>
                </div>
                <div className="flex items-center gap-4">
                  <Avatar className="h-10 w-10 border-2 border-indigo-100">
                    <AvatarImage src={testimonial.avatar || "/placeholder.svg"} alt={testimonial.name} />
                    <AvatarFallback className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
                      {testimonial.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="text-sm font-semibold text-indigo-800">{testimonial.name}</h4>
                    <p className="text-xs text-indigo-600">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            </GlassCard>
          ))}
        </div>
      </div>
    </section>
  )
}
