"use client";

import { useState } from "react";
import {
  Use<PERSON>,
  <PERSON>,
  Settings as SettingsIcon,
  Shield,
  Save,
  RefreshCw,
  Trash2,
  Lock,
  Mail,
  Phone,
  Check,
  X,
  Upload
} from "lucide-react";

export default function SettingsPage() {
  // State for different settings sections
  const [activeTab, setActiveTab] = useState("profile");
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // Profile settings state
  const [profileForm, setProfileForm] = useState({
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    position: "Senior Sales Manager",
    department: "Business Development",
    bio: "Over 8 years of experience in visa consultancy sales and team management. Specialized in business visa applications and corporate partnerships.",
    avatar: null
  });
  
  // Notification settings state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    leadAssignments: true,
    teamPerformance: true,
    systemUpdates: false,
    dailyReports: true,
    weeklyReports: true,
    monthlyReports: true
  });
  
  // System settings state
  const [systemSettings, setSystemSettings] = useState({
    language: "english",
    timezone: "Asia/Kolkata",
    dateFormat: "DD/MM/YYYY",
    currencyFormat: "INR",
    autoAssignLeads: true,
    leadAssignmentAlgorithm: "round-robin",
    workloadLimit: 10,
    dataRetentionPeriod: 36,
    twoFactorAuth: false
  });
  
  // Function to handle profile form changes
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Function to handle notification toggle
  const handleNotificationToggle = (setting: string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: !prev[setting as keyof typeof prev]
    }));
  };
  
  // Function to handle system setting changes
  const handleSystemSettingChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value, type } = e.target;
    
    setSystemSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };
  
  // Save handler (simulated)
  const handleSave = async () => {
    setIsLoading(true);
    setSaveSuccess(false);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsLoading(false);
    setSaveSuccess(true);
    
    // Reset success message after 3 seconds
    setTimeout(() => {
      setSaveSuccess(false);
    }, 3000);
  };
  
  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-800">Settings</h1>
        <p className="text-gray-500 mt-1">Manage your account settings and preferences</p>
      </div>
      
      {/* Settings container */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="flex flex-col md:flex-row">
          {/* Sidebar */}
          <div className="w-full md:w-60 border-b md:border-b-0 md:border-r border-gray-200">
            <nav className="p-4">
              <div className="space-y-1">
                <button
                  onClick={() => setActiveTab("profile")}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
                    activeTab === "profile"
                      ? "bg-indigo-100 text-indigo-800 font-medium"
                      : "text-gray-600 hover:bg-indigo-50 hover:text-indigo-700"
                  }`}
                >
                  <User size={18} />
                  <span>Profile</span>
                </button>
                
                <button
                  onClick={() => setActiveTab("notifications")}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
                    activeTab === "notifications"
                      ? "bg-indigo-100 text-indigo-800 font-medium"
                      : "text-gray-600 hover:bg-indigo-50 hover:text-indigo-700"
                  }`}
                >
                  <Bell size={18} />
                  <span>Notifications</span>
                </button>
                
                <button
                  onClick={() => setActiveTab("system")}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
                    activeTab === "system"
                      ? "bg-indigo-100 text-indigo-800 font-medium"
                      : "text-gray-600 hover:bg-indigo-50 hover:text-indigo-700"
                  }`}
                >
                  <SettingsIcon size={18} />
                  <span>System</span>
                </button>
                
                <button
                  onClick={() => setActiveTab("security")}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
                    activeTab === "security"
                      ? "bg-indigo-100 text-indigo-800 font-medium"
                      : "text-gray-600 hover:bg-indigo-50 hover:text-indigo-700"
                  }`}
                >
                  <Shield size={18} />
                  <span>Security</span>
                </button>
              </div>
            </nav>
          </div>
          
          {/* Content area */}
          <div className="flex-1 p-6">
            {/* Profile Settings */}
            {activeTab === "profile" && (
              <div className="space-y-6">
                <h2 className="text-xl font-medium text-gray-800">Profile Information</h2>
                
                <div className="flex flex-col md:flex-row gap-6">
                  {/* Avatar section */}
                  <div className="w-full md:w-1/4 flex flex-col items-center">
                    <div className="relative">
                      <div className="h-32 w-32 rounded-full bg-indigo-100 border border-indigo-200 flex items-center justify-center text-indigo-500 text-4xl font-bold">
                        {profileForm.name.split(" ").map(n => n[0]).join("")}
                      </div>
                      <button className="absolute bottom-0 right-0 bg-indigo-600 text-white p-2 rounded-full shadow-lg hover:bg-indigo-700">
                        <Upload size={16} />
                      </button>
                    </div>
                    <p className="text-sm text-gray-500 mt-3">
                      Upload a profile picture (JPG, PNG, max 2MB)
                    </p>
                  </div>
                  
                  {/* Form fields */}
                  <div className="flex-1 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                          Full Name
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={profileForm.name}
                          onChange={handleProfileChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
                          Position
                        </label>
                        <input
                          type="text"
                          id="position"
                          name="position"
                          value={profileForm.position}
                          onChange={handleProfileChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                          Email Address
                        </label>
                        <div className="flex">
                          <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                            <Mail size={16} />
                          </span>
                          <input
                            type="email"
                            id="email"
                            name="email"
                            value={profileForm.email}
                            onChange={handleProfileChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number
                        </label>
                        <div className="flex">
                          <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                            <Phone size={16} />
                          </span>
                          <input
                            type="tel"
                            id="phone"
                            name="phone"
                            value={profileForm.phone}
                            onChange={handleProfileChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                          Department
                        </label>
                        <select
                          id="department"
                          name="department"
                          value={profileForm.department}
                          onChange={handleProfileChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        >
                          <option value="Business Development">Business Development</option>
                          <option value="Sales">Sales</option>
                          <option value="Lead Generation">Lead Generation</option>
                          <option value="Account Management">Account Management</option>
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                        Bio
                      </label>
                      <textarea
                        id="bio"
                        name="bio"
                        rows={4}
                        value={profileForm.bio}
                        onChange={handleProfileChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      ></textarea>
                      <p className="text-sm text-gray-500 mt-1">Brief description about yourself for your team profile.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Notification Settings */}
            {activeTab === "notifications" && (
              <div className="space-y-6">
                <h2 className="text-xl font-medium text-gray-800">Notification Preferences</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-md font-medium text-gray-700 mb-3">Communication Channels</h3>
                    <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Email Notifications</div>
                          <div className="text-sm text-gray-500">Receive updates via email</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('emailNotifications')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.emailNotifications ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.emailNotifications ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Push Notifications</div>
                          <div className="text-sm text-gray-500">Receive in-app notifications</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('pushNotifications')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.pushNotifications ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.pushNotifications ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">SMS Notifications</div>
                          <div className="text-sm text-gray-500">Receive text messages for critical updates</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('smsNotifications')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.smsNotifications ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.smsNotifications ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-md font-medium text-gray-700 mb-3">Notification Types</h3>
                    <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Lead Assignments</div>
                          <div className="text-sm text-gray-500">When new leads are assigned to your team</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('leadAssignments')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.leadAssignments ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.leadAssignments ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Team Performance</div>
                          <div className="text-sm text-gray-500">Updates about your team's performance metrics</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('teamPerformance')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.teamPerformance ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.teamPerformance ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">System Updates</div>
                          <div className="text-sm text-gray-500">Notifications about system changes and maintenance</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('systemUpdates')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.systemUpdates ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.systemUpdates ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-md font-medium text-gray-700 mb-3">Report Frequency</h3>
                    <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Daily Reports</div>
                          <div className="text-sm text-gray-500">Receive daily summary reports</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('dailyReports')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.dailyReports ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.dailyReports ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Weekly Reports</div>
                          <div className="text-sm text-gray-500">Receive weekly summary reports</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('weeklyReports')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.weeklyReports ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.weeklyReports ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Monthly Reports</div>
                          <div className="text-sm text-gray-500">Receive monthly summary reports</div>
                        </div>
                        <button
                          onClick={() => handleNotificationToggle('monthlyReports')}
                          className={`w-11 h-6 relative inline-flex items-center rounded-full transition-colors ${
                            notificationSettings.monthlyReports ? 'bg-indigo-600' : 'bg-gray-300'
                          }`}
                        >
                          <span 
                            className={`${
                              notificationSettings.monthlyReports ? 'translate-x-6' : 'translate-x-1'
                            } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* System Settings */}
            {activeTab === "system" && (
              <div className="space-y-6">
                <h2 className="text-xl font-medium text-gray-800">System Preferences</h2>
                
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">
                        Language
                      </label>
                      <select
                        id="language"
                        name="language"
                        value={systemSettings.language}
                        onChange={handleSystemSettingChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="english">English</option>
                        <option value="hindi">Hindi</option>
                        <option value="gujarati">Gujarati</option>
                        <option value="marathi">Marathi</option>
                        <option value="tamil">Tamil</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 mb-1">
                        Timezone
                      </label>
                      <select
                        id="timezone"
                        name="timezone"
                        value={systemSettings.timezone}
                        onChange={handleSystemSettingChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="Asia/Kolkata">(GMT+5:30) India Standard Time</option>
                        <option value="Etc/GMT+0">(GMT+0:00) Greenwich Mean Time</option>
                        <option value="America/New_York">(GMT-5:00) Eastern Time</option>
                        <option value="America/Los_Angeles">(GMT-8:00) Pacific Time</option>
                        <option value="Australia/Sydney">(GMT+10:00) Australian Eastern Time</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="dateFormat" className="block text-sm font-medium text-gray-700 mb-1">
                        Date Format
                      </label>
                      <select
                        id="dateFormat"
                        name="dateFormat"
                        value={systemSettings.dateFormat}
                        onChange={handleSystemSettingChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="currencyFormat" className="block text-sm font-medium text-gray-700 mb-1">
                        Currency Format
                      </label>
                      <select
                        id="currencyFormat"
                        name="currencyFormat"
                        value={systemSettings.currencyFormat}
                        onChange={handleSystemSettingChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="INR">Indian Rupee (₹)</option>
                        <option value="USD">US Dollar ($)</option>
                        <option value="EUR">Euro (€)</option>
                        <option value="GBP">British Pound (£)</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t border-gray-200">
                    <h3 className="text-md font-medium text-gray-700 mb-3">Lead Management</h3>
                    
                    <div className="space-y-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="autoAssignLeads"
                          name="autoAssignLeads"
                          checked={systemSettings.autoAssignLeads}
                          onChange={handleSystemSettingChange}
                          className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <label htmlFor="autoAssignLeads" className="ml-3 block text-sm text-gray-700">
                          Automatically assign new leads to team members
                        </label>
                      </div>
                      
                      {systemSettings.autoAssignLeads && (
                        <div className="ml-7">
                          <label htmlFor="leadAssignmentAlgorithm" className="block text-sm font-medium text-gray-700 mb-1">
                            Assignment Algorithm
                          </label>
                          <select
                            id="leadAssignmentAlgorithm"
                            name="leadAssignmentAlgorithm"
                            value={systemSettings.leadAssignmentAlgorithm}
                            onChange={handleSystemSettingChange}
                            className="w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          >
                            <option value="round-robin">Round Robin</option>
                            <option value="load-balanced">Load Balanced</option>
                            <option value="skill-based">Skill Based</option>
                            <option value="geographic">Geographic</option>
                          </select>
                        </div>
                      )}
                      
                      <div>
                        <label htmlFor="workloadLimit" className="block text-sm font-medium text-gray-700 mb-1">
                          Max Leads Per Executive
                        </label>
                        <div className="w-full max-w-xs">
                          <input
                            type="number"
                            id="workloadLimit"
                            name="workloadLimit"
                            min="1"
                            max="50"
                            value={systemSettings.workloadLimit}
                            onChange={handleSystemSettingChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          />
                          <p className="text-sm text-gray-500 mt-1">Maximum number of active leads an executive can handle</p>
                        </div>
                      </div>
                      
                      <div>
                        <label htmlFor="dataRetentionPeriod" className="block text-sm font-medium text-gray-700 mb-1">
                          Data Retention Period (months)
                        </label>
                        <div className="w-full max-w-xs">
                          <input
                            type="number"
                            id="dataRetentionPeriod"
                            name="dataRetentionPeriod"
                            min="1"
                            max="84"
                            value={systemSettings.dataRetentionPeriod}
                            onChange={handleSystemSettingChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          />
                          <p className="text-sm text-gray-500 mt-1">How long to keep inactive lead data before archiving</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Security Settings */}
            {activeTab === "security" && (
              <div className="space-y-6">
                <h2 className="text-xl font-medium text-gray-800">Security Settings</h2>
                
                <div className="space-y-6">
                  <div className="p-4 rounded-lg bg-indigo-50 border border-indigo-100">
                    <h3 className="text-md font-medium text-indigo-800 flex items-center gap-2">
                      <Lock size={18} />
                      <span>Password Management</span>
                    </h3>
                    
                    <div className="mt-4 space-y-4">
                      <button className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Change Password
                      </button>
                      
                      <div className="text-sm text-gray-600">
                        <p>Your password was last changed 30 days ago.</p>
                        <p className="mt-1">Password requirements:</p>
                        <ul className="list-disc ml-5 mt-1 space-y-1 text-gray-600">
                          <li>Minimum 8 characters</li>
                          <li>At least one uppercase letter</li>
                          <li>At least one number</li>
                          <li>At least one special character</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 rounded-lg bg-gray-50 border border-gray-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-800 mb-3">Two-Factor Authentication</h3>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-600">
                              Add an extra layer of security to your account
                            </p>
                          </div>
                          <button className="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                            Enable
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 rounded-lg bg-gray-50 border border-gray-200">
                    <h3 className="text-md font-medium text-gray-800">Active Sessions</h3>
                    <p className="text-sm text-gray-600 mt-1">
                      These are the devices that are currently logged into your account.
                    </p>
                    
                    <div className="mt-4 space-y-3">
                      <div className="p-3 bg-white rounded border border-gray-200 flex items-center justify-between">
                        <div>
                          <div className="font-medium">Windows PC - Chrome</div>
                          <div className="text-xs text-gray-500">Mumbai, India • Current session</div>
                        </div>
                        <div className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                          Active Now
                        </div>
                      </div>
                      
                      <div className="p-3 bg-white rounded border border-gray-200 flex items-center justify-between">
                        <div>
                          <div className="font-medium">iPhone 12 - Safari</div>
                          <div className="text-xs text-gray-500">Mumbai, India • Last active 2 hours ago</div>
                        </div>
                        <button className="text-xs text-red-600 hover:text-red-800">
                          Sign Out
                        </button>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <button className="text-sm text-red-600 hover:text-red-800 font-medium">
                        Sign out from all other devices
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-4 rounded-lg bg-red-50 border border-red-100">
                    <h3 className="text-md font-medium text-red-800">Danger Zone</h3>
                    <p className="text-sm text-red-600 mt-1">
                      Destructive actions that cannot be undone.
                    </p>
                    
                    <div className="mt-4">
                      <button className="flex items-center gap-2 px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-100">
                        <Trash2 size={16} />
                        <span>Delete Account</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Save button */}
            <div className="mt-6 flex items-center justify-end gap-3 border-t border-gray-200 pt-6">
              {saveSuccess && (
                <div className="mr-auto flex items-center gap-2 text-green-600">
                  <Check size={18} />
                  <span>Settings saved successfully</span>
                </div>
              )}
              
              <button
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              
              <button
                onClick={handleSave}
                disabled={isLoading}
                className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-70"
              >
                {isLoading ? (
                  <>
                    <RefreshCw size={18} className="animate-spin" />
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <Save size={18} />
                    <span>Save Changes</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 