// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "./generated/client"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified DateTime?
  image         String?
  phone         String?
  country       String?
  status        String    @default("pending") // "active", "pending", "suspended"
  role          String    @default("user") // "user", "admin", "counselor"
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastLogin     DateTime?

  // Relations
  profile          Profile?
  visaApplications VisaApplication[]
  documents        Document[]
  bookings         Booking[]
  actionsPerformed AdminAction[]     @relation("AdminActions")
  actionsReceived  AdminAction[]     @relation("UserActions")
  sentMessages     Message[]         @relation("SentMessages")
  receivedMessages Message[]         @relation("ReceivedMessages")
}

model Profile {
  id             String    @id @default(cuid())
  userId         String    @unique
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  address        String?
  city           String?
  state          String?
  zipCode        String?
  dateOfBirth    DateTime?
  passportNumber String?
  passportExpiry DateTime?
  nationality    String?
  occupation     String?
  travelHistory  Json? // Array of previous travel details
  bio            String?   @db.Text
  preferences    Json? // User preferences as JSON
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
}

model VisaApplication {
  id                 String    @id @default(cuid())
  userId             String
  user               User      @relation(fields: [userId], references: [id])
  applicationType    String // "tourist", "business", "student", "work", "family", etc.
  destinationCountry String
  purpose            String?   @db.Text
  plannedArrival     DateTime?
  plannedDeparture   DateTime?
  status             String    @default("draft") // "draft", "submitted", "in_review", "pending_documents", "approved", "rejected"
  stage              Int       @default(1) // 1-8 for different stages in the visa process
  submissionDate     DateTime?
  decisionDate       DateTime?
  rejectionReason    String?   @db.Text
  processingTime     Int? // In days
  notes              String?   @db.Text
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  // Relations
  documents Document[]
}

model Document {
  id            String           @id @default(cuid())
  userId        String
  user          User             @relation(fields: [userId], references: [id])
  applicationId String?
  application   VisaApplication? @relation(fields: [applicationId], references: [id])
  filename      String
  originalName  String
  path          String
  size          Int
  mimetype      String
  documentType  String // "passport", "photo", "financials", "invitation", etc.
  status        String           @default("SUBMITTED") // "SUBMITTED", "APPROVED", "REJECTED"
  reviewNotes   String?          @db.Text
  reviewedBy    String?
  reviewedAt    DateTime?
  uploadedAt    DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
}

model Booking {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  counselorId String?
  name        String
  email       String
  phone       String
  country     String
  visaType    String
  date        DateTime
  time        String
  notes       String?  @db.Text
  status      String   @default("scheduled") // "scheduled", "completed", "cancelled", "no_show"
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model AdminAction {
  id        Int      @id @default(autoincrement())
  adminId   String
  admin     User     @relation("AdminActions", fields: [adminId], references: [id])
  userId    String
  user      User     @relation("UserActions", fields: [userId], references: [id])
  action    String // "STATUS_UPDATE", "DOC_APPROVAL", "VISA_OVERRIDE", etc.
  metadata  Json // Details of the action including before/after states, IP, device info
  createdAt DateTime @default(now())
}

model TravelRecommendation {
  id                     String   @id @default(cuid())
  destinationCountry     String
  attractionName         String
  attractionType         String
  image                  String
  address                String
  description            String   @db.Text
  historicalSignificance String   @db.Text
  bestTimeToVisit        String
  entranceFee            String
  openingHours           String
  website                String?
  localTips              String?  @db.Text
  weatherInfo            String?  @db.Text
  safetyTips             String?  @db.Text
  isActive               Boolean  @default(true)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
}

model Setting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String   @db.Text
  description String?
  updatedBy   String?
  updatedAt   DateTime @updatedAt
}

model Message {
  id          String   @id @default(cuid())
  content     String   @db.Text
  userId      String
  sender      String // "user", "agent", "system"
  read        Boolean  @default(false)
  attachments String?  @db.Text // JSON string of attachment objects
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // For direct messaging between users (e.g. counselor and client)
  senderId     String?
  senderUser   User?   @relation("SentMessages", fields: [senderId], references: [id])
  receiverId   String?
  receiverUser User?   @relation("ReceivedMessages", fields: [receiverId], references: [id])
}
