"use client";

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRouter } from 'next/navigation';
import { PROFILE_STEPS, useProfileCompletion } from '@/contexts/ProfileCompletionContext';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormDescription,
  FormMessage,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';

// Define the form schema with validation
const preferencesSchema = z.object({
  communicationPreference: z.enum(['email', 'phone', 'both'], {
    required_error: 'Please select a communication preference',
  }),
  receiveUpdates: z.boolean().default(true),
  receivePromotions: z.boolean().default(false),
});

type PreferencesFormValues = z.infer<typeof preferencesSchema>;

export default function PreferencesStep() {
  const router = useRouter();
  const { moveToPrevStep, markStepComplete, completeProfile } = useProfileCompletion();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with default values
  const form = useForm<PreferencesFormValues>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: {
      communicationPreference: 'email',
      receiveUpdates: true,
      receivePromotions: false,
    },
  });

  // Load user data when available
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch('/api/user/profile');
        if (response.ok) {
          const userData = await response.json();
          form.reset({
            communicationPreference: userData.communicationPreference || 'email',
            receiveUpdates: userData.receiveUpdates ?? true,
            receivePromotions: userData.receivePromotions ?? false,
          });
        }
      } catch (error) {
        console.error('Failed to fetch user data:', error);
      }
    };

    fetchUserData();
  }, [form]);

  // Handle form submission
  const onSubmit = async (data: PreferencesFormValues) => {
    setIsLoading(true);
    
    try {
      // Send data to API
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          communicationPreference: data.communicationPreference,
          receiveUpdates: data.receiveUpdates,
          receivePromotions: data.receivePromotions,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      // Mark this step as complete
      markStepComplete(PROFILE_STEPS.PREFERENCES);
      
      // Complete the entire profile
      await completeProfile();
      
      toast.success('Profile completed successfully!');
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/user/dashboard');
      }, 1500);
      
    } catch (error) {
      toast.error('Failed to save preferences');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Preferences</h2>
        <p className="text-muted-foreground">
          Set your communication and notification preferences.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="communicationPreference"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Preferred Communication Method</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="email" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Email
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="phone" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Phone
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="both" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Both Email and Phone
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="receiveUpdates"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    Receive Status Updates
                  </FormLabel>
                  <FormDescription>
                    Receive notifications about your visa application status.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="receivePromotions"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    Promotional Communications
                  </FormLabel>
                  <FormDescription>
                    Receive promotional offers and news about our services.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          
          <div className="flex justify-between pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={moveToPrevStep}
            >
              Previous
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Completing Profile..." : "Complete Profile"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
} 