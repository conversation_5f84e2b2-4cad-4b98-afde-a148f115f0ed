import { ReactNode } from 'react';

// Augment Next.js type definitions for Link component
declare module 'next/link' {
  export interface LinkProps {
    href: string | URL;
    as?: string;
    replace?: boolean;
    scroll?: boolean;
    shallow?: boolean;
    passHref?: boolean;
    prefetch?: boolean;
    locale?: string | false;
    legacyBehavior?: boolean;
    onMouseEnter?: (e: any) => void;
    onTouchStart?: (e: any) => void;
    onClick?: (e: any) => void;
    children?: ReactNode;
  }
} 