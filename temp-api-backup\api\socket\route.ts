import { NextRequest, NextResponse } from 'next/server';
import { initSocketServer, NextApiResponseWithSocket } from '@/lib/socket';

export async function GET(req: NextRequest, res: any) {
  try {
    // Initialize Socket.IO server
    initSocketServer(req as any, res as NextApiResponseWithSocket);
    
    return NextResponse.json({ success: true, message: 'Socket server initialized' });
  } catch (error) {
    console.error('Socket initialization error:', error);
    return NextResponse.json({ error: 'Failed to initialize socket' }, { status: 500 });
  }
} 