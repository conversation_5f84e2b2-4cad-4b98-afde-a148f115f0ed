import { Award, Check } from "lucide-react"
import { ReactNode } from "react"

interface GlassCardProps {
  children: ReactNode;
  className?: string;
}

function GlassCard({ children, className = "" }: GlassCardProps) {
  return (
    <div
      className={`rounded-xl p-4 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl ${className}`}
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {children}
    </div>
  )
}

export default function ImmigrationPathways() {
  const pathways = [
    {
      id: 1,
      title: "O-1 Visa",
      description: "Success rate up to 96% for applicants with strong research credentials.",
      icon: <Award className="h-5 w-5 text-indigo-600" />,
    },
    {
      id: 2,
      title: "EB-1 Visa",
      description: "High approval for extraordinary ability professionals.",
      icon: <Award className="h-5 w-5 text-indigo-600" />,
    },
    {
      id: 3,
      title: "UK Global Talent Visa",
      description: "Guidance for innovators and leaders.",
      icon: <Award className="h-5 w-5 text-indigo-600" />,
    },
    {
      id: 4,
      title: "Australia National Innovation Visa",
      description: "Support for future-shaping professionals.",
      icon: <Award className="h-5 w-5 text-indigo-600" />,
    },
  ]

  return (
    <section className="py-16">
      <div className="container max-w-7xl mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-6 text-indigo-800">Immigration Pathways & Success Rates</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
          {pathways.map((pathway) => (
            <GlassCard key={pathway.id} className="transform transition hover:translate-y-[-4px]">
              <div className="p-6">
                <div className="mb-4 bg-indigo-50/50 p-3 rounded-full inline-block">{pathway.icon}</div>
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">{pathway.title}</h3>
                <p className="text-sm text-gray-600">{pathway.description}</p>
              </div>
            </GlassCard>
          ))}
        </div>

        <div className="mt-12">
          <GlassCard className="transform transition hover:translate-y-[-4px]">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-indigo-800 text-center mb-8">Services Snapshot</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-8 gap-y-4 max-w-4xl mx-auto">
            {[
              "Visa Application Assistance",
              "Documentation & Financial Planning",
              "Mock Interviews",
              "Fast-Track Appointments",
              "Personalized Counseling",
              "University Shortlisting",
            ].map((service, index) => (
              <div key={index} className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600">{service}</span>
              </div>
            ))}
          </div>
            </div>
          </GlassCard>
        </div>
      </div>
    </section>
  )
}
