"use client";

import { useState } from "react";
import { format, startOfToday, add, eachDayOfInterval, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isSameMonth, isSameDay, parse, getDay } from "date-fns";
import { Calendar as CalendarIcon, Plus, Filter, ChevronLeft, ChevronRight, MoreHorizontal, X, Clock, MapPin, User, FileText } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import GlassCard from "@/components/GlassCard";

// Mock data for events
const MOCK_EVENTS = [
  {
    id: "1",
    title: "Client Consultation",
    date: add(startOfToday(), { days: 0 }),
    time: "10:00 AM - 11:00 AM",
    type: "consultation",
    client: "John Smith",
    description: "Initial consultation for student visa application",
    location: "Virtual Meeting"
  },
  {
    id: "2",
    title: "Document Review",
    date: add(startOfToday(), { days: 1 }),
    time: "2:00 PM - 3:00 PM",
    type: "review",
    client: "Maria Garcia",
    description: "Review application documents for completeness",
    location: "Office"
  },
  {
    id: "3",
    title: "Team Meeting",
    date: add(startOfToday(), { days: 2 }),
    time: "9:00 AM - 10:00 AM",
    type: "internal",
    client: "",
    description: "Weekly team sync",
    location: "Conference Room"
  },
  {
    id: "4",
    title: "Follow-up Call",
    date: add(startOfToday(), { days: 3 }),
    time: "11:30 AM - 12:00 PM",
    type: "follow-up",
    client: "David Lee",
    description: "Follow up on visa status",
    location: "Phone Call"
  },
  {
    id: "5",
    title: "Application Submission",
    date: add(startOfToday(), { days: 5 }),
    time: "3:00 PM - 4:00 PM",
    type: "submission",
    client: "Sarah Johnson",
    description: "Help client submit visa application",
    location: "Office"
  }
];

// Event type colors
const EVENT_COLORS = {
  consultation: "bg-blue-100/70 text-blue-800 border-blue-200",
  review: "bg-amber-100/70 text-amber-800 border-amber-200",
  internal: "bg-indigo-100/70 text-indigo-800 border-indigo-200",
  "follow-up": "bg-green-100/70 text-green-800 border-green-200",
  submission: "bg-purple-100/70 text-purple-800 border-purple-200"
};

export default function CalendarPage() {
  const today = startOfToday();
  const [selectedDate, setSelectedDate] = useState(today);
  const [currentMonth, setCurrentMonth] = useState(format(today, 'MMM-yyyy'));
  const [currentView, setCurrentView] = useState<"month" | "week" | "day">("month");
  const [isNewEventOpen, setIsNewEventOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  const [filterType, setFilterType] = useState<string | null>(null);
  
  // Parse the current month string back to a Date object
  const firstDayOfMonth = parse(currentMonth, 'MMM-yyyy', new Date());
  
  // Calculate days for month view
  const daysInMonth = eachDayOfInterval({
    start: startOfMonth(firstDayOfMonth),
    end: endOfMonth(firstDayOfMonth)
  });
  
  // Calculate days for week view
  const daysInWeek = eachDayOfInterval({
    start: startOfWeek(selectedDate),
    end: endOfWeek(selectedDate)
  });
  
  // Filter events based on selected view and filters
  const filteredEvents = MOCK_EVENTS.filter(event => {
    if (filterType && event.type !== filterType) return false;
    
    if (currentView === "day") {
      return isSameDay(event.date, selectedDate);
    } else if (currentView === "week") {
      return daysInWeek.some(day => isSameDay(day, event.date));
    } else {
      return isSameMonth(event.date, firstDayOfMonth);
    }
  });
  
  // Handle month navigation
  const previousMonth = () => {
    const firstDayOfPrevMonth = add(firstDayOfMonth, { months: -1 });
    setCurrentMonth(format(firstDayOfPrevMonth, 'MMM-yyyy'));
  };
  
  const nextMonth = () => {
    const firstDayOfNextMonth = add(firstDayOfMonth, { months: 1 });
    setCurrentMonth(format(firstDayOfNextMonth, 'MMM-yyyy'));
  };
  
  // Get events for a specific day
  const getEventsForDay = (day: Date) => {
    return MOCK_EVENTS.filter(event => isSameDay(event.date, day));
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 md:gap-0">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Calendar
          </h1>
          <p className="text-gray-600 mt-1">Manage your appointments and schedule</p>
        </div>
        
        <div className="flex gap-3 self-end">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-indigo-50/70 transition-colors shadow-sm flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-4 bg-white/90 backdrop-blur-lg border border-indigo-100/30 rounded-lg shadow-lg">
              <div className="space-y-2">
                <h4 className="font-medium text-indigo-800">Filter by type</h4>
                <Select onValueChange={(value) => setFilterType(value === "all" ? null : value)}>
                  <SelectTrigger className="border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-800 focus:ring-2 focus:ring-indigo-500">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Events</SelectItem>
                    <SelectItem value="consultation">Consultation</SelectItem>
                    <SelectItem value="review">Document Review</SelectItem>
                    <SelectItem value="internal">Internal</SelectItem>
                    <SelectItem value="follow-up">Follow-up</SelectItem>
                    <SelectItem value="submission">Submission</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </PopoverContent>
          </Popover>
          
          <Dialog open={isNewEventOpen} onOpenChange={setIsNewEventOpen}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                New Event
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] bg-white/90 backdrop-blur-lg border border-indigo-100/30 rounded-xl shadow-lg">
              <DialogHeader>
                <DialogTitle className="text-xl text-indigo-800">Create New Event</DialogTitle>
                <DialogDescription className="text-gray-600">
                  Add a new event to your calendar. Click save when you're done.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="title" className="text-indigo-700 font-medium">Title</Label>
                  <Input id="title" placeholder="Event title" className="border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-800 focus:ring-2 focus:ring-indigo-500" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="date" className="text-indigo-700 font-medium">Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-800"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4 text-indigo-600" />
                        {format(selectedDate, "PPP")}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-white/90 backdrop-blur-lg border border-indigo-100/30">
                      <Calendar
                        mode="single"
                        selected={selectedDate}
                        onSelect={(date) => date && setSelectedDate(date)}
                        initialFocus
                        className="text-indigo-800"
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="start-time" className="text-indigo-700 font-medium">Start Time</Label>
                    <Input id="start-time" type="time" className="border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-800 focus:ring-2 focus:ring-indigo-500" />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="end-time" className="text-indigo-700 font-medium">End Time</Label>
                    <Input id="end-time" type="time" className="border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-800 focus:ring-2 focus:ring-indigo-500" />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="type" className="text-indigo-700 font-medium">Event Type</Label>
                  <Select>
                    <SelectTrigger className="border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-800 focus:ring-2 focus:ring-indigo-500">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="consultation">Consultation</SelectItem>
                      <SelectItem value="review">Document Review</SelectItem>
                      <SelectItem value="internal">Internal</SelectItem>
                      <SelectItem value="follow-up">Follow-up</SelectItem>
                      <SelectItem value="submission">Submission</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="client" className="text-indigo-700 font-medium">Client (if applicable)</Label>
                  <Input id="client" placeholder="Client name" className="border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-800 focus:ring-2 focus:ring-indigo-500" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="location" className="text-indigo-700 font-medium">Location</Label>
                  <Input id="location" placeholder="Event location" className="border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-800 focus:ring-2 focus:ring-indigo-500" />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description" className="text-indigo-700 font-medium">Description</Label>
                  <Input id="description" placeholder="Event description" className="border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-800 focus:ring-2 focus:ring-indigo-500" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsNewEventOpen(false)} className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 hover:bg-indigo-50/70">
                  Cancel
                </Button>
                <Button onClick={() => setIsNewEventOpen(false)} className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white hover:from-blue-700 hover:to-indigo-800">
                  Save Event
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      {/* Wrap the TabsContent in a Tabs component */}
      <Tabs defaultValue={currentView} onValueChange={(value) => setCurrentView(value as "month" | "week" | "day")}>
        <div className="flex justify-between items-center mb-4">
          <TabsList className="bg-white/70 backdrop-blur-sm border border-indigo-100/30 p-1">
            <TabsTrigger value="month" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg">
              Month
            </TabsTrigger>
            <TabsTrigger value="week" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg">
              Week
            </TabsTrigger>
            <TabsTrigger value="day" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg">
              Day
            </TabsTrigger>
          </TabsList>
          
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={previousMonth}
              className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 hover:bg-indigo-50/70"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="text-lg font-medium text-indigo-800">
              {format(firstDayOfMonth, 'MMMM yyyy')}
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={nextMonth}
              className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 hover:bg-indigo-50/70"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Month View */}
        <TabsContent value="month">
          <div className="grid grid-cols-7 gap-px bg-indigo-100/30 rounded-lg overflow-hidden">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
              <div key={day} className="bg-indigo-50/50 backdrop-blur-sm py-2 text-center text-xs font-medium text-indigo-700">
                {day}
              </div>
            ))}
            {daysInMonth.map((day, dayIdx) => {
              const eventsForDay = getEventsForDay(day);
              const isOutsideMonth = !isSameMonth(day, firstDayOfMonth);
              
              // Calculate the starting day offset for the first week
              const startOffset = dayIdx === 0 ? getDay(day) : 0;
              
              // Add offset cells for the first week
              const offsetCells = dayIdx === 0 ? Array.from({ length: startOffset }).map((_, i) => (
                <div key={`offset-${i}`} className="bg-white/50 backdrop-blur-sm h-24 p-1 border-t border-indigo-100/30"></div>
              )) : null;
              
              return (
                <>
                  {offsetCells}
                  <div 
                    key={day.toString()}
                    className={cn(
                      "bg-white/80 backdrop-blur-sm h-24 p-1 border-t border-indigo-100/30",
                      isSameDay(day, today) && "bg-indigo-50/80",
                      isOutsideMonth && "bg-white/50 opacity-40"
                    )}
                    onClick={() => setSelectedDate(day)}
                  >
                    <div className="flex justify-between items-start">
                      <div 
                        className={cn(
                          "h-6 w-6 flex items-center justify-center text-xs rounded-full",
                          isSameDay(day, today) ? "bg-gradient-to-r from-blue-600 to-indigo-700 text-white" : "text-indigo-800"
                        )}
                      >
                        {format(day, 'd')}
                      </div>
                      {eventsForDay.length > 0 && (
                        <span className="text-xs bg-indigo-100/70 text-indigo-700 px-1.5 py-0.5 rounded-full">
                          {eventsForDay.length}
                        </span>
                      )}
                    </div>
                    
                    <div className="mt-1 overflow-y-auto max-h-16">
                      {eventsForDay.slice(0, 2).map((event, eventIdx) => (
                        <div 
                          key={event.id}
                          className={`text-xs truncate mb-1 px-1.5 py-0.5 rounded ${EVENT_COLORS[event.type as keyof typeof EVENT_COLORS]}`}
                        >
                          {event.title}
                        </div>
                      ))}
                      {eventsForDay.length > 2 && (
                        <div className="text-xs text-indigo-600 px-1.5">{eventsForDay.length - 2} more...</div>
                      )}
                    </div>
                  </div>
                </>
              );
            })}
          </div>
        </TabsContent>
        
        {/* Week View */}
        <TabsContent value="week">
          <div className="grid grid-cols-7 gap-px bg-indigo-100/30 rounded-lg overflow-hidden">
            {daysInWeek.map((day) => (
              <div key={day.toString()} className="bg-white/80 backdrop-blur-sm">
                <div className={cn(
                  "py-2 text-center text-sm font-medium border-b border-indigo-100/30",
                  isSameDay(day, today) && "bg-indigo-50/80"
                )}>
                  <div className="text-xs text-indigo-600">{format(day, 'EEE')}</div>
                  <div className={cn(
                    "h-7 w-7 mx-auto flex items-center justify-center rounded-full",
                    isSameDay(day, today) ? "bg-gradient-to-r from-blue-600 to-indigo-700 text-white" : "text-indigo-800"
                  )}>
                    {format(day, 'd')}
                  </div>
                </div>
                
                <div className="h-[300px] overflow-y-auto p-1">
                  {getEventsForDay(day).map((event) => (
                    <div 
                      key={event.id}
                      className={`mb-2 p-2 rounded-lg ${EVENT_COLORS[event.type as keyof typeof EVENT_COLORS]} border`}
                    >
                      <div className="text-sm font-medium truncate">{event.title}</div>
                      <div className="text-xs flex items-center mt-1">
                        <Clock className="h-3 w-3 mr-1" />
                        {event.time}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </TabsContent>
        
        {/* Day View */}
        <TabsContent value="day">
          <div className="flex gap-4">
            <div className="w-1/3 lg:w-1/4">
              <div className="bg-indigo-50/70 backdrop-blur-sm rounded-lg p-4 mb-4">
                <h3 className="text-xl font-bold text-indigo-800 mb-1">{format(selectedDate, 'd')}</h3>
                <div className="text-indigo-600 font-medium">{format(selectedDate, 'EEEE')}</div>
                <div className="text-gray-600">{format(selectedDate, 'MMMM yyyy')}</div>
                
                {isSameDay(selectedDate, today) && (
                  <div className="mt-2 bg-gradient-to-r from-blue-600 to-indigo-700 text-white text-xs px-2 py-1 rounded-full inline-block">Today</div>
                )}
              </div>
              
              <div className="bg-indigo-50/70 backdrop-blur-sm rounded-lg p-4">
                <h4 className="text-sm font-semibold text-indigo-800 mb-2">Calendar Legend</h4>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
                    <span className="text-sm text-indigo-800">Consultation</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-amber-400 mr-2"></div>
                    <span className="text-sm text-indigo-800">Document Review</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-indigo-400 mr-2"></div>
                    <span className="text-sm text-indigo-800">Internal Meeting</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-green-400 mr-2"></div>
                    <span className="text-sm text-indigo-800">Follow-up Call</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-purple-400 mr-2"></div>
                    <span className="text-sm text-indigo-800">Application Submission</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="w-2/3 lg:w-3/4">
              <div className="bg-white/80 backdrop-blur-sm rounded-lg border border-indigo-100/30 overflow-hidden">
                <div className="border-b border-indigo-100/30 p-3">
                  <h3 className="text-lg font-semibold text-indigo-800">Events for {format(selectedDate, 'MMMM d, yyyy')}</h3>
                </div>
                
                <div className="p-4">
                  {filteredEvents.length > 0 ? (
                    <div className="space-y-4">
                      {filteredEvents.map((event) => (
                        <GlassCard 
                          key={event.id}
                          className="transform transition hover:translate-y-[-4px]"
                        >
                          <div className="p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="text-lg font-medium text-indigo-800">{event.title}</h4>
                                <div className="flex items-center text-sm text-indigo-600 mt-1">
                                  <Clock className="h-4 w-4 mr-1.5" />
                                  {event.time}
                                </div>
                              </div>
                              <div className={`px-2.5 py-1 rounded-full text-xs font-medium ${EVENT_COLORS[event.type as keyof typeof EVENT_COLORS]}`}>
                                {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                              </div>
                            </div>
                            
                            <div className="mt-3 space-y-2">
                              {event.client && (
                                <div className="flex items-center text-sm text-gray-600">
                                  <User className="h-4 w-4 text-indigo-600 mr-1.5" />
                                  {event.client}
                                </div>
                              )}
                              
                              <div className="flex items-center text-sm text-gray-600">
                                <MapPin className="h-4 w-4 text-indigo-600 mr-1.5" />
                                {event.location}
                              </div>
                              
                              {event.description && (
                                <div className="flex items-start text-sm text-gray-600 mt-1">
                                  <FileText className="h-4 w-4 text-indigo-600 mr-1.5 mt-0.5" />
                                  <span>{event.description}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </GlassCard>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="mb-2">
                        <CalendarIcon className="h-12 w-12 text-indigo-300 mx-auto" />
                      </div>
                      <h4 className="text-lg font-medium text-indigo-800 mb-1">No events scheduled</h4>
                      <p className="text-gray-600">There are no events scheduled for this day.</p>
                      <Button 
                        onClick={() => setIsNewEventOpen(true)}
                        className="mt-4 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add New Event
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Event List for Today */}
      <GlassCard>
        <div className="p-5 border-b border-indigo-100/30">
          <h2 className="text-lg font-semibold text-indigo-800">Today's Events</h2>
        </div>
        
        <div className="divide-y divide-indigo-100/30">
          {getEventsForDay(today).length > 0 ? (
            getEventsForDay(today).map((event) => (
              <div key={event.id} className="p-4 flex items-start">
                <div className={`h-10 w-10 rounded-lg flex items-center justify-center mr-4 ${
                  event.type === 'consultation' ? 'bg-gradient-to-br from-blue-400 to-blue-300' : 
                  event.type === 'review' ? 'bg-gradient-to-br from-amber-400 to-amber-300' : 
                  event.type === 'internal' ? 'bg-gradient-to-br from-indigo-400 to-indigo-300' : 
                  event.type === 'follow-up' ? 'bg-gradient-to-br from-green-400 to-green-300' :
                  'bg-gradient-to-br from-purple-400 to-purple-300'
                }`}>
                  <CalendarIcon className="h-6 w-6 text-white" />
                </div>
                
                <div className="flex-1">
                  <div className="flex justify-between">
                    <h4 className="font-medium text-indigo-800">{event.title}</h4>
                    <span className="text-sm text-indigo-600">{event.time}</span>
                  </div>
                  
                  <div className="flex items-center mt-1 text-sm text-gray-600">
                    <MapPin className="h-3.5 w-3.5 mr-1" />
                    {event.location}
                    {event.client && (
                      <>
                        <span className="mx-2">•</span>
                        <User className="h-3.5 w-3.5 mr-1" />
                        {event.client}
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600">No events scheduled for today</p>
            </div>
          )}
        </div>
      </GlassCard>
    </div>
  );
} 