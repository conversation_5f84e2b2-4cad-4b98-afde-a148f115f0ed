# 🗄️ Database Integration Guide - Visa Mentor Application

## 📋 Overview

The visa-mentor application has been successfully integrated with a **MySQL database** to replace all mock/dummy data with real, persistent user data. This guide will help you set up and use the database system.

## 🚀 Quick Setup

### Prerequisites
- **XAMPP** installed and running
- **MySQL** service started on port 3306
- **Node.js** and npm installed

### 1. Start XAMPP
1. Open XAMPP Control Panel
2. Start **Apache** and **MySQL** services
3. Ensure MySQL is running on port 3306

### 2. Create Database
1. Open phpMyAdmin (http://localhost/phpmyadmin)
2. Create a new database named `visa_mentor`
3. Or run the SQL script: `scripts/setup-database.sql`

### 3. Configure Environment
Ensure your `.env.local` file contains:
```env
DATABASE_URL="mysql://root:@localhost:3306/visa_mentor"
```

### 4. Setup Database
Run the automated setup script:
```bash
npm run db:setup
```

This will:
- Generate Prisma client
- Run database migrations
- Seed the database with sample data

## 📊 Database Schema

### Core Models

#### **User**
- Stores user profiles linked to Clerk authentication
- Includes visa preferences, progress tracking, and settings
- Connected to all other user-specific data

#### **Application**
- Visa applications with progress tracking
- Status updates and stage management
- Document checklists and counselor assignments

#### **Appointment**
- Scheduling system for consultations
- Counselor assignments and meeting links
- Status tracking (scheduled, confirmed, completed)

#### **Task**
- Todo items with priority levels
- Due dates and completion tracking
- Linked to specific applications

#### **Message**
- Communication between users and counselors
- Read/unread status tracking
- Attachment support

#### **Booking**
- Consultation bookings with payment tracking
- Service type categorization
- Meeting type preferences

#### **TravelRecommendation**
- Personalized travel suggestions
- Category-based organization
- Validity periods and action links

## 🔧 Available Scripts

```bash
# Complete database setup (recommended)
npm run db:setup

# Run migrations only
npm run db:migrate

# Seed database with sample data
npm run db:seed

# Reset database (WARNING: deletes all data)
npm run db:reset

# Open Prisma Studio (database GUI)
npm run db:studio
```

## 📱 Dashboard Integration

### Real Data Features

#### **Statistics Cards**
- **Active Applications**: Count from database
- **Upcoming Appointments**: Future appointments only
- **Pending Tasks**: Tasks with PENDING/IN_PROGRESS status
- **Unread Messages**: Messages marked as unread

#### **Tabbed Interface**
1. **Visa Timeline**: Real application progress tracking
2. **Applications**: Live application data with progress bars
3. **Appointments**: Actual scheduled appointments
4. **Tasks**: Real task management with priorities
5. **Messages**: Live communication history

#### **User Profile**
- Synced with Clerk authentication
- Database-stored preferences and progress
- Real membership and profile data

## 🔄 Data Synchronization

### Clerk Integration
- Users are automatically created in database on first login
- Clerk user data syncs with database user records
- Profile updates persist across sessions

### Real-time Updates
- All dashboard data fetched from database
- Statistics update based on actual data counts
- Progress tracking reflects real application status

## 🛠️ API Endpoints

### User Management
- `GET /api/user` - Get user profile and stats
- `PUT /api/user` - Update user profile

### Applications
- `GET /api/applications` - Get user applications
- `POST /api/applications` - Create new application

### Appointments
- `GET /api/appointments` - Get user appointments
- `POST /api/appointments` - Schedule new appointment

### Tasks
- `GET /api/tasks` - Get user tasks
- `POST /api/tasks` - Create new task

### Messages
- `GET /api/messages` - Get user messages
- `POST /api/messages` - Send new message

### Bookings
- `GET /api/bookings` - Get user bookings
- `POST /api/bookings` - Create new booking

## 🔍 Testing the Integration

### 1. Start the Application
```bash
npm run dev
```

### 2. Sign In
- Visit http://localhost:3000
- Sign in with Clerk authentication
- User will be automatically created in database

### 3. Verify Data
- Check dashboard statistics show real counts
- Navigate through tabs to see actual data
- Create new applications, appointments, or tasks
- Verify data persists after page refresh

### 4. Database Inspection
```bash
npm run db:studio
```
- Opens Prisma Studio at http://localhost:5555
- Browse and edit database records
- Verify data integrity

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Failed
- Ensure XAMPP MySQL is running
- Check DATABASE_URL in .env.local
- Verify database "visa_mentor" exists

#### Migration Errors
```bash
npm run db:reset
npm run db:setup
```

#### No Data Showing
- Run seeding script: `npm run db:seed`
- Check browser console for API errors
- Verify user is signed in with Clerk

#### Prisma Client Issues
```bash
npx prisma generate
```

### Reset Everything
If you encounter persistent issues:
```bash
npm run db:reset
npm run db:setup
```

## 📈 Performance Considerations

- Database queries are optimized with proper indexing
- Related data is fetched efficiently using Prisma includes
- Statistics are calculated server-side for better performance
- Pagination can be added for large datasets

## 🔒 Security Features

- All API routes require Clerk authentication
- Users can only access their own data
- Database queries include user ID filtering
- Input validation on all endpoints

## 🎉 Success!

Your visa-mentor application now uses a real MySQL database with:
- ✅ Persistent user data
- ✅ Real-time dashboard updates
- ✅ Comprehensive data models
- ✅ Secure API endpoints
- ✅ Clerk authentication integration
- ✅ Production-ready architecture

The application has been transformed from using static mock data to a fully functional system with persistent user data stored in a real database!
