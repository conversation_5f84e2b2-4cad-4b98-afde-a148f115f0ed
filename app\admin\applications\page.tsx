"use client";

import { useState } from "react";
import Link from "next/link";
import {
  Search,
  ChevronDown,
  Filter,
  ChevronLeft,
  ChevronRight,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  FileText,
  Calendar,
  BarChart2,
  ArrowUpDown,
  Stamp,
  UserCheck,
  MoreHorizontal,
  MessageSquare,
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Application interface
interface Application {
  id: string;
  userId: string;
  userName: string;
  applicationType: string;
  destinationCountry: string;
  purpose: string;
  plannedArrival: string;
  plannedDeparture: string | null;
  status: string;
  stage: number;
  submissionDate: string;
  decisionDate: string | null;
  processingTime: number | null;
  documentsCount: number;
  documentsApproved: number;
  rejectionReason?: string;
}

// Mock data for applications
const mockApplications: Application[] = [
  {
    id: "app-123",
    userId: "u-1",
    userName: "<PERSON>",
    applicationType: "tourist",
    destinationCountry: "United States",
    purpose: "Vacation and visiting family",
    plannedArrival: "2023-11-15T00:00:00Z",
    plannedDeparture: "2023-12-20T00:00:00Z",
    status: "approved",
    stage: 8,
    submissionDate: "2023-09-10T14:30:00Z",
    decisionDate: "2023-10-05T09:15:00Z",
    processingTime: 25,
    documentsCount: 5,
    documentsApproved: 5,
  },
  {
    id: "app-456",
    userId: "u-2",
    userName: "Maria Johnson",
    applicationType: "student",
    destinationCountry: "United Kingdom",
    purpose: "Masters degree in Computer Science",
    plannedArrival: "2023-12-28T00:00:00Z",
    plannedDeparture: "2025-06-30T00:00:00Z",
    status: "in_review",
    stage: 4,
    submissionDate: "2023-09-25T10:45:00Z",
    decisionDate: null,
    processingTime: null,
    documentsCount: 8,
    documentsApproved: 6,
  },
  {
    id: "app-789",
    userId: "u-3",
    userName: "Robert Chen",
    applicationType: "business",
    destinationCountry: "Germany",
    purpose: "Business conference and meetings",
    plannedArrival: "2023-11-05T00:00:00Z",
    plannedDeparture: "2023-11-15T00:00:00Z",
    status: "pending_documents",
    stage: 3,
    submissionDate: "2023-09-20T16:30:00Z",
    decisionDate: null,
    processingTime: null,
    documentsCount: 4,
    documentsApproved: 2,
  },
  {
    id: "app-012",
    userId: "u-4",
    userName: "Sophia Rodriguez",
    applicationType: "work",
    destinationCountry: "Canada",
    purpose: "Software engineer position at tech company",
    plannedArrival: "2024-01-15T00:00:00Z",
    plannedDeparture: "2026-01-15T00:00:00Z",
    status: "approved",
    stage: 8,
    submissionDate: "2023-08-12T11:20:00Z",
    decisionDate: "2023-09-30T14:10:00Z",
    processingTime: 49,
    documentsCount: 10,
    documentsApproved: 10,
  },
  {
    id: "app-345",
    userId: "u-5",
    userName: "Daniel Wilson",
    applicationType: "family",
    destinationCountry: "Australia",
    purpose: "Joining spouse who is a permanent resident",
    plannedArrival: "2023-12-10T00:00:00Z",
    plannedDeparture: null,
    status: "rejected",
    stage: 6,
    submissionDate: "2023-09-05T09:30:00Z",
    decisionDate: "2023-10-10T15:45:00Z",
    processingTime: 35,
    documentsCount: 7,
    documentsApproved: 5,
    rejectionReason: "Insufficient proof of financial support",
  },
  {
    id: "app-678",
    userId: "u-6",
    userName: "Emily Davis",
    applicationType: "tourist",
    destinationCountry: "Japan",
    purpose: "Tourism and cultural experience",
    plannedArrival: "2023-11-20T00:00:00Z",
    plannedDeparture: "2023-12-15T00:00:00Z",
    status: "in_review",
    stage: 5,
    submissionDate: "2023-09-15T13:40:00Z",
    decisionDate: null,
    processingTime: null,
    documentsCount: 5,
    documentsApproved: 5,
  },
  {
    id: "app-901",
    userId: "u-7",
    userName: "Michael Brown",
    applicationType: "student",
    destinationCountry: "France",
    purpose: "Exchange semester at university",
    plannedArrival: "2024-01-25T00:00:00Z",
    plannedDeparture: "2024-06-30T00:00:00Z",
    status: "submitted",
    stage: 2,
    submissionDate: "2023-10-01T10:15:00Z",
    decisionDate: null,
    processingTime: null,
    documentsCount: 6,
    documentsApproved: 3,
  },
  {
    id: "app-234",
    userId: "u-8",
    userName: "Lisa Johnson",
    applicationType: "business",
    destinationCountry: "Singapore",
    purpose: "Business expansion and partnership negotiations",
    plannedArrival: "2023-11-10T00:00:00Z",
    plannedDeparture: "2023-11-25T00:00:00Z",
    status: "submitted",
    stage: 2,
    submissionDate: "2023-10-02T14:20:00Z",
    decisionDate: null,
    processingTime: null,
    documentsCount: 5,
    documentsApproved: 2,
  },
];

export default function ApplicationsPage() {
  const [applications, setApplications] = useState<Application[]>(mockApplications);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [countryFilter, setCountryFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState("submissionDate");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [selectedApplication, setSelectedApplication] = useState<string | null>(null);
  
  const itemsPerPage = 5;
  
  // Application type options
  const applicationTypes = [
    { id: "all", name: "All Types" },
    { id: "tourist", name: "Tourist" },
    { id: "business", name: "Business" },
    { id: "student", name: "Student" },
    { id: "work", name: "Work" },
    { id: "family", name: "Family" },
  ];
  
  // Status options
  const statusOptions = [
    { id: "all", name: "All Statuses" },
    { id: "submitted", name: "Submitted" },
    { id: "in_review", name: "In Review" },
    { id: "pending_documents", name: "Documents Pending" },
    { id: "approved", name: "Approved" },
    { id: "rejected", name: "Rejected" },
  ];
  
  // Get unique countries
  const countries = Array.from(new Set(applications.map(app => app.destinationCountry)));
  
  // Filter applications
  const filteredApplications = applications.filter(app => {
    // Search filter
    const searchMatch = 
      app.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.destinationCountry.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Status filter
    const statusMatch = statusFilter === "all" || app.status === statusFilter;
    
    // Type filter
    const typeMatch = typeFilter === "all" || app.applicationType === typeFilter;
    
    // Country filter
    const countryMatch = countryFilter === "all" || app.destinationCountry === countryFilter;
    
    return searchMatch && statusMatch && typeMatch && countryMatch;
  }).sort((a, b) => {
    if (sortField === "submissionDate") {
      return sortDirection === "asc"
        ? new Date(a.submissionDate).getTime() - new Date(b.submissionDate).getTime()
        : new Date(b.submissionDate).getTime() - new Date(a.submissionDate).getTime();
    } else if (sortField === "processingTime") {
      // Handle null values
      if (a.processingTime === null && b.processingTime === null) return 0;
      if (a.processingTime === null) return sortDirection === "asc" ? 1 : -1;
      if (b.processingTime === null) return sortDirection === "asc" ? -1 : 1;
      
      return sortDirection === "asc"
        ? a.processingTime - b.processingTime
        : b.processingTime - a.processingTime;
    } else if (sortField === "stage") {
      return sortDirection === "asc"
        ? a.stage - b.stage
        : b.stage - a.stage;
    }
    return 0;
  });
  
  // Pagination
  const totalPages = Math.ceil(filteredApplications.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedApplications = filteredApplications.slice(startIndex, startIndex + itemsPerPage);
  
  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric'
    }).format(date);
  };
  
  // Handle sort
  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100/70 backdrop-blur-sm text-green-800">
            <CheckCircle className="h-3.5 w-3.5 mr-1" />
            Approved
          </span>
        );
      case "rejected":
        return (
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100/70 backdrop-blur-sm text-red-800">
            <XCircle className="h-3.5 w-3.5 mr-1" />
            Rejected
          </span>
        );
      case "in_review":
        return (
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100/70 backdrop-blur-sm text-blue-800">
            <Clock className="h-3.5 w-3.5 mr-1" />
            In Review
          </span>
        );
      case "pending_documents":
        return (
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-amber-100/70 backdrop-blur-sm text-amber-800">
            <AlertCircle className="h-3.5 w-3.5 mr-1" />
            Documents Pending
          </span>
        );
      default:
        return (
          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100/70 backdrop-blur-sm text-gray-800">
            <FileText className="h-3.5 w-3.5 mr-1" />
            Submitted
          </span>
        );
    }
  };
  
  // Handle application approval
  const handleApproveApplication = (appId: string) => {
    setApplications(applications.map(app => {
      if (app.id === appId) {
        return { 
          ...app, 
          status: "approved", 
          stage: 8,
          decisionDate: new Date().toISOString(),
          processingTime: Math.floor((new Date().getTime() - new Date(app.submissionDate).getTime()) / (1000 * 60 * 60 * 24))
        };
      }
      return app;
    }));
    setSelectedApplication(null);
  };
  
  // Handle application rejection
  const handleRejectApplication = (appId: string) => {
    setApplications(applications.map(app => {
      if (app.id === appId) {
        return { 
          ...app, 
          status: "rejected", 
          stage: 6,
          decisionDate: new Date().toISOString(),
          processingTime: Math.floor((new Date().getTime() - new Date(app.submissionDate).getTime()) / (1000 * 60 * 60 * 24)),
          rejectionReason: "Application does not meet eligibility criteria."
        };
      }
      return app;
    }));
    setSelectedApplication(null);
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
          Visa Applications
        </h1>
        <p className="text-gray-600 mt-1">Manage and review visa applications</p>
      </div>
      
      {/* Filters & Actions */}
      <GlassCard className="mb-6 transform transition hover:translate-y-[-2px]">
        <div className="p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            {/* Search */}
            <div className="relative w-full sm:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-indigo-400" />
              </div>
              <input
                type="text"
                placeholder="Search applications..."
                className="pl-10 pr-3 py-2 w-full border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            {/* Filters */}
            <div className="flex items-center gap-3 w-full sm:w-auto flex-wrap">
              <div className="relative w-full sm:w-auto">
                <select
                  className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  {statusOptions.map(option => (
                    <option key={option.id} value={option.id}>{option.name}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
              
              <div className="relative w-full sm:w-auto">
                <select
                  className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  {applicationTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
              
              <div className="relative w-full sm:w-auto">
                <select
                  className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={countryFilter}
                  onChange={(e) => setCountryFilter(e.target.value)}
                >
                  <option value="all">All Countries</option>
                  {countries.map(country => (
                    <option key={country} value={country}>{country}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
            </div>
            
            {/* Actions */}
            <div className="flex items-center gap-2 w-full sm:w-auto">
              <button
                className="px-3 py-2 bg-indigo-50/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-100/80 transition-colors flex items-center"
                onClick={() => {/* Implement export */}}
              >
                <Download className="h-4 w-4 mr-1" />
                Export
              </button>
            </div>
          </div>
        </div>
      </GlassCard>
      
      {/* Applications Table */}
      <GlassCard className="mb-6 overflow-hidden transform transition hover:translate-y-[-2px]" opacity="high">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-indigo-100/30">
            <thead className="bg-indigo-50/30">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Application
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Applicant
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Type & Destination
                </th>
                <th scope="col" className="px-6 py-3 text-left">
                  <button 
                    className="flex items-center text-xs font-medium text-indigo-700 uppercase tracking-wider"
                    onClick={() => handleSort("submissionDate")}
                  >
                    Submission Date
                    {sortField === "submissionDate" && (
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    )}
                  </button>
                </th>
                <th scope="col" className="px-6 py-3 text-left">
                  <button 
                    className="flex items-center text-xs font-medium text-indigo-700 uppercase tracking-wider"
                    onClick={() => handleSort("stage")}
                  >
                    Stage
                    {sortField === "stage" && (
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    )}
                  </button>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-indigo-700 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-indigo-100/20">
              {paginatedApplications.map((app) => (
                <tr key={app.id} className="hover:bg-indigo-50/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-indigo-700">{app.id}</div>
                    <div className="text-xs text-gray-600">
                      {app.documentsApproved}/{app.documentsCount} docs verified
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-gradient-to-br from-indigo-100 to-blue-50 rounded-full flex items-center justify-center">
                        <span className="text-indigo-700 font-medium">
                          {app.userName.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-indigo-800">{app.userName}</div>
                        <div className="text-xs text-gray-600">ID: {app.userId}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-indigo-800 capitalize">{app.applicationType} Visa</div>
                    <div className="text-xs text-gray-600">{app.destinationCountry}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                    {formatDate(app.submissionDate)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-full bg-indigo-100/40 rounded-full h-1.5">
                      <div 
                        className={`h-1.5 rounded-full ${
                          app.status === "approved" ? "bg-gradient-to-r from-green-500 to-emerald-400" : 
                          app.status === "rejected" ? "bg-gradient-to-r from-red-500 to-rose-400" : 
                          "bg-gradient-to-r from-indigo-500 to-blue-400"
                        }`} 
                        style={{ width: `${(app.stage / 8) * 100}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-600 mt-1">Stage {app.stage}/8</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(app.status)}
                    {app.decisionDate && (
                      <div className="text-xs text-gray-600 mt-1">
                        {app.status === "approved" ? "Approved" : "Rejected"} on {formatDate(app.decisionDate)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button 
                        className="p-1 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded transition-colors"
                        onClick={() => setSelectedApplication(app.id)}
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {app.status !== "approved" && app.status !== "rejected" && (
                        <>
                          <button 
                            className="p-1 text-green-600 hover:text-green-900 bg-green-50/70 backdrop-blur-sm rounded transition-colors"
                            onClick={() => handleApproveApplication(app.id)}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </button>
                          <button 
                            className="p-1 text-red-600 hover:text-red-900 bg-red-50/70 backdrop-blur-sm rounded transition-colors"
                            onClick={() => handleRejectApplication(app.id)}
                          >
                            <XCircle className="h-4 w-4" />
                          </button>
                        </>
                      )}
                      <Link 
                        href={`/admin/documents?application=${app.id}`}
                        className="p-1 text-amber-600 hover:text-amber-900 bg-amber-50/70 backdrop-blur-sm rounded transition-colors"
                      >
                        <FileText className="h-4 w-4" />
                      </Link>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        <div className="bg-indigo-50/30 px-4 py-3 flex items-center justify-between border-t border-indigo-100/20 sm:px-6">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-indigo-700">
                Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(startIndex + itemsPerPage, filteredApplications.length)}
                </span>{' '}
                of <span className="font-medium">{filteredApplications.length}</span> applications
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-indigo-200/60 bg-white/80 backdrop-blur-sm text-sm font-medium text-indigo-600 hover:bg-indigo-50/70 transition-colors ${
                    currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`relative inline-flex items-center px-4 py-2 border ${
                      currentPage === page
                        ? 'z-10 bg-indigo-100/70 border-indigo-500 text-indigo-700'
                        : 'border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-600 hover:bg-indigo-50/70'
                    } text-sm font-medium transition-colors`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-indigo-200/60 bg-white/80 backdrop-blur-sm text-sm font-medium text-indigo-600 hover:bg-indigo-50/70 transition-colors ${
                    currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </GlassCard>
      
      {/* Application Detail Modal */}
      {selectedApplication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
          <GlassCard className="w-full max-w-4xl max-h-[90vh] overflow-y-auto" opacity="high">
            <div className="p-6">
              {(() => {
                const app = applications.find(a => a.id === selectedApplication);
                if (!app) return null;
                
                return (
                  <>
                    <div className="flex justify-between items-center mb-6">
                      <div>
                        <h3 className="text-lg font-semibold text-indigo-800">
                          Application {app.id}
                        </h3>
                        <p className="text-sm text-gray-600">Submitted on {formatDate(app.submissionDate)}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(app.status)}
                        <button 
                          onClick={() => setSelectedApplication(null)}
                          className="p-1 rounded-full hover:bg-indigo-50/70 transition-colors"
                        >
                          <XCircle className="h-5 w-5 text-indigo-400" />
                        </button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className="text-sm font-medium text-indigo-700 mb-3">Applicant Information</h4>
                        <div className="bg-indigo-50/30 rounded-lg p-4">
                          <div className="flex items-center mb-3">
                            <div className="h-10 w-10 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center mr-3">
                              <span className="text-white font-medium">
                                {app.userName.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium text-indigo-800">{app.userName}</div>
                              <div className="text-sm text-gray-600">User ID: {app.userId}</div>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <div className="text-gray-600">Application Type</div>
                              <div className="font-medium text-indigo-800 capitalize">{app.applicationType} Visa</div>
                            </div>
                            <div>
                              <div className="text-gray-600">Destination</div>
                              <div className="font-medium text-indigo-800">{app.destinationCountry}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-indigo-700 mb-3">Application Status</h4>
                        <div className="bg-indigo-50/30 rounded-lg p-4">
                          <div className="mb-3">
                            <div className="flex justify-between text-sm mb-1">
                              <div className="text-gray-600">Progress</div>
                              <div className="font-medium text-indigo-800">Stage {app.stage}/8</div>
                            </div>
                            <div className="w-full bg-indigo-100/40 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  app.status === "approved" ? "bg-gradient-to-r from-green-500 to-emerald-400" : 
                                  app.status === "rejected" ? "bg-gradient-to-r from-red-500 to-rose-400" : 
                                  "bg-gradient-to-r from-indigo-500 to-blue-400"
                                }`}
                                style={{ width: `${(app.stage / 8) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <div className="text-gray-600">Processing Time</div>
                              <div className="font-medium text-indigo-800">{app.processingTime ? `${app.processingTime} days` : 'In progress'}</div>
                            </div>
                            <div>
                              <div className="text-gray-600">Decision Date</div>
                              <div className="font-medium text-indigo-800">{app.decisionDate ? formatDate(app.decisionDate) : 'Pending'}</div>
                            </div>
                            <div>
                              <div className="text-gray-600">Documents</div>
                              <div className="font-medium text-indigo-800">{app.documentsApproved}/{app.documentsCount} verified</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-indigo-700 mb-3">Travel Information</h4>
                      <div className="bg-indigo-50/30 rounded-lg p-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                          <div>
                            <div className="text-gray-600">Purpose of Travel</div>
                            <div className="font-medium text-indigo-800">{app.purpose}</div>
                          </div>
                          <div>
                            <div className="text-gray-600">Planned Arrival</div>
                            <div className="font-medium text-indigo-800">{app.plannedArrival ? formatDate(app.plannedArrival) : 'Not specified'}</div>
                          </div>
                          <div>
                            <div className="text-gray-600">Planned Departure</div>
                            <div className="font-medium text-indigo-800">{app.plannedDeparture ? formatDate(app.plannedDeparture) : 'Not specified'}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {app.status === "rejected" && app.rejectionReason && (
                      <div className="mb-6">
                        <h4 className="text-sm font-medium text-indigo-700 mb-3">Rejection Reason</h4>
                        <div className="bg-red-50/70 backdrop-blur-sm text-red-800 rounded-lg p-4 text-sm">
                          {app.rejectionReason}
                        </div>
                      </div>
                    )}
                    
                    <div className="flex justify-between mt-6 pt-6 border-t border-indigo-100/30">
                      <div className="flex gap-2">
                        <Link
                          href={`/admin/documents?application=${app.id}`}
                          className="px-4 py-2 bg-indigo-100/70 backdrop-blur-sm text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-200/70 transition-colors flex items-center"
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          View Documents
                        </Link>
                        <button
                          className="px-4 py-2 bg-indigo-50/70 backdrop-blur-sm text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-100/70 transition-colors flex items-center"
                        >
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Contact Applicant
                        </button>
                      </div>
                      
                      {app.status !== "approved" && app.status !== "rejected" && (
                        <div className="flex gap-2">
                          <button
                            onClick={() => handleApproveApplication(app.id)}
                            className="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-500 text-white rounded-lg text-sm font-medium hover:from-green-700 hover:to-emerald-600 transition-colors shadow-sm"
                          >
                            Approve Application
                          </button>
                          <button
                            onClick={() => handleRejectApplication(app.id)}
                            className="px-4 py-2 bg-gradient-to-r from-red-600 to-rose-500 text-white rounded-lg text-sm font-medium hover:from-red-700 hover:to-rose-600 transition-colors shadow-sm"
                          >
                            Reject Application
                          </button>
                        </div>
                      )}
                    </div>
                  </>
                );
              })()}
            </div>
          </GlassCard>
        </div>
      )}
    </div>
  );
} 