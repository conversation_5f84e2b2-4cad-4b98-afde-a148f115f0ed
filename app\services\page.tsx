"use client"

import { useState } from "react"
import { CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import {
  GraduationCap,
  FileText,
  Video,
  Award,
  Building,
  PenTool,
  MessageSquare,
  Wallet,
  Briefcase,
  User,
  Home,
  ArrowRight
} from "lucide-react"
import useLeadForm from "@/hooks/useLeadForm"
import { ReactNode } from "react"

interface GlassCardProps {
  children: ReactNode;
  className?: string;
}

function GlassCard({ children, className = "" }: GlassCardProps) {
  return (
    <div
      className={`rounded-xl p-4 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl ${className}`}
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {children}
    </div>
  )
}

export default function ServicesPage() {
  // For mock interviews
  const { 
    openLeadForm: openMockInterviewForm, 
    LeadFormComponent: MockInterviewForm 
  } = useLeadForm({
    defaultService: "Mock Interview",
    source: "Services Page",
    formType: "mock-interview"
  });

  // For counseling sessions
  const { 
    openLeadForm: openCounselingForm, 
    LeadFormComponent: CounselingForm 
  } = useLeadForm({
    defaultService: "Counseling Session",
    source: "Services Page",
    formType: "counseling"
  });

  // Generic lead form for other services
  const [currentService, setCurrentService] = useState("");
  const { 
    openLeadForm: openServiceForm, 
    LeadFormComponent: ServiceForm 
  } = useLeadForm({
    defaultService: currentService,
    source: "Services Page",
    formType: "general"
  });

  // Handler for service buttons
  const handleServiceClick = (service: string) => {
    setCurrentService(service);
    openServiceForm();
  };

  const services = [
    {
      id: 1,
      title: "Counseling Sessions",
      description:
        "Personalized guidance from experienced immigration counselors. Understand your options, clarify doubts, and set a clear path forward.",
      icon: <MessageSquare className="h-5 w-5 text-indigo-600" />,
      cta: "Book Now",
      action: () => openCounselingForm(),
      link: "/services/counseling",
      gradient: "from-blue-600 to-indigo-700"
    },
    {
      id: 2,
      title: "Visa Application Help",
      description:
        "End-to-end support for paperwork, financial documentation, and interview preparation for US, Canada, UK, Germany, Australia, and more.",
      icon: <FileText className="h-5 w-5 text-indigo-600" />,
      cta: "Get Started",
      action: () => handleServiceClick("Visa Application Help"),
      link: "/services/visa-application",
      gradient: "from-indigo-600 to-purple-700"
    },
    {
      id: 3,
      title: "Research Paper Drafting & Publishing",
      description:
        "Boost your academic and visa profile with credible research publications-essential for MS/PhD and talent-based visa applicants.",
      icon: <PenTool className="h-5 w-5 text-indigo-600" />,
      cta: "Learn More",
      action: () => handleServiceClick("Research Paper Service"),
      link: "/services/research-paper",
      gradient: "from-purple-600 to-pink-600"
    },
    {
      id: 4,
      title: "Talent-Based Visa Support",
      description:
        "Expert guidance for EB-1, O-1, Australia National Innovation, and UK Global Talent visas for professionals with extraordinary abilities.",
      icon: <Award className="h-5 w-5 text-indigo-600" />,
      cta: "Explore Options",
      action: () => handleServiceClick("Talent-Based Visa Support"),
      link: "/services/talent-visa",
      gradient: "from-blue-600 to-indigo-700"
    },
    {
      id: 5,
      title: "University Shortlisting",
      description:
        "Expert evaluation of your profile and guidance to shortlist the best-fit universities without wasting time or money.",
      icon: <Building className="h-5 w-5 text-indigo-600" />,
      cta: "Get Recommendations",
      action: () => handleServiceClick("University Shortlisting"),
      link: "/services/university-shortlisting",
      gradient: "from-indigo-600 to-purple-700"
    },
    {
      id: 6,
      title: "Statement of Purpose & Essay Drafting",
      description: "Custom-written, plagiarism-free essays and SOPs tailored to your story and goals.",
      icon: <PenTool className="h-5 w-5 text-indigo-600" />,
      cta: "Start Writing",
      action: () => handleServiceClick("SOP & Essay Drafting"),
      link: "/services/sop-essay",
      gradient: "from-purple-600 to-pink-600"
    },
    {
      id: 7,
      title: "Mock Interviews",
      description: "Simulated visa interviews with actionable feedback to maximize your success.",
      icon: <Video className="h-5 w-5 text-indigo-600" />,
      cta: "Book Session",
      action: () => openMockInterviewForm(),
      link: "/services/mock-interviews",
      gradient: "from-blue-600 to-indigo-700"
    },
    {
      id: 8,
      title: "Financial Documentation",
      description: "Comprehensive support to prepare and validate financial documents for visa applications.",
      icon: <Wallet className="h-5 w-5 text-indigo-600" />,
      cta: "Get Help",
      action: () => handleServiceClick("Financial Documentation"),
      link: "/services/financial-docs",
      gradient: "from-indigo-600 to-purple-700"
    },
    {
      id: 9,
      title: "Scholarship Application Assistance",
      description: "Maximize your chances of securing scholarships and fellowships.",
      icon: <GraduationCap className="h-5 w-5 text-indigo-600" />,
      cta: "Find Scholarships",
      action: () => handleServiceClick("Scholarship Application"),
      link: "/services/scholarships",
      gradient: "from-purple-600 to-pink-600"
    },
    {
      id: 10,
      title: "Job Application Support",
      description:
        "Guidance for job applications, resume building, and interview preparation for global opportunities.",
      icon: <Briefcase className="h-5 w-5 text-indigo-600" />,
      cta: "Get Started",
      action: () => handleServiceClick("Job Application Support"),
      link: "/services/job-support",
      gradient: "from-blue-600 to-indigo-700"
    },
    {
      id: 11,
      title: "Profile Building & LinkedIn Optimization",
      description: "Enhance your online presence and professional brand to attract universities and employers.",
      icon: <User className="h-5 w-5 text-indigo-600" />,
      cta: "Optimize Profile",
      action: () => handleServiceClick("Profile Building"),
      link: "/services/profile-building",
      gradient: "from-indigo-600 to-purple-700"
    },
    {
      id: 12,
      title: "Housing Search",
      description: "Assistance in finding safe and affordable accommodation in your destination country.",
      icon: <Home className="h-5 w-5 text-indigo-600" />,
      cta: "Find Housing",
      action: () => handleServiceClick("Housing Search"),
      link: "/services/housing",
      gradient: "from-purple-600 to-pink-600"
    },
  ]

  return (
    <div className="py-12 md:py-16 bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Visa Mentor Services
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Comprehensive support for every step of your international education and immigration journey.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          {services.map((service) => (
            <GlassCard key={service.id} className="transform transition hover:translate-y-[-4px]">
              <CardContent className="p-6">
                <div className="mb-4 bg-indigo-50/50 p-3 rounded-full inline-block">
                  {service.icon}
                </div>
                <h2 className="text-lg font-semibold text-indigo-800 mb-2">{service.title}</h2>
                <p className="text-sm text-gray-600 mb-6">{service.description}</p>
                <div className="flex items-center justify-between">
                <Button 
                    className={`bg-gradient-to-r ${service.gradient} text-white rounded-lg py-2 px-4 font-medium hover:opacity-90 transition shadow-md flex items-center`}
                  onClick={service.action}
                >
                  {service.cta}
                    <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
                  
                  <Link href={service.link} className="text-xs text-indigo-600 hover:underline">
                    View details
                  </Link>
                </div>
              </CardContent>
            </GlassCard>
          ))}
        </div>

        <div className="mt-16 text-center">
          <GlassCard className="max-w-2xl mx-auto transform transition hover:translate-y-[-4px] py-8">
            <div className="p-4">
              <h2 className="text-lg font-semibold text-indigo-800 mb-4">Need a Custom Service?</h2>
              <p className="text-sm text-gray-600 mb-6">
            If you need a service not listed above, our team will create a tailored solution for you.
          </p>
          <Button 
                className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-3 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center mx-auto"
            onClick={() => handleServiceClick("Custom Service")}
          >
                <MessageSquare className="h-4 w-4 mr-2" />
            Request Custom Service
          </Button>
            </div>
          </GlassCard>
        </div>
      </div>
      
      {/* Render all the form components */}
      <MockInterviewForm />
      <CounselingForm />
      <ServiceForm />
    </div>
  )
}
