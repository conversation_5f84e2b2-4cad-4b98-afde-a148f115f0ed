import { prisma } from '@/lib/prisma'
import { Application, ApplicationStatus, VisaStage } from '@prisma/client'

export interface CreateApplicationData {
  userId: string
  type: string
  status?: ApplicationStatus
  progress?: number
  currentStage?: VisaStage
  documents?: any
  notes?: string
  counselorId?: string
  estimatedCompletion?: Date
}

export interface UpdateApplicationData {
  type?: string
  status?: ApplicationStatus
  progress?: number
  currentStage?: VisaStage
  documents?: any
  notes?: string
  counselorId?: string
  estimatedCompletion?: Date
}

export class ApplicationService {
  // Create new application
  static async createApplication(data: CreateApplicationData): Promise<Application> {
    try {
      return await prisma.application.create({
        data: {
          ...data,
          submittedAt: data.status === 'SUBMITTED' ? new Date() : null,
        },
      })
    } catch (error) {
      console.error('Error creating application:', error)
      throw new Error('Failed to create application')
    }
  }

  // Get applications for user
  static async getUserApplications(userId: string): Promise<Application[]> {
    try {
      return await prisma.application.findMany({
        where: { userId },
        orderBy: { updatedAt: 'desc' },
      })
    } catch (error) {
      console.error('Error fetching applications:', error)
      return []
    }
  }

  // Get application by ID
  static async getApplicationById(id: string, userId: string): Promise<Application | null> {
    try {
      return await prisma.application.findFirst({
        where: { 
          id,
          userId // Ensure user can only access their own applications
        },
      })
    } catch (error) {
      console.error('Error fetching application:', error)
      return null
    }
  }

  // Update application
  static async updateApplication(id: string, userId: string, data: UpdateApplicationData): Promise<Application | null> {
    try {
      return await prisma.application.update({
        where: { id },
        data: {
          ...data,
          submittedAt: data.status === 'SUBMITTED' ? new Date() : undefined,
        },
      })
    } catch (error) {
      console.error('Error updating application:', error)
      throw new Error('Failed to update application')
    }
  }

  // Delete application
  static async deleteApplication(id: string, userId: string): Promise<boolean> {
    try {
      await prisma.application.delete({
        where: { 
          id,
          userId
        }
      })
      return true
    } catch (error) {
      console.error('Error deleting application:', error)
      return false
    }
  }

  // Update application progress
  static async updateProgress(id: string, userId: string, progress: number, stage?: VisaStage): Promise<Application | null> {
    try {
      return await prisma.application.update({
        where: { 
          id,
          userId
        },
        data: {
          progress: Math.min(100, Math.max(0, progress)),
          currentStage: stage,
        },
      })
    } catch (error) {
      console.error('Error updating application progress:', error)
      return null
    }
  }

  // Get application statistics
  static async getApplicationStats(userId: string) {
    try {
      const [
        total,
        inProgress,
        completed,
        pending
      ] = await Promise.all([
        prisma.application.count({
          where: { userId }
        }),
        prisma.application.count({
          where: { 
            userId,
            status: {
              in: ['SUBMITTED', 'IN_REVIEW', 'PENDING_DOCUMENTS', 'INTERVIEW_SCHEDULED']
            }
          }
        }),
        prisma.application.count({
          where: { 
            userId,
            status: {
              in: ['APPROVED', 'COMPLETED']
            }
          }
        }),
        prisma.application.count({
          where: { 
            userId,
            status: 'DRAFT'
          }
        })
      ])

      return {
        total,
        inProgress,
        completed,
        pending
      }
    } catch (error) {
      console.error('Error fetching application stats:', error)
      return {
        total: 0,
        inProgress: 0,
        completed: 0,
        pending: 0
      }
    }
  }
}
