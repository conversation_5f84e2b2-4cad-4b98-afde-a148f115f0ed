"use client";

import { useState } from "react";
import { 
  Search, 
  FileText, 
  Upload, 
  Filter, 
  Download, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  ChevronDown,
  ChevronRight,
  Clipboard,
  ClipboardCheck,
  Save,
  Plus,
  Trash2,
  QrCode,
  Share2,
  X,
  <PERSON><PERSON>p<PERSON><PERSON>,
  FilePlus
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock data for client documents
const clients = [
  {
    id: "CL-2023-001",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    visaType: "EB-1",
    stage: "Document Collection",
    completionStatus: 65,
    documents: [
      { id: "DOC-1001", name: "Passport", status: "Approved", uploadedDate: "2023-05-10T09:30:00", expiryDate: "2030-05-09T00:00:00" },
      { id: "DOC-1002", name: "CV/Resume", status: "Approved", uploadedDate: "2023-05-10T10:15:00", expiryDate: null },
      { id: "DOC-1003", name: "Educational Certificates", status: "Approved", uploadedDate: "2023-05-12T14:20:00", expiryDate: null },
      { id: "DOC-1004", name: "Employment Verification", status: "Pending Review", uploadedDate: "2023-05-15T16:45:00", expiryDate: null },
      { id: "DOC-1005", name: "Publication Evidence", status: "Pending", uploadedDate: null, expiryDate: null },
      { id: "DOC-1006", name: "Reference Letters", status: "Rejected", uploadedDate: "2023-05-13T11:30:00", expiryDate: null, reason: "Signature missing" },
      { id: "DOC-1007", name: "Financial Statements", status: "Pending", uploadedDate: null, expiryDate: null },
    ]
  },
  {
    id: "CL-2023-002",
    name: "Sneha Reddy",
    email: "<EMAIL>",
    visaType: "Student Visa",
    stage: "Interview Preparation",
    completionStatus: 90,
    documents: [
      { id: "DOC-2001", name: "Passport", status: "Approved", uploadedDate: "2023-05-05T09:30:00", expiryDate: "2028-05-04T00:00:00" },
      { id: "DOC-2002", name: "University Acceptance Letter", status: "Approved", uploadedDate: "2023-05-07T14:30:00", expiryDate: "2023-09-01T00:00:00" },
      { id: "DOC-2003", name: "Financial Documents", status: "Approved", uploadedDate: "2023-05-09T11:20:00", expiryDate: null },
      { id: "DOC-2004", name: "Statement of Purpose", status: "Approved", uploadedDate: "2023-05-10T15:40:00", expiryDate: null },
      { id: "DOC-2005", name: "Academic Transcripts", status: "Approved", uploadedDate: "2023-05-08T10:15:00", expiryDate: null },
      { id: "DOC-2006", name: "Scholarship Letter", status: "Approved", uploadedDate: "2023-05-12T09:50:00", expiryDate: null },
      { id: "DOC-2007", name: "IELTS Results", status: "Pending Review", uploadedDate: "2023-05-16T13:45:00", expiryDate: "2025-05-15T00:00:00" },
    ]
  },
  {
    id: "CL-2023-003",
    name: "Rajat Gupta",
    email: "<EMAIL>",
    visaType: "H1-B",
    stage: "Application Review",
    completionStatus: 75,
    documents: [
      { id: "DOC-3001", name: "Passport", status: "Approved", uploadedDate: "2023-04-20T10:30:00", expiryDate: "2029-04-19T00:00:00" },
      { id: "DOC-3002", name: "CV/Resume", status: "Approved", uploadedDate: "2023-04-22T11:15:00", expiryDate: null },
      { id: "DOC-3003", name: "Employment Contract", status: "Approved", uploadedDate: "2023-04-25T14:20:00", expiryDate: "2026-04-24T00:00:00" },
      { id: "DOC-3004", name: "Educational Certificates", status: "Approved", uploadedDate: "2023-04-27T09:30:00", expiryDate: null },
      { id: "DOC-3005", name: "Company Job Description", status: "Approved", uploadedDate: "2023-05-02T16:45:00", expiryDate: null },
      { id: "DOC-3006", name: "LCA Filing Receipt", status: "Pending Review", uploadedDate: "2023-05-10T13:20:00", expiryDate: null },
      { id: "DOC-3007", name: "Previous Visa Copies", status: "Pending", uploadedDate: null, expiryDate: null },
      { id: "DOC-3008", name: "I-94 Record", status: "Pending", uploadedDate: null, expiryDate: null },
    ]
  }
];

// Document requirements by visa type
const documentRequirements = {
  "EB-1": [
    { name: "Passport", required: true, description: "Valid passport with at least 6 months validity remaining" },
    { name: "CV/Resume", required: true, description: "Detailed CV showing extraordinary ability in field" },
    { name: "Educational Certificates", required: true, description: "All degree certificates and transcripts" },
    { name: "Employment Verification", required: true, description: "Letters from current and previous employers" },
    { name: "Publication Evidence", required: true, description: "Copies of published work or citations" },
    { name: "Reference Letters", required: true, description: "Letters from experts in the field testifying to achievements" },
    { name: "Financial Statements", required: false, description: "Bank statements or other financial documents" },
    { name: "Awards and Recognitions", required: false, description: "Certificates of awards or recognitions received" },
  ],
  "Student Visa": [
    { name: "Passport", required: true, description: "Valid passport with at least 6 months validity remaining" },
    { name: "University Acceptance Letter", required: true, description: "Official acceptance letter from educational institution" },
    { name: "Financial Documents", required: true, description: "Bank statements or sponsorship letters showing ability to pay tuition and living expenses" },
    { name: "Statement of Purpose", required: true, description: "Essay explaining educational goals and plans to return to home country" },
    { name: "Academic Transcripts", required: true, description: "Previous academic records and transcripts" },
    { name: "Scholarship Letter", required: false, description: "Letter confirming scholarship, if applicable" },
    { name: "IELTS/TOEFL Results", required: true, description: "English language proficiency test results" },
    { name: "Photograph", required: true, description: "Recent passport-sized photographs" },
  ],
  "H1-B": [
    { name: "Passport", required: true, description: "Valid passport with at least 6 months validity remaining" },
    { name: "CV/Resume", required: true, description: "Detailed resume showing qualifications and experience" },
    { name: "Employment Contract", required: true, description: "Contract or offer letter from U.S. employer" },
    { name: "Educational Certificates", required: true, description: "Degree certificates and transcripts" },
    { name: "Company Job Description", required: true, description: "Detailed job description from employer" },
    { name: "LCA Filing Receipt", required: true, description: "Labor Condition Application filing receipt" },
    { name: "Previous Visa Copies", required: false, description: "Copies of previously issued U.S. visas" },
    { name: "I-94 Record", required: false, description: "I-94 record if currently in the U.S." },
    { name: "Pay Stubs", required: false, description: "Recent pay stubs if currently employed in the U.S." },
  ],
  "O-1": [
    { name: "Passport", required: true, description: "Valid passport with at least 6 months validity remaining" },
    { name: "CV/Resume", required: true, description: "Detailed CV showing extraordinary ability in field" },
    { name: "Employment Contract", required: true, description: "Contract or offer letter from U.S. employer" },
    { name: "Press Coverage", required: true, description: "Media articles featuring the applicant's work" },
    { name: "Awards and Recognitions", required: true, description: "Evidence of national or international awards" },
    { name: "Reference Letters", required: true, description: "Letters from experts in the field" },
    { name: "Portfolio", required: true, description: "Portfolio of work or performances" },
    { name: "Salary Evidence", required: false, description: "Evidence of high salary or remuneration" },
  ],
};

// Function to get status color
function getStatusColor(status: string) {
  switch (status) {
    case "Approved":
      return "text-green-700";
    case "Pending Review":
      return "text-amber-700";
    case "Rejected":
      return "text-red-700";
    default:
      return "text-indigo-400";
  }
}

// Function to get status badge
function getStatusBadge(status: string) {
  switch (status) {
    case "Approved":
      return "bg-green-100/70 text-green-800";
    case "Pending Review":
      return "bg-amber-100/70 text-amber-800";
    case "Rejected":
      return "bg-red-100/70 text-red-800";
    case "Pending":
      return "bg-indigo-100/70 text-indigo-800";
    default:
      return "bg-gray-100/70 text-gray-800";
  }
}

// Function to get status icon
function getStatusIcon(status: string) {
  switch (status) {
    case "Approved":
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    case "Pending Review":
      return <Clock className="h-5 w-5 text-amber-600" />;
    case "Rejected":
      return <AlertCircle className="h-5 w-5 text-red-600" />;
    default:
      return <FileText className="h-5 w-5 text-indigo-400" />;
  }
}

// Function to format date
function formatDate(dateString: string | null) {
  if (!dateString) return "—";
  return new Date(dateString).toLocaleDateString("en-US", {
    day: "numeric",
    month: "short",
    year: "numeric"
  });
}

export default function DocumentsPage() {
  const [selectedClient, setSelectedClient] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showChecklistGenerator, setShowChecklistGenerator] = useState(false);
  const [selectedVisaType, setSelectedVisaType] = useState("EB-1");
  const [customChecklist, setCustomChecklist] = useState<Array<{ name: string; required: boolean; description: string }>>([]);
  const [newDocumentName, setNewDocumentName] = useState("");
  const [newDocumentRequired, setNewDocumentRequired] = useState(true);
  const [newDocumentDescription, setNewDocumentDescription] = useState("");
  const [clipboardSuccess, setClipboardSuccess] = useState(false);
  
  // Filter clients based on search query
  const filteredClients = clients.filter(client => {
    const matchesSearch = 
      client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      client.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesSearch;
  });
  
  // Get current client detail
  const currentClient = clients.find(client => client.id === selectedClient);
  
  // Generate document checklist
  const generateChecklist = () => {
    setCustomChecklist([...documentRequirements[selectedVisaType as keyof typeof documentRequirements]]);
    setShowChecklistGenerator(true);
  };
  
  // Add custom document to checklist
  const addCustomDocument = () => {
    if (newDocumentName.trim()) {
      setCustomChecklist([
        ...customChecklist,
        {
          name: newDocumentName,
          required: newDocumentRequired,
          description: newDocumentDescription || "Custom document"
        }
      ]);
      setNewDocumentName("");
      setNewDocumentDescription("");
    }
  };
  
  // Remove document from checklist
  const removeDocument = (index: number) => {
    const updatedChecklist = [...customChecklist];
    updatedChecklist.splice(index, 1);
    setCustomChecklist(updatedChecklist);
  };
  
  // Copy checklist to clipboard
  const copyToClipboard = () => {
    const checklistText = customChecklist.map(doc => 
      `${doc.required ? '[REQUIRED]' : '[OPTIONAL]'} ${doc.name}: ${doc.description}`
    ).join('\n\n');
    
    navigator.clipboard.writeText(checklistText).then(() => {
      setClipboardSuccess(true);
      setTimeout(() => setClipboardSuccess(false), 2000);
    });
  };
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 md:gap-0">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Document Management
          </h1>
          <p className="text-gray-600 mt-1">Track and manage client documents and requirements</p>
        </div>
        
        <div className="flex gap-3 self-end">
          <button 
            className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-indigo-50/70 transition-colors shadow-sm flex items-center"
            onClick={() => setShowChecklistGenerator(!showChecklistGenerator)}
          >
            <Clipboard className="h-4 w-4 mr-2" />
            Document Checklist
          </button>
          <button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
            <Upload className="h-4 w-4 mr-2" />
            Upload Documents
          </button>
        </div>
      </div>
      
      {/* Search and filters */}
      <GlassCard className="p-4">
        <div className="relative flex-1 min-w-0">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-indigo-400" size={18} />
          <input
            type="text"
            placeholder="Search clients by name, email or ID..."
            className="pl-10 pr-4 py-2 border border-indigo-200/60 rounded-lg w-full bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </GlassCard>
      
      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Clients list */}
        <GlassCard className="p-0 overflow-hidden">
          <div className="p-4 border-b border-indigo-100/30 bg-indigo-50/50 backdrop-blur-sm">
            <h2 className="text-lg font-semibold text-indigo-800">Clients</h2>
          </div>
          <ul className="divide-y divide-indigo-100/30">
            {filteredClients.map(client => (
              <li 
                key={client.id}
                className={`cursor-pointer transition-all duration-200 ${
                  selectedClient === client.id 
                    ? 'bg-indigo-50/70 border-l-4 border-l-indigo-700' 
                    : 'hover:bg-indigo-50/30 border-l-4 border-l-transparent'
                }`}
                onClick={() => setSelectedClient(client.id)}
              >
                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-indigo-800">{client.name}</h3>
                      <p className="text-sm text-gray-600">{client.email}</p>
                    </div>
                    <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
                      {client.visaType}
                    </span>
                  </div>
                  
                  <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <div className="text-xs text-indigo-600 font-medium">Stage</div>
                      <div className="text-indigo-800">{client.stage}</div>
                    </div>
                    <div>
                      <div className="text-xs text-indigo-600 font-medium">Completion</div>
                      <div className="flex items-center">
                        <div className="h-1.5 w-16 bg-indigo-100/40 rounded-full overflow-hidden mr-2">
                          <div 
                            className={`h-full rounded-full ${
                              client.completionStatus >= 80 ? 'bg-gradient-to-r from-green-500 to-emerald-400' : 
                              client.completionStatus >= 50 ? 'bg-gradient-to-r from-blue-500 to-indigo-400' : 
                              'bg-gradient-to-r from-amber-500 to-orange-400'
                            }`}
                            style={{ width: `${client.completionStatus}%` }}
                          ></div>
                        </div>
                        <span className="text-indigo-800 font-medium">{client.completionStatus}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </GlassCard>
        
        {/* Document list */}
        <GlassCard className={`p-0 overflow-hidden lg:col-span-2 ${!selectedClient ? 'flex items-center justify-center' : ''}`}>
          {!selectedClient ? (
            <div className="text-center p-8">
              <FileText className="h-12 w-12 text-indigo-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-indigo-800 mb-2">No Client Selected</h3>
              <p className="text-gray-600">Select a client from the list to view their documents</p>
            </div>
          ) : (
            <div>
              <div className="p-4 border-b border-indigo-100/30 bg-indigo-50/50 backdrop-blur-sm">
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-lg font-semibold text-indigo-800">{currentClient?.name}'s Documents</h2>
                    <p className="text-sm text-indigo-600">{currentClient?.visaType} • {currentClient?.stage}</p>
                  </div>
                  <div className="flex gap-2">
                    <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                      <Download className="h-4 w-4" />
                    </button>
                    <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                      <Share2 className="h-4 w-4" />
                    </button>
                    <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                      <FilePlus className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="p-4">
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-semibold text-indigo-800">Document Status</h3>
                    <span className="text-xs text-indigo-600">{currentClient?.documents.filter(doc => doc.status === "Approved").length} of {currentClient?.documents.length} approved</span>
                  </div>
                  <div className="h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-gradient-to-r from-blue-500 to-indigo-400 rounded-full"
                      style={{ width: `${currentClient?.completionStatus}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-2 mb-4">
                  <div className="flex justify-between items-center px-2 py-1">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-indigo-600" />
                      <span className="text-sm font-medium text-indigo-800">Document Name</span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-indigo-600 font-medium">
                      <span>Status</span>
                      <span className="ml-4">Uploaded</span>
                      <span className="ml-4">Expiry</span>
                    </div>
                  </div>
                </div>
                
                <ul className="space-y-2">
                  {currentClient?.documents.map(document => (
                    <li key={document.id} className="bg-white/50 backdrop-blur-sm border border-indigo-100/30 rounded-lg overflow-hidden transition-all duration-300 hover:shadow-md">
                      <div className="p-4">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center">
                            {getStatusIcon(document.status)}
                            <span className="ml-3 font-medium text-indigo-800">{document.name}</span>
                          </div>
                          <div className="flex items-center gap-4">
                            <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getStatusBadge(document.status)}`}>
                              {document.status}
                            </span>
                            <span className="text-xs text-gray-600 w-24 text-right">{document.uploadedDate ? formatDate(document.uploadedDate) : "Not uploaded"}</span>
                            <span className="text-xs text-gray-600 w-24 text-right">{document.expiryDate ? formatDate(document.expiryDate) : "N/A"}</span>
                          </div>
                        </div>
                        
                        {document.status === "Rejected" && document.reason && (
                          <div className="mt-2 ml-8 text-xs text-red-600">
                            Reason: {document.reason}
                          </div>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
                
                <div className="flex justify-end mt-4">
                  <button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Missing Documents
                  </button>
                </div>
              </div>
            </div>
          )}
        </GlassCard>
      </div>
      
      {/* Document Checklist Generator */}
      {showChecklistGenerator && (
        <GlassCard className="p-6 mt-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-indigo-800">Document Checklist Generator</h2>
            <button 
              onClick={() => setShowChecklistGenerator(false)} 
              className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-indigo-800 mb-1">Select Visa Type</label>
                <div className="relative">
                  <select
                    className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg w-full bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    value={selectedVisaType}
                    onChange={(e) => setSelectedVisaType(e.target.value)}
                  >
                    {Object.keys(documentRequirements).map(visaType => (
                      <option key={visaType} value={visaType}>{visaType}</option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <ChevronDown className="h-4 w-4 text-indigo-500" />
                  </div>
                </div>
              </div>
              
              <button 
                className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-indigo-700 hover:to-purple-800 transition-all shadow-md flex items-center"
                onClick={generateChecklist}
              >
                <Clipboard className="h-4 w-4 mr-2" />
                Generate Checklist
              </button>
              
              <div className="mt-6">
                <h3 className="text-sm font-semibold text-indigo-800 mb-3">Add Custom Document</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs text-indigo-600 mb-1">Document Name</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-indigo-200/60 rounded-lg bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      value={newDocumentName}
                      onChange={(e) => setNewDocumentName(e.target.value)}
                      placeholder="e.g., Travel History"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-xs text-indigo-600 mb-1">Description</label>
                    <textarea
                      className="w-full px-3 py-2 border border-indigo-200/60 rounded-lg bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      value={newDocumentDescription}
                      onChange={(e) => setNewDocumentDescription(e.target.value)}
                      placeholder="Document description..."
                      rows={3}
                    ></textarea>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="documentRequired"
                      checked={newDocumentRequired}
                      onChange={() => setNewDocumentRequired(!newDocumentRequired)}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-indigo-300 rounded"
                    />
                    <label htmlFor="documentRequired" className="ml-2 text-sm text-indigo-800">
                      Required Document
                    </label>
                  </div>
                  
                  <button 
                    className="w-full bg-white/80 backdrop-blur-sm border border-indigo-200/60 py-2 px-4 rounded-lg text-indigo-700 text-sm font-medium hover:bg-indigo-50/70 transition-colors flex items-center justify-center"
                    onClick={addCustomDocument}
                  >
                    <Plus className="h-4 w-4 mr-1.5" />
                    Add Document
                  </button>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-indigo-800 mb-3">Document Checklist</h3>
              
              {customChecklist.length === 0 ? (
                <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-6 text-center">
                  <Clipboard className="h-8 w-8 text-indigo-300 mx-auto mb-2" />
                  <p className="text-indigo-800">No documents in checklist yet</p>
                  <p className="text-xs text-indigo-600 mt-1">Select a visa type and click "Generate Checklist" to get started</p>
                </div>
              ) : (
                <div>
                  <ul className="divide-y divide-indigo-100/30 border border-indigo-100/30 rounded-lg overflow-hidden bg-white/80 backdrop-blur-lg">
                    {customChecklist.map((document, index) => (
                      <li key={index} className="p-3 flex justify-between items-center">
                        <div>
                          <div className="flex items-center">
                            {document.required ? (
                              <span className="text-xs px-2 py-0.5 bg-indigo-100/70 text-indigo-800 rounded-full mr-2">Required</span>
                            ) : (
                              <span className="text-xs px-2 py-0.5 bg-gray-100/70 text-gray-800 rounded-full mr-2">Optional</span>
                            )}
                            <span className="font-medium text-indigo-800">{document.name}</span>
                          </div>
                          <p className="text-xs text-gray-600 mt-1">{document.description}</p>
                        </div>
                        <button 
                          onClick={() => removeDocument(index)}
                          className="p-1.5 text-red-600 hover:text-red-900 bg-red-50/70 backdrop-blur-sm rounded-lg transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </li>
                    ))}
                  </ul>
                  
                  <div className="flex justify-between mt-4">
                    <div>
                      <span className="text-xs text-indigo-600 mr-2">
                        {customChecklist.filter(doc => doc.required).length} required,
                        {customChecklist.filter(doc => !doc.required).length} optional
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <button 
                        onClick={copyToClipboard}
                        className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 py-2 px-4 rounded-lg text-indigo-700 text-sm font-medium hover:bg-indigo-50/70 transition-colors flex items-center"
                      >
                        {clipboardSuccess ? (
                          <>
                            <ClipboardCheck className="h-4 w-4 mr-1.5" />
                            Copied!
                          </>
                        ) : (
                          <>
                            <Clipboard className="h-4 w-4 mr-1.5" />
                            Copy to Clipboard
                          </>
                        )}
                      </button>
                      
                      <button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
                        <Save className="h-4 w-4 mr-1.5" />
                        Save Checklist
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </GlassCard>
      )}
    </div>
  );
} 