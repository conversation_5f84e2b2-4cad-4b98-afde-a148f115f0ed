"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import BookingForm from "../../components/booking/BookingForm"
import TravelRecommendationList from "@/components/travel/TravelRecommendationList"
import UserProfileCard from "@/components/user/UserProfileCard"
import useLeadForm from "@/hooks/useLeadForm"
import { useUser } from "@clerk/nextjs"
import RoleBasedAccess from "@/components/auth/RoleBasedAccess"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowRight, Calendar, FileText, MessageSquare, Clock, CheckCircle, AlertCircle, Users } from "lucide-react"

interface Booking {
  id: string
  name: string
  email: string
  phone: string
  country: string
  visaType: string
  date: string
  time: string
  notes?: string
  status: string
  createdAt: string
}

interface TimelineEvent {
  date: string;
  title: string;
  description: string;
  status: "completed" | "current" | "upcoming";
}

interface Application {
  id: number;
  type: string;
  status: string;
  progress: number;
  lastUpdated: string;
}

interface Appointment {
  id: number;
  title: string;
  date: string;
  time: string;
  counselor: string;
}

interface Task {
  id: number;
  title: string;
  dueDate: string;
  priority: "High" | "Medium" | "Low";
}

interface Message {
  id: number;
  sender: string;
  content: string;
  timestamp: string;
}

const visaStages = [
  "Profile Evaluation",
  "Docs Collection",
  "Application Submission",
  "Biometrics",
  "Interview Scheduled",
  "Decision Pending",
  "Visa Approved",
  "Visa Granted"
];

function GanttProgress() {
  // For demo, let's say the user is at stage 4
  const currentStage = 4;
  return (
    <div className="flex flex-col gap-2 py-4 px-2">
      <div className="flex items-center relative">
        {visaStages.map((stage, idx) => (
          <div key={idx} className="flex-1 flex flex-col items-center">
            {/* Label above line (for even indices) */}
            {idx % 2 === 0 && (
              <div className="mb-3 text-center px-1">
                <span className={`text-xs ${idx < currentStage ? "text-indigo-700 font-bold" : "text-gray-400"}`}>
                  {stage}
                </span>
              </div>
            )}
            
            {/* Circle and connecting line */}
            <div className="flex items-center w-full">
              <div
                className={`h-4 w-4 rounded-full ${
                  idx < currentStage 
                    ? "bg-gradient-to-r from-blue-500 to-indigo-600 ring-2 ring-indigo-100" 
                    : "bg-gray-200"
                } border-2 border-white shadow-md z-10 ${
                  idx === currentStage - 1 ? "animate-pulse" : ""
                }`}
              />
              {idx < visaStages.length - 1 && (
                <div className={`flex-1 h-1 ${
                  idx < currentStage - 1 
                    ? "bg-gradient-to-r from-blue-500 to-indigo-600" 
                    : "bg-gray-200"
                }`} />
              )}
            </div>
            
            {/* Label below line (for odd indices) */}
            {idx % 2 !== 0 && (
              <div className="mt-3 text-center px-1">
                <span className={`text-xs ${idx < currentStage ? "text-indigo-700 font-bold" : "text-gray-400"}`}>
                  {stage}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

function GlassCard({ children, className = "" }: { children: React.ReactNode; className?: string }) {
  return (
    <div
      className={`rounded-xl p-4 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl ${className}`}
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {children}
    </div>
  )
}

function ErrorBoundary({ children }: { children: React.ReactNode }) {
  const [error, setError] = useState<Error | null>(null)
  if (error) {
    return (
      <GlassCard className="col-span-3 text-center">
        <h2 className="text-xl font-bold text-red-600 mb-2">Something went wrong</h2>
        <pre className="text-sm text-gray-700">{error.message}</pre>
      </GlassCard>
    )
  }
  // @ts-ignore
  return <>{children}</>
}

export default function UserDashboard() {
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [showBookingModal, setShowBookingModal] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const router = useRouter()
  const { isLoaded, isSignedIn, user } = useUser()

  // Consolidated dashboard data
  const [applications, setApplications] = useState<Application[]>([
    {
      id: 1,
      type: "Student Visa - USA",
      status: "In Progress",
      progress: 65,
      lastUpdated: "2 days ago",
    },
    {
      id: 2,
      type: "University Application - Stanford",
      status: "Under Review",
      progress: 80,
      lastUpdated: "1 week ago",
    },
  ])

  const [appointments, setAppointments] = useState<Appointment[]>([
    {
      id: 1,
      title: "Visa Interview Preparation",
      date: "May 15, 2025",
      time: "10:00 AM",
      counselor: "Rajiv Sharma",
    },
    {
      id: 2,
      title: "Document Review Session",
      date: "May 20, 2025",
      time: "2:30 PM",
      counselor: "Priya Patel",
    },
  ])

  const [tasks, setTasks] = useState<Task[]>([
    {
      id: 1,
      title: "Submit financial documents",
      dueDate: "May 14, 2025",
      priority: "High",
    },
    {
      id: 2,
      title: "Complete SOP draft",
      dueDate: "May 18, 2025",
      priority: "Medium",
    },
    {
      id: 3,
      title: "Prepare for mock interview",
      dueDate: "May 22, 2025",
      priority: "High",
    },
  ])

  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      sender: "Rajiv Sharma",
      content: "I've reviewed your documents and everything looks good. Let's discuss the next steps in our appointment.",
      timestamp: "2 hours ago",
    },
    {
      id: 2,
      sender: "Support Team",
      content: "Your appointment has been confirmed for May 15th at 10:00 AM. Please make sure to prepare all required documents.",
      timestamp: "1 day ago",
    },
  ])
  
  // Lead form integration
  const { openLeadForm, LeadFormComponent } = useLeadForm({
    defaultService: "Consultation",
    source: "User Dashboard",
    formType: "consultation"
  });

  // Check if user is authenticated and profile is completed
  useEffect(() => {
    const checkProfileCompletion = async () => {
      try {
        if (!isLoaded) return;

        if (!isSignedIn) {
          router.push('/sign-in');
          return;
        }

        // Continue with regular data fetching if user is signed in
        fetchBookings();
      } catch (err) {
        console.error('Error checking profile completion:', err);
        // Proceed anyway to prevent blocking the user
        fetchBookings();
      }
    };

    if (isMounted) {
      checkProfileCompletion();
    }
  }, [isMounted, isLoaded, isSignedIn, router]);

  const fetchBookings = async () => {
    setLoading(true)
    try {
      const res = await fetch("/api/bookings")
      if (!res.ok) throw new Error("Failed to fetch bookings")
      const data = await res.json()
      setBookings(data)
    } catch (err: any) {
      setError(err.message || "Unknown error")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Show loading state while mounting or checking auth
  if (!isMounted || !isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="animate-pulse flex space-x-4">
          <div className="rounded-full bg-indigo-200 h-12 w-12"></div>
          <div className="flex-1 space-y-4 py-1 max-w-lg">
            <div className="h-4 bg-indigo-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-indigo-200 rounded"></div>
              <div className="h-4 bg-indigo-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Find the next appointment (future date, soonest)
  const now = new Date()
  const nextBooking = bookings
    .map(b => ({ ...b, dateObj: new Date(b.date) }))
    .filter(b => b.dateObj > now)
    .sort((a, b) => a.dateObj.getTime() - b.dateObj.getTime())[0]

  // Sample timeline data
  const visaTimeline: TimelineEvent[] = [
    {
      date: "Mar 15, 2025",
      title: "Application Submitted",
      description: "Initial application documents submitted",
      status: "completed"
    },
    {
      date: "Apr 2, 2025",
      title: "Documents Verified",
      description: "All required documents have been verified",
      status: "completed"
    },
    {
      date: "Apr 18, 2025",
      title: "Biometrics Appointment",
      description: "Fingerprints and photo collected at visa center",
      status: "current"
    },
    {
      date: "May 5, 2025",
      title: "Visa Interview",
      description: "Scheduled interview at embassy",
      status: "upcoming"
    },
    {
      date: "May 20, 2025",
      title: "Visa Decision",
      description: "Final decision on visa application",
      status: "upcoming"
    }
  ];

  return (
    <RoleBasedAccess allowedRoles={['admin', 'sales-manager', 'sales-executive', 'crm', 'hr', 'user']}>
      <ErrorBoundary>
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-8 px-4">
        <div className="max-w-7xl mx-auto">

          {/* Header Section */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
                Welcome back{user?.firstName ? `, ${user.firstName}` : ''}!
              </h1>
              <p className="text-gray-600 mt-1">
                Here's your complete visa journey overview and dashboard.
              </p>
            </div>
            <Button
              className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
              onClick={openLeadForm}
            >
              Start New Application
            </Button>
          </div>

          {/* Stats Overview */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
            <GlassCard className="transform transition hover:translate-y-[-2px]">
              <div className="p-4">
                <div className="flex flex-row items-center justify-between pb-2">
                  <h3 className="text-sm font-medium text-indigo-800">Active Applications</h3>
                  <div className="bg-indigo-50/50 p-2 rounded-full">
                    <FileText className="h-4 w-4 text-indigo-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-indigo-800">{applications.length}</div>
                <p className="text-xs text-gray-600">+0 from last month</p>
              </div>
            </GlassCard>

            <GlassCard className="transform transition hover:translate-y-[-2px]">
              <div className="p-4">
                <div className="flex flex-row items-center justify-between pb-2">
                  <h3 className="text-sm font-medium text-indigo-800">Upcoming Appointments</h3>
                  <div className="bg-indigo-50/50 p-2 rounded-full">
                    <Calendar className="h-4 w-4 text-indigo-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-indigo-800">{appointments.length}</div>
                <p className="text-xs text-gray-600">Next: {appointments[0]?.date}</p>
              </div>
            </GlassCard>

            <GlassCard className="transform transition hover:translate-y-[-2px]">
              <div className="p-4">
                <div className="flex flex-row items-center justify-between pb-2">
                  <h3 className="text-sm font-medium text-indigo-800">Pending Tasks</h3>
                  <div className="bg-indigo-50/50 p-2 rounded-full">
                    <Clock className="h-4 w-4 text-indigo-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-indigo-800">{tasks.length}</div>
                <p className="text-xs text-gray-600">
                  {tasks.filter((t) => t.priority === "High").length} high priority
                </p>
              </div>
            </GlassCard>

            <GlassCard className="transform transition hover:translate-y-[-2px]">
              <div className="p-4">
                <div className="flex flex-row items-center justify-between pb-2">
                  <h3 className="text-sm font-medium text-indigo-800">Messages</h3>
                  <div className="bg-indigo-50/50 p-2 rounded-full">
                    <MessageSquare className="h-4 w-4 text-indigo-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-indigo-800">{messages.length}</div>
                <p className="text-xs text-gray-600">Latest: {messages[0]?.timestamp}</p>
              </div>
            </GlassCard>
          </div>

          {/* Top Row - User Profile on the left, Appointment and Progress on the right */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* User Profile Card - takes 1/3 on large screens */}
            <GlassCard className="lg:col-span-1 h-full transform transition hover:translate-y-[-4px]">
              <UserProfileCard />
            </GlassCard>
            
            {/* Wrapper for Next Appointment and Visa Progress Tracker - takes 2/3 on large screens */}
            <div className="lg:col-span-2 flex flex-col gap-6 h-full">
              {/* Next Appointment Section - now in its own GlassCard */}
              <GlassCard className="flex flex-col flex-[0.4] transform transition hover:translate-y-[-4px]">
                <h2 className="text-lg font-semibold mb-3 text-indigo-800 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Next Appointment
                </h2>
                <div className="flex-grow flex flex-col justify-center items-center py-4">
                  {loading ? (
                    <div className="flex justify-center items-center">
                      <div className="animate-pulse flex space-x-3">
                        <div className="rounded-full bg-indigo-200 h-8 w-8"></div>
                        <div className="flex-1 space-y-2 py-1">
                          <div className="h-2 bg-indigo-200 rounded"></div>
                          <div className="h-2 bg-indigo-200 rounded w-5/6"></div>
                        </div>
                      </div>
                    </div>
                  ) : nextBooking ? (
                    <div className="text-center bg-indigo-50 p-4 rounded-lg w-full max-w-md">
                      <div className="inline-block p-3 bg-indigo-100 rounded-full mb-3">
                        <svg className="w-6 h-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="font-bold text-lg mb-1 text-indigo-800">{nextBooking.visaType} Consultation</div>
                      <div className="text-sm text-indigo-600">{nextBooking.dateObj.toLocaleDateString()} at {nextBooking.time}</div>
                      <div className="text-xs text-indigo-400 mt-2">with {nextBooking.name}</div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center bg-indigo-50 p-6 rounded-lg w-full max-w-md">
                      <div className="bg-indigo-100 p-3 rounded-full mb-3">
                        <svg className="w-6 h-6 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="text-indigo-700 mb-3 font-medium text-center">No upcoming appointments</div>
                      <button 
                        onClick={openLeadForm}
                        className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-full py-2.5 px-5 text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Book Consultation
                      </button>
                    </div>
                  )}
                </div>
              </GlassCard>

              {/* Visa Progress Tracker - remains in its GlassCard, now within the new wrapper */}
              <GlassCard className="flex flex-col flex-[0.6] transform transition hover:translate-y-[-4px]">
                <div className="flex items-center mb-3">
                  <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <h2 className="text-lg font-semibold text-indigo-800">Visa Progress Tracker</h2>
                  <div className="relative ml-2 group">
                    <svg className="w-4 h-4 text-indigo-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div className="absolute left-0 bottom-full mb-2 w-48 p-2 rounded shadow-lg bg-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
                      Track your visa application progress through 8 key stages
                    </div>
                  </div>
                </div>
                <div className="flex-grow flex items-center justify-center bg-indigo-50/50 rounded-lg p-2">
                  <GanttProgress />
                </div>
              </GlassCard>
            </div>
          </div>

          {/* Tabbed Content Section */}
          <div className="mb-6">
            <Tabs defaultValue="timeline" className="space-y-4">
              <TabsList className="bg-white/70 backdrop-blur-sm border border-indigo-100/30 p-1">
                <TabsTrigger
                  value="timeline"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
                >
                  Visa Timeline
                </TabsTrigger>
                <TabsTrigger
                  value="applications"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
                >
                  Applications
                </TabsTrigger>
                <TabsTrigger
                  value="appointments"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
                >
                  Appointments
                </TabsTrigger>
                <TabsTrigger
                  value="tasks"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
                >
                  Tasks
                </TabsTrigger>
                <TabsTrigger
                  value="messages"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
                >
                  Messages
                </TabsTrigger>
              </TabsList>

              {/* Timeline Tab */}
              <TabsContent value="timeline">
                <GlassCard className="grid grid-cols-1 md:grid-cols-4 gap-6 transform transition hover:translate-y-[-4px]">
            {/* Timeline section - 3/4 width */}
            <div className="md:col-span-3">
              <h2 className="text-lg font-semibold mb-4 text-indigo-800 flex items-center">
                <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Visa Timeline
              </h2>
              <div className="overflow-auto pr-2 bg-indigo-50/50 rounded-lg p-4" style={{ maxHeight: "280px" }}>
                <div className="relative">
                  {/* Vertical line */}
                  <div className="absolute left-[90px] top-0 bottom-0 w-0.5 bg-indigo-200"></div>
                  
                  {visaTimeline.map((event, index) => (
                    <div key={index} className="relative flex items-start mb-5 last:mb-2 pl-1">
                      {/* Date with special styling */}
                      <div className="min-w-[70px] text-xs font-medium text-indigo-700 mr-8">
                        <span className="bg-white/80 px-2 py-1 rounded-full shadow-sm">{event.date}</span>
                      </div>
                      
                      {/* Timeline dot */}
                      <div className={`
                        absolute left-[90px] w-4 h-4 rounded-full border-2 border-white transform -translate-x-1/2 mt-0.5
                        ${event.status === "completed" ? "bg-green-500" : 
                           event.status === "current" ? "bg-blue-500" : "bg-indigo-300"}
                        z-10 ${event.status === "current" ? "animate-pulse" : ""}
                      `}></div>
                      
                      {/* Content */}
                      <div className={`
                        flex-grow ml-8 py-3 px-4 rounded-lg transition-all shadow-sm
                        ${event.status === "completed" ? "bg-green-50" : 
                           event.status === "current" ? "bg-blue-50" : "bg-indigo-50/70"}
                      `}>
                        <h3 className={`
                          text-sm font-semibold
                          ${event.status === "completed" ? "text-green-700" : 
                             event.status === "current" ? "text-blue-700" : "text-indigo-700"}
                        `}>{event.title}</h3>
                        <p className="text-xs text-gray-600 mt-1">{event.description}</p>
                        
                        {/* Status indicator */}
                        <div className="flex items-center mt-2">
                          {event.status === "completed" && (
                            <span className="inline-flex items-center text-xs font-medium text-green-700 bg-green-100 px-2 py-0.5 rounded-full">
                              <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              Completed
                            </span>
                          )}
                          {event.status === "current" && (
                            <span className="inline-flex items-center text-xs font-medium text-blue-700 bg-blue-100 px-2 py-0.5 rounded-full">
                              <svg className="w-3 h-3 mr-1 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              In Progress
                            </span>
                          )}
                          {event.status === "upcoming" && (
                            <span className="inline-flex items-center text-xs font-medium text-indigo-600 bg-indigo-100 px-2 py-0.5 rounded-full">
                              <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              Upcoming
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Quick Actions section - 1/4 width */}
            <div className="md:col-span-1 md:border-l md:pl-6 flex flex-col">
              <h2 className="text-lg font-semibold mb-4 text-indigo-800 flex items-center">
                <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Quick Actions
              </h2>
              <div className="flex flex-col gap-3 bg-indigo-50/50 rounded-lg p-4">
                <button 
                  onClick={() => setShowBookingModal(true)} 
                  className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-3 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center justify-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Book Consultation
                </button>
                
                <Link href="/user/documents" className="w-full">
                  <button className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-lg py-3 px-4 font-medium hover:from-indigo-700 hover:to-purple-800 transition shadow-md w-full flex items-center justify-center">
                    <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Upload Documents
                  </button>
                </Link>
                
                <Link href="/user/chat" className="w-full">
                  <button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg py-3 px-4 font-medium hover:from-purple-700 hover:to-pink-700 transition shadow-md w-full flex items-center justify-center">
                    <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                    Open Chat
                  </button>
                </Link>
              </div>
            </div>
                </GlassCard>
              </TabsContent>

              {/* Applications Tab */}
              <TabsContent value="applications" className="space-y-4">
                <div className="grid gap-4">
                  {applications.map((app) => (
                    <GlassCard key={app.id} className="transform transition hover:translate-y-[-2px]">
                      <div className="p-6">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="text-lg font-semibold text-indigo-800">{app.type}</h3>
                            <p className="text-sm text-gray-600">Status: {app.status}</p>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-gray-600 mr-4">{app.lastUpdated}</div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50/70"
                              asChild
                            >
                              <Link href={`/user/applications/${app.id}`}>
                                View <ArrowRight className="ml-1 h-4 w-4" />
                              </Link>
                            </Button>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2 mt-4">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Progress</span>
                            <span className="text-indigo-800 font-medium">{app.progress}%</span>
                          </div>
                          <div className="h-2 bg-indigo-100/40 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-gradient-to-r from-indigo-600 to-blue-400 rounded-full"
                              style={{ width: `${app.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </GlassCard>
                  ))}
                </div>
              </TabsContent>

              {/* Appointments Tab */}
              <TabsContent value="appointments" className="space-y-4">
                <div className="grid gap-4">
                  {appointments.map((appointment) => (
                    <GlassCard key={appointment.id} className="transform transition hover:translate-y-[-2px]">
                      <div className="p-6">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="text-lg font-semibold text-indigo-800">{appointment.title}</h3>
                            <p className="text-sm text-gray-600">With {appointment.counselor}</p>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-indigo-600 font-medium mr-4">{appointment.time}</div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50/70"
                              asChild
                            >
                              <Link href={`/user/appointments/${appointment.id}`}>
                                View <ArrowRight className="ml-1 h-4 w-4" />
                              </Link>
                            </Button>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 mt-4 text-sm text-gray-600">
                          <div className="bg-indigo-50/70 p-1.5 rounded-full">
                            <Calendar className="h-4 w-4 text-indigo-600" />
                          </div>
                          <span>{appointment.date}</span>
                        </div>
                      </div>
                    </GlassCard>
                  ))}
                </div>
              </TabsContent>

              {/* Tasks Tab */}
              <TabsContent value="tasks" className="space-y-4">
                <div className="grid gap-4">
                  {tasks.map((task) => (
                    <GlassCard key={task.id} className="transform transition hover:translate-y-[-2px]">
                      <div className="p-6">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="text-lg font-semibold text-indigo-800">{task.title}</h3>
                            <div className="flex items-center mt-1">
                              <Clock className="h-4 w-4 text-gray-600 mr-1" />
                              <p className="text-sm text-gray-600">Due: {task.dueDate}</p>
                            </div>
                          </div>
                          <div
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              task.priority === "High"
                                ? "bg-red-100/70 backdrop-blur-sm text-red-800"
                                : task.priority === "Medium"
                                  ? "bg-amber-100/70 backdrop-blur-sm text-amber-800"
                                  : "bg-green-100/70 backdrop-blur-sm text-green-800"
                            }`}
                          >
                            {task.priority}
                          </div>
                        </div>
                        <div className="flex justify-end mt-4">
                          <Button
                            size="sm"
                            className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg py-1 px-3 text-sm font-medium hover:from-indigo-700 hover:to-blue-700 transition-colors shadow-sm"
                          >
                            Mark as Complete
                          </Button>
                        </div>
                      </div>
                    </GlassCard>
                  ))}
                </div>
              </TabsContent>

              {/* Messages Tab */}
              <TabsContent value="messages" className="space-y-4">
                <GlassCard className="transform transition hover:translate-y-[-2px]">
                  <div className="p-6">
                    <h2 className="text-lg font-semibold text-indigo-800 mb-2">Recent Messages</h2>
                    <p className="text-sm text-gray-600 mb-4">Stay in touch with your counselors and support team</p>
                    <div className="space-y-4">
                      {messages.map((message) => (
                        <div key={message.id} className="flex items-start gap-4">
                          <div className="bg-gradient-to-br from-indigo-100 to-blue-50 p-3 rounded-full flex-shrink-0">
                            <MessageSquare className="h-5 w-5 text-indigo-600" />
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="font-semibold text-indigo-800">{message.sender}</h4>
                              <span className="text-xs text-gray-600">{message.timestamp}</span>
                            </div>
                            <p className="text-sm text-gray-600">{message.content}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-6 flex justify-center">
                      <Button
                        variant="outline"
                        className="border border-indigo-300 rounded-lg bg-white/80 backdrop-blur-sm text-indigo-600 hover:bg-indigo-50/70"
                        asChild
                      >
                        <Link href="/user/chat">View All Messages</Link>
                      </Button>
                    </div>
                  </div>
                </GlassCard>
              </TabsContent>
            </Tabs>
          </div>

          {/* Bottom Row - Travel Recommendations - Compact Version */}
          <GlassCard className="py-6 transform transition hover:translate-y-[-4px]">
            <h2 className="text-lg font-semibold mb-5 text-indigo-800 flex items-center">
              <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Travel Recommendations
              <span className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white text-xs font-medium px-2.5 py-1 rounded-full ml-2">Suggested for you</span>
            </h2>
            <div className="bg-indigo-50/50 p-4 rounded-lg">
              {isMounted && <TravelRecommendationList userId="mock-user-id" />}
            </div>
          </GlassCard>
        </div>
        
        {/* Render the lead form component */}
        <LeadFormComponent />
        
        {/* Old booking form modal, kept for backward compatibility */}
        {showBookingModal && (
          <BookingForm 
            isOpen={showBookingModal} 
            onClose={() => setShowBookingModal(false)} 
            onBookingSuccess={fetchBookings} 
          />
        )}
        </div>
      </ErrorBoundary>
    </RoleBasedAccess>
  )
}