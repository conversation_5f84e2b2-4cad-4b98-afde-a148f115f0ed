"use client"

import { useUser } from "@clerk/nextjs"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Loader2, Shield, AlertTriangle } from "lucide-react"

export type UserRole = 'admin' | 'sales-manager' | 'sales-executive' | 'crm' | 'hr' | 'user'

interface RoleBasedAccessProps {
  children: React.ReactNode
  allowedRoles: UserRole[]
  fallbackUrl?: string
  showUnauthorized?: boolean
}

// Mock function to get user role - in production, this would come from Clerk metadata
function getUserRole(user: any): UserRole {
  // For demo purposes, we'll determine role based on email or metadata
  // In production, you would set this in Clerk's user metadata
  const email = user?.emailAddresses?.[0]?.emailAddress?.toLowerCase()
  
  if (email?.includes('admin')) return 'admin'
  if (email?.includes('sales-manager') || email?.includes('manager')) return 'sales-manager'
  if (email?.includes('sales-executive') || email?.includes('executive')) return 'sales-executive'
  if (email?.includes('crm')) return 'crm'
  if (email?.includes('hr')) return 'hr'
  
  // Default role for regular users
  return 'user'
}

export default function RoleBasedAccess({ 
  children, 
  allowedRoles, 
  fallbackUrl = '/unauthorized',
  showUnauthorized = true 
}: RoleBasedAccessProps) {
  const { isLoaded, isSignedIn, user } = useUser()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)

  useEffect(() => {
    if (!isLoaded) return

    if (!isSignedIn) {
      router.push('/sign-in')
      return
    }

    if (user) {
      const role = getUserRole(user)
      setUserRole(role)
      
      const authorized = allowedRoles.includes(role)
      setIsAuthorized(authorized)
      
      if (!authorized && !showUnauthorized) {
        router.push(fallbackUrl)
      }
    }
  }, [isLoaded, isSignedIn, user, allowedRoles, router, fallbackUrl, showUnauthorized])

  // Loading state
  if (!isLoaded || isAuthorized === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">Verifying access permissions...</p>
        </div>
      </div>
    )
  }

  // Not signed in (should redirect, but show loading as fallback)
  if (!isSignedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-indigo-600 mx-auto mb-4" />
          <p className="text-gray-600">Redirecting to sign in...</p>
        </div>
      </div>
    )
  }

  // Unauthorized access
  if (!isAuthorized && showUnauthorized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white/80 backdrop-blur-lg rounded-xl shadow-lg p-8 text-center">
          <div className="mb-6">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Shield className="h-8 w-8 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p className="text-gray-600">
              You don't have permission to access this page. Your current role is <strong>{userRole}</strong>.
            </p>
          </div>
          
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-left">
                <h3 className="text-sm font-medium text-amber-800">Required Roles</h3>
                <p className="text-sm text-amber-700 mt-1">
                  This page requires one of the following roles: {allowedRoles.join(', ')}
                </p>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <button
              onClick={() => router.push('/')}
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-indigo-800 transition-all"
            >
              Go to Home
            </button>
            <button
              onClick={() => router.back()}
              className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-all"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Authorized access
  return <>{children}</>
}

// Helper hook to get current user role
export function useUserRole(): { role: UserRole | null; isLoading: boolean } {
  const { isLoaded, user } = useUser()
  const [role, setRole] = useState<UserRole | null>(null)

  useEffect(() => {
    if (isLoaded && user) {
      setRole(getUserRole(user))
    }
  }, [isLoaded, user])

  return {
    role,
    isLoading: !isLoaded
  }
}
