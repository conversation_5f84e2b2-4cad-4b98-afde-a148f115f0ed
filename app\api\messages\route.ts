import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { MessageService } from '@/lib/services/messageService';
import { UserService } from '@/lib/services/userService';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get messages for user
    const messages = await MessageService.getUserMessages(user.id);

    return NextResponse.json({ messages });
  } catch (error) {
    console.error('API: Error fetching messages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const data = await request.json();

    if (!data.receiverId || !data.content) {
      return NextResponse.json({ 
        error: 'Receiver ID and content are required' 
      }, { status: 400 });
    }

    // Send new message
    const message = await MessageService.sendMessage({
      senderId: user.id,
      ...data,
    });

    return NextResponse.json({ message }, { status: 201 });
  } catch (error) {
    console.error('API: Error sending message:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
