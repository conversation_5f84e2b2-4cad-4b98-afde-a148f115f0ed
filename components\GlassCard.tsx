import { ReactNode } from 'react';
import { twMerge } from 'tailwind-merge';

interface GlassCardProps {
  children: ReactNode;
  className?: string;
  blur?: 'sm' | 'md' | 'lg' | 'xl';
  opacity?: 'low' | 'medium' | 'high';
  border?: boolean;
  borderColor?: string;
  shadow?: boolean;
  hoverEffect?: boolean;
}

const GlassCard = ({
  children,
  className = '',
  blur = 'md',
  opacity = 'medium',
  border = true,
  borderColor = 'border-white/20',
  shadow = true,
  hoverEffect = false,
}: GlassCardProps) => {
  // Map blur values to Tailwind classes
  const blurMap = {
    sm: 'backdrop-blur-sm',
    md: 'backdrop-blur-md',
    lg: 'backdrop-blur-lg',
    xl: 'backdrop-blur-xl',
  };
  
  // Map opacity values to Tailwind classes
  const opacityMap = {
    low: 'bg-white/30',
    medium: 'bg-white/60',
    high: 'bg-white/80',
  };
  
  // Build the class string
  const glassClasses = twMerge(`
    ${opacityMap[opacity]}
    ${blurMap[blur]}
    ${border ? `border ${borderColor}` : ''}
    ${shadow ? 'shadow-lg' : ''}
    ${hoverEffect ? 'hover:bg-white/90 transition-all duration-300' : ''}
    rounded-xl
    ${className}
  `);
  
  return (
    <div className={glassClasses}>
      {children}
    </div>
  );
};

export default GlassCard; 