"use client";

import { useState } from "react";
import { Shield, Check, AlertTriangle, FileText, Calendar, Trash2 } from "lucide-react";

// Import our reusable components
import SettingsHeader from "@/app/components/admin/SettingsHeader";
import SettingsSection from "@/app/components/admin/SettingsSection";
import FormField from "@/app/components/admin/FormField";
import SaveButton from "@/app/components/admin/SaveButton";
import SuccessMessage from "@/app/components/admin/SuccessMessage";
import TabNav from "@/app/components/admin/TabNav";

export default function ComplianceSettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("privacy");
  
  // Save settings
  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  // Define tabs for the tab navigation
  const tabs = [
    { id: "privacy", label: "Privacy" },
    { id: "cookies", label: "Cookies & Tracking" },
    { id: "gdpr", label: "GDPR Compliance" },
    { id: "data-retention", label: "Data Retention" },
    { id: "legal", label: "Legal Documents" }
  ];

  return (
    <div>
      {/* Header */}
      <SettingsHeader 
        title="Compliance & Privacy" 
        description="Configure data protection, privacy controls, and legal compliance settings"
        actions={
          <SaveButton isLoading={isSaving} onClick={saveSettings} />
        }
      />
      
      {/* Success Message */}
      {showSuccess && (
        <SuccessMessage message="Compliance settings have been successfully saved." />
      )}
      
      {/* Tabs */}
      <TabNav tabs={tabs} activeTab={activeTab} onChange={setActiveTab} />
      
      {/* Privacy Tab */}
      {activeTab === "privacy" && (
        <>
          <SettingsSection 
            title="Privacy Controls" 
            description="Configure how user data is collected, processed, and managed"
          >
            <FormField 
              label="User Data Collection" 
              helper="Select what user data is collected by default"
            >
              <div className="space-y-2">
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="collect-profile" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    defaultChecked 
                  />
                  <label htmlFor="collect-profile" className="ml-2 block text-sm text-gray-700">
                    Profile information
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="collect-usage" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    defaultChecked 
                  />
                  <label htmlFor="collect-usage" className="ml-2 block text-sm text-gray-700">
                    Usage analytics
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="collect-location" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="collect-location" className="ml-2 block text-sm text-gray-700">
                    Location data
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="collect-device" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    defaultChecked 
                  />
                  <label htmlFor="collect-device" className="ml-2 block text-sm text-gray-700">
                    Device information
                  </label>
                </div>
              </div>
            </FormField>
            
            <FormField 
              label="Privacy Policy URL" 
              helper="Link to your privacy policy document"
              required
            >
              <input
                type="url"
                className="w-full p-2 border border-gray-300 rounded-md"
                defaultValue="https://visamentor.com/privacy-policy"
              />
            </FormField>
            
            <FormField 
              label="Data Processing Purpose" 
              helper="Define the purpose for processing user data"
            >
              <select className="w-full p-2 border border-gray-300 rounded-md">
                <option value="service">Providing the Service</option>
                <option value="analytics">Analytics & Improvements</option>
                <option value="marketing">Marketing & Personalization</option>
                <option value="legal">Legal Requirements</option>
                <option value="multiple">Multiple Purposes</option>
              </select>
            </FormField>
          </SettingsSection>
          
          <SettingsSection 
            title="User Consent Management" 
            description="Configure how user consent is obtained and managed"
          >
            <FormField 
              label="Consent Requirements" 
              helper="Define what users must consent to"
            >
              <div className="space-y-2">
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="consent-terms" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    defaultChecked 
                  />
                  <label htmlFor="consent-terms" className="ml-2 block text-sm text-gray-700">
                    Terms of Service (required)
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="consent-privacy" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    defaultChecked 
                  />
                  <label htmlFor="consent-privacy" className="ml-2 block text-sm text-gray-700">
                    Privacy Policy (required)
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="consent-marketing" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="consent-marketing" className="ml-2 block text-sm text-gray-700">
                    Marketing Communications (optional)
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="consent-analytics" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="consent-analytics" className="ml-2 block text-sm text-gray-700">
                    Analytics & Tracking (optional)
                  </label>
                </div>
              </div>
            </FormField>
            
            <FormField 
              label="Consent Renewal" 
              helper="When to request renewed consent from users"
            >
              <select className="w-full p-2 border border-gray-300 rounded-md">
                <option value="never">Never (one-time consent)</option>
                <option value="policy-change">When policy changes</option>
                <option value="yearly">Yearly</option>
                <option value="6-months">Every 6 months</option>
              </select>
            </FormField>
          </SettingsSection>
        </>
      )}
      
      {/* GDPR Compliance Tab */}
      {activeTab === "gdpr" && (
        <SettingsSection 
          title="GDPR Compliance Controls" 
          description="Configure settings to comply with the General Data Protection Regulation"
        >
          <FormField 
            label="Data Subject Rights" 
            helper="Enable features to comply with GDPR data subject rights"
          >
            <div className="space-y-2">
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="right-access" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="right-access" className="ml-2 block text-sm text-gray-700">
                  Right to access (data export)
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="right-rectification" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="right-rectification" className="ml-2 block text-sm text-gray-700">
                  Right to rectification (edit data)
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="right-erasure" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="right-erasure" className="ml-2 block text-sm text-gray-700">
                  Right to erasure (delete account)
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="right-restriction" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="right-restriction" className="ml-2 block text-sm text-gray-700">
                  Right to restrict processing
                </label>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="right-portability" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="right-portability" className="ml-2 block text-sm text-gray-700">
                  Right to data portability
                </label>
              </div>
            </div>
          </FormField>
          
          <FormField 
            label="Data Protection Officer" 
            helper="Contact information for your DPO"
          >
            <div className="space-y-3">
              <input 
                type="text" 
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="DPO Name"
                defaultValue="Jane Smith"
              />
              <input 
                type="email" 
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="DPO Email"
                defaultValue="<EMAIL>"
              />
            </div>
          </FormField>
          
          <FormField 
            label="Legal Basis for Processing" 
            helper="Select the legal basis for processing user data under GDPR"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="consent">Consent</option>
              <option value="contract">Contract Performance</option>
              <option value="legal">Legal Obligation</option>
              <option value="vital">Vital Interest</option>
              <option value="public">Public Interest</option>
              <option value="legitimate">Legitimate Interest</option>
            </select>
          </FormField>
        </SettingsSection>
      )}
      
      {/* Data Retention Tab */}
      {activeTab === "data-retention" && (
        <SettingsSection 
          title="Data Retention Policies" 
          description="Configure how long different types of data are kept before being deleted"
        >
          <div className="mb-6 bg-amber-50 border border-amber-200 text-amber-800 rounded-lg p-4 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <span>Changes to data retention policies may have legal implications. Consult your legal team before making changes.</span>
          </div>
          
          <FormField 
            label="User Account Data" 
            helper="How long to keep user account data after account deletion"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="immediate">Delete Immediately</option>
              <option value="30-days">30 Days</option>
              <option value="90-days">90 Days</option>
              <option value="1-year">1 Year</option>
              <option value="7-years">7 Years (regulatory)</option>
            </select>
          </FormField>
          
          <FormField 
            label="Application Logs" 
            helper="How long to retain system and application logs"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="30-days">30 Days</option>
              <option value="90-days">90 Days</option>
              <option value="1-year">1 Year</option>
              <option value="3-years">3 Years</option>
            </select>
          </FormField>
          
          <FormField 
            label="Transaction Data" 
            helper="How long to retain transaction and payment records"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="1-year">1 Year</option>
              <option value="3-years">3 Years</option>
              <option value="5-years">5 Years</option>
              <option value="7-years">7 Years (regulatory)</option>
            </select>
          </FormField>
          
          <FormField 
            label="Automated Data Cleanup" 
            helper="Schedule automatic deletion of expired data"
          >
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="auto-cleanup" 
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                defaultChecked 
              />
              <label htmlFor="auto-cleanup" className="ml-2 block text-sm text-gray-700">
                Enable automated data cleanup based on retention policies
              </label>
            </div>
          </FormField>
        </SettingsSection>
      )}
      
      {/* Legal Documents Tab */}
      {activeTab === "legal" && (
        <SettingsSection 
          title="Legal Documents" 
          description="Manage your application's legal documents and policies"
        >
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Document
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Updated
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <FileText className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">Terms of Service</div>
                        <div className="text-xs text-gray-500">All users must accept</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    June 15, 2023
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-indigo-600 hover:text-indigo-900 mr-3">
                      Edit
                    </button>
                    <button className="text-indigo-600 hover:text-indigo-900">
                      View
                    </button>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <FileText className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">Privacy Policy</div>
                        <div className="text-xs text-gray-500">All users must accept</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    May 24, 2023
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-indigo-600 hover:text-indigo-900 mr-3">
                      Edit
                    </button>
                    <button className="text-indigo-600 hover:text-indigo-900">
                      View
                    </button>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <FileText className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">Cookie Policy</div>
                        <div className="text-xs text-gray-500">Informational</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    May 24, 2023
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      Active
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-indigo-600 hover:text-indigo-900 mr-3">
                      Edit
                    </button>
                    <button className="text-indigo-600 hover:text-indigo-900">
                      View
                    </button>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
                        <FileText className="h-4 w-4 text-yellow-600" />
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">GDPR Data Processing Addendum</div>
                        <div className="text-xs text-gray-500">Draft</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    April 10, 2023
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      Draft
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-indigo-600 hover:text-indigo-900 mr-3">
                      Edit
                    </button>
                    <button className="text-indigo-600 hover:text-indigo-900">
                      View
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div className="mt-4">
            <button className="bg-indigo-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-indigo-700">
              <FileText className="h-4 w-4" />
              Add New Document
            </button>
          </div>
        </SettingsSection>
      )}
    </div>
  );
} 