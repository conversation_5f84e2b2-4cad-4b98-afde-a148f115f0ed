# Script to kill all Node processes and restart the Next.js application

# Kill all Node processes (if any)
Write-Host "Attempting to kill all Node.js processes..." -ForegroundColor Yellow
$nodeProcesses = Get-Process | Where-Object { $_.ProcessName -like "*node*" }

if ($nodeProcesses) {
    Write-Host "Found Node.js processes. Killing them..." -ForegroundColor Yellow
    $nodeProcesses | ForEach-Object { 
        try {
            Stop-Process -Id $_.Id -Force
            Write-Host "Killed process with ID: $($_.Id)" -ForegroundColor Green
        } catch {
            Write-Host "Failed to kill process with ID: $($_.Id)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "No Node.js processes found." -ForegroundColor Green
}

# Give it a moment to fully terminate
Start-Sleep -Seconds 2

# Start the Next.js application
Write-Host "Starting the Next.js application..." -ForegroundColor Yellow
try {
    # Change directory to the project folder if needed
    # cd "C:\path\to\your\project"
    
    # Start the application
    npm run dev
} catch {
    Write-Host "Failed to start the application: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Application started successfully!" -ForegroundColor Green 