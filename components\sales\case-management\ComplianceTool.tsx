import React, { useState } from 'react';
import { 
  AlertTriangle, 
  Bell, 
  CheckCircle2, 
  Clock, 
  ExternalLink, 
  FileWarning, 
  Search,
  Shield,
  RefreshCw,
  AlarmCheck,
  Link as LinkIcon,
  UserX,
  AlertCircle
} from 'lucide-react';

export default function ComplianceTool() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCountry, setSelectedCountry] = useState("all");
  const [selectedVisaType, setSelectedVisaType] = useState("all");
  
  // Filter alerts based on search and filters
  const filteredAlerts = policyAlerts.filter(alert => {
    const matchesSearch = alert.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCountry = selectedCountry === "all" || alert.country === selectedCountry;
    const matchesVisaType = selectedVisaType === "all" || alert.visaTypes.includes(selectedVisaType);
    
    return matchesSearch && matchesCountry && matchesVisaType;
  });
  
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Policy Change Alerts</h3>
        <p className="text-sm text-gray-600 mb-6">
          Real-time updates on visa and immigration policy changes that may affect your cases
        </p>
        
        {/* Search and filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md"
              placeholder="Search policy changes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <select
            className="px-3 py-2 border border-gray-300 rounded-md"
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
          >
            <option value="all">All Countries</option>
            <option value="United States">United States</option>
            <option value="United Kingdom">United Kingdom</option>
            <option value="Canada">Canada</option>
            <option value="Australia">Australia</option>
            <option value="European Union">European Union</option>
          </select>
          
          <select
            className="px-3 py-2 border border-gray-300 rounded-md"
            value={selectedVisaType}
            onChange={(e) => setSelectedVisaType(e.target.value)}
          >
            <option value="all">All Visa Types</option>
            <option value="EB-1">EB-1</option>
            <option value="EB-2">EB-2</option>
            <option value="H1-B">H1-B</option>
            <option value="O-1">O-1</option>
            <option value="Student">Student Visa</option>
            <option value="Tourist">Tourist Visa</option>
          </select>
        </div>
        
        {/* Policy alerts */}
        <div className="space-y-4">
          {filteredAlerts.length > 0 ? (
            filteredAlerts.map((alert, index) => (
              <div key={index} className={`border rounded-lg p-4 ${
                alert.severity === "high" ? "border-red-300 bg-red-50" : 
                alert.severity === "medium" ? "border-amber-300 bg-amber-50" : 
                "border-blue-300 bg-blue-50"
              }`}>
                <div className="flex items-start">
                  <div className={`p-2 rounded-full mr-3 ${
                    alert.severity === "high" ? "bg-red-200 text-red-600" : 
                    alert.severity === "medium" ? "bg-amber-200 text-amber-600" : 
                    "bg-blue-200 text-blue-600"
                  }`}>
                    {alert.severity === "high" ? (
                      <AlertTriangle className="h-5 w-5" />
                    ) : alert.severity === "medium" ? (
                      <Clock className="h-5 w-5" />
                    ) : (
                      <Bell className="h-5 w-5" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <h4 className="font-medium">{alert.title}</h4>
                      <span className="text-xs text-gray-500">{alert.date}</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{alert.description}</p>
                    <div className="mt-3 flex flex-wrap gap-2">
                      {alert.visaTypes.map((type, i) => (
                        <span key={i} className="px-2 py-1 bg-white rounded text-xs font-medium text-gray-600 border border-gray-200">
                          {type}
                        </span>
                      ))}
                      <span className="px-2 py-1 bg-white rounded text-xs font-medium text-gray-600 border border-gray-200">
                        {alert.country}
                      </span>
                    </div>
                    <div className="mt-3 flex justify-between">
                      <button className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center">
                        View Full Details 
                        <ExternalLink className="h-4 w-4 ml-1" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-800 text-sm font-medium">
                        Mark as Read
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-10 text-gray-500">
              <SearchIcon className="h-12 w-12 mx-auto text-gray-400 mb-3" />
              <p>No policy changes found matching your criteria</p>
              <p className="text-sm">Try changing your search or filters</p>
            </div>
          )}
        </div>
        
        {filteredAlerts.length > 0 && (
          <div className="mt-4 text-center">
            <button className="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
              View All Policy Updates
            </button>
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Conflict Checker */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Application Conflict Checker</h3>
            <button className="p-2 rounded-full bg-indigo-100 text-indigo-600">
              <RefreshCw className="h-5 w-5" />
            </button>
          </div>
          
          <p className="text-sm text-gray-600 mb-6">
            Identify potential conflicts in visa applications based on previous application history
          </p>
          
          <div className="border border-gray-200 rounded-lg divide-y divide-gray-200">
            {conflicts.map((conflict, index) => (
              <div key={index} className="p-4 flex items-start">
                <div className={`p-2 rounded-full mr-3 ${
                  conflict.severity === "critical" ? "bg-red-100 text-red-600" : 
                  conflict.severity === "warning" ? "bg-amber-100 text-amber-600" : 
                  "bg-blue-100 text-blue-600"
                }`}>
                  {conflict.severity === "critical" ? (
                    <AlertCircle className="h-5 w-5" />
                  ) : conflict.severity === "warning" ? (
                    <FileWarning className="h-5 w-5" />
                  ) : (
                    <LinkIcon className="h-5 w-5" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium text-sm">{conflict.title}</h4>
                  <p className="text-xs text-gray-600 mt-1">{conflict.description}</p>
                  <div className="mt-2 flex items-center">
                    <span className="text-xs text-gray-500">{conflict.clientName}</span>
                    <span className="mx-2 text-gray-300">•</span>
                    <span className="text-xs text-gray-500">{conflict.caseId}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4">
            <button className="w-full py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
              Run New Conflict Check
            </button>
          </div>
        </div>
        
        {/* Biometric Validation */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">Biometric Data Validation</h3>
          <p className="text-sm text-gray-600 mb-6">
            Ensure biometric data compliance with international visa requirements
          </p>
          
          <div className="space-y-4">
            {biometricChecks.map((check, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex items-start">
                    <div className={`p-2 rounded-full mr-3 ${
                      check.status === "valid" ? "bg-green-100 text-green-600" : 
                      check.status === "invalid" ? "bg-red-100 text-red-600" : 
                      "bg-gray-100 text-gray-600"
                    }`}>
                      {check.status === "valid" ? (
                        <CheckCircle2 className="h-5 w-5" />
                      ) : check.status === "invalid" ? (
                        <AlertTriangle className="h-5 w-5" />
                      ) : (
                        <Clock className="h-5 w-5" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{check.type}</h4>
                      <p className="text-xs text-gray-600 mt-1">{check.description}</p>
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    check.status === "valid" ? "bg-green-100 text-green-800" : 
                    check.status === "invalid" ? "bg-red-100 text-red-800" : 
                    "bg-gray-100 text-gray-800"
                  }`}>
                    {check.status.charAt(0).toUpperCase() + check.status.slice(1)}
                  </div>
                </div>
                
                {check.status === "invalid" && (
                  <div className="mt-3 border-t border-gray-100 pt-3">
                    <div className="text-xs text-red-600">
                      <div className="font-medium">Issues Found:</div>
                      <ul className="list-disc pl-4 mt-1 space-y-1">
                        {check.issues.map((issue, i) => (
                          <li key={i}>{issue}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
                
                <div className="mt-3 flex justify-end">
                  <button className="text-indigo-600 hover:text-indigo-800 text-xs font-medium">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Compliance Dashboard */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Compliance Dashboard</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-600">Overall Compliance</p>
                <p className="text-2xl font-bold text-green-600 mt-1">98%</p>
              </div>
              <div className="p-2 rounded-full bg-green-100 text-green-600">
                <CheckCircle2 className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Last updated: Today at 09:45 AM
            </div>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-600">Policy Alerts</p>
                <p className="text-2xl font-bold text-amber-600 mt-1">7</p>
              </div>
              <div className="p-2 rounded-full bg-amber-100 text-amber-600">
                <Bell className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              3 require immediate attention
            </div>
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-600">Biometric Validations</p>
                <p className="text-2xl font-bold text-indigo-600 mt-1">42/45</p>
              </div>
              <div className="p-2 rounded-full bg-indigo-100 text-indigo-600">
                <AlarmCheck className="h-6 w-6" />
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              3 pending validation
            </div>
          </div>
        </div>
        
        <div className="flex justify-end">
          <button className="px-4 py-2 bg-indigo-600 text-white rounded-md font-medium">
            Run Compliance Audit
          </button>
        </div>
      </div>
    </div>
  );
}

// Mock data
const policyAlerts = [
  {
    title: "USCIS Announces H1-B Registration Changes",
    description: "The U.S. Citizenship and Immigration Services has announced changes to the H1-B registration process for the fiscal year 2024, affecting application timelines and requirements.",
    date: "May 15, 2023",
    severity: "high",
    country: "United States",
    visaTypes: ["H1-B"],
    url: "#"
  },
  {
    title: "UK Updated Documentation Requirements for Student Visas",
    description: "The UKVI has updated financial documentation requirements for Tier 4 student visa applications, including new bank statement format requirements.",
    date: "May 12, 2023",
    severity: "medium",
    country: "United Kingdom",
    visaTypes: ["Student"],
    url: "#"
  },
  {
    title: "Canada Express Entry Draw Increased CRS Score Threshold",
    description: "The latest Express Entry draw has increased the Comprehensive Ranking System (CRS) score threshold to 520, affecting ongoing applications.",
    date: "May 10, 2023",
    severity: "medium",
    country: "Canada",
    visaTypes: ["Express Entry", "Skilled Worker"],
    url: "#"
  },
  {
    title: "Australia Reopens Priority Migration Skilled Occupation List",
    description: "The Australian Department of Home Affairs has updated the Priority Migration Skilled Occupation List (PMSOL), adding 9 new occupations.",
    date: "May 7, 2023",
    severity: "low",
    country: "Australia",
    visaTypes: ["Skilled Migration", "Employer Sponsored"],
    url: "#"
  },
  {
    title: "European Union Implements New Schengen Visa Application Process",
    description: "The EU has announced a new digital application process for Schengen visas, to be rolled out by the end of 2023.",
    date: "May 5, 2023",
    severity: "low",
    country: "European Union",
    visaTypes: ["Tourist", "Business"],
    url: "#"
  }
];

const conflicts = [
  {
    title: "Concurrent Application Conflict",
    description: "Applicant has a pending B1/B2 visa application while applying for F1 student visa",
    severity: "critical",
    clientName: "Sneha Reddy",
    caseId: "CS-2023-1235"
  },
  {
    title: "Previous Visa Denial History",
    description: "Undisclosed previous H1-B visa denial found in USCIS records from 2020",
    severity: "warning",
    clientName: "Rajat Gupta",
    caseId: "CS-2023-1236"
  },
  {
    title: "Potential Employment History Gap",
    description: "Discrepancy between stated work history and employment records",
    severity: "warning",
    clientName: "Aditya Mehta",
    caseId: "CS-2023-1234"
  },
  {
    title: "Related Applications",
    description: "Spouse has a pending dependent visa application that should be coordinated",
    severity: "info",
    clientName: "Meera Patel",
    caseId: "CS-2023-1237"
  }
];

const biometricChecks = [
  {
    type: "Passport Photo Validation",
    description: "ICAO compliance check for biometric passport photo",
    status: "valid",
    client: "Aditya Mehta",
    lastChecked: "May 18, 2023",
    issues: []
  },
  {
    type: "Fingerprint Quality Check",
    description: "FBI/IDENT compliance for fingerprint submission",
    status: "invalid",
    client: "Rajat Gupta",
    lastChecked: "May 16, 2023",
    issues: [
      "Low ridge clarity on right index finger",
      "Insufficient contrast on left thumb print"
    ]
  },
  {
    type: "Facial Recognition Compatibility",
    description: "Embassy facial recognition system compatibility",
    status: "valid",
    client: "Sneha Reddy",
    lastChecked: "May 17, 2023",
    issues: []
  },
  {
    type: "Digital Signature Validation",
    description: "ePassport digital signature verification",
    status: "pending",
    client: "Meera Patel",
    lastChecked: null,
    issues: []
  }
];

// Simple icon for empty search
function SearchIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
    </svg>
  );
} 