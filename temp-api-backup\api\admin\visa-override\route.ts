import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

interface RequestBody {
  caseId: string;
  newStatus: string;
  reason?: string;
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return new NextResponse(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }
    
    // Check admin role (mock implementation)
    const isAdmin = true; // In production, check user's role from session
    
    if (!isAdmin) {
      return new NextResponse(JSON.stringify({ error: "Forbidden: Admin access required" }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      });
    }
    
    // Parse request body
    const body: RequestBody = await req.json();
    const { caseId, newStatus, reason } = body;
    
    if (!caseId || !newStatus) {
      return new NextResponse(JSON.stringify({ error: "Missing required fields" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
    
    // Get client IP and user agent for audit trail
    const ip = req.headers.get("x-forwarded-for") || "unknown";
    const userAgent = req.headers.get("user-agent") || "unknown";
    
    // Mock implementation - in production, use Prisma transaction
    try {
      // In production, use a database transaction
      // const result = await prisma.$transaction([
      //   prisma.visaApplication.update({
      //     where: { id: caseId },
      //     data: { status: newStatus }
      //   }),
      //   prisma.adminAction.create({
      //     data: {
      //       adminId: session.user.id,
      //       userId: "app.userId", // Need to get this from the application
      //       action: "STATUS_OVERRIDE",
      //       metadata: {
      //         ip,
      //         userAgent,
      //         from: "app.status", // Old status
      //         to: newStatus,
      //         reason,
      //         timestamp: new Date()
      //       }
      //     }
      //   })
      // ]);
      
      // Return mock response
      return new NextResponse(JSON.stringify({
        success: true,
        message: "Visa application status updated",
        data: {
          id: caseId,
          newStatus,
          auditRecord: {
            actionId: "mock-action-id",
            adminId: session.user.email,
            timestamp: new Date().toISOString(),
          }
        }
      }), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (dbError: any) {
      console.error("Database error:", dbError);
      
      return new NextResponse(JSON.stringify({ error: "Database operation failed", details: dbError.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  } catch (error: any) {
    console.error("Server error:", error);
    
    return new NextResponse(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
} 