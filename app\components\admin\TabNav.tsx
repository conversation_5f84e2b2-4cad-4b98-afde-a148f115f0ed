"use client";

import React from "react";

interface TabItem {
  id: string;
  label: string;
}

interface TabNavProps {
  tabs: TabItem[];
  activeTab: string;
  onChange: (tabId: string) => void;
}

const TabNav = ({ tabs, activeTab, onChange }: TabNavProps) => (
  <div className="flex border-b border-gray-200 mb-6">
    {tabs.map((tab) => (
      <button
        key={tab.id}
        className={`py-2 px-4 font-medium text-sm ${
          activeTab === tab.id 
            ? "text-indigo-600 border-b-2 border-indigo-600" 
            : "text-gray-500 hover:text-gray-700"
        }`}
        onClick={() => onChange(tab.id)}
      >
        {tab.label}
      </button>
    ))}
  </div>
);

export default TabNav; 