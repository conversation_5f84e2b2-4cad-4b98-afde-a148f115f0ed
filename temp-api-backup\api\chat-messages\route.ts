import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { initSocketServer, NextApiResponseWithSocket } from "@/lib/socket";

// Keep mock data for fallback
interface MockMessage {
  id: string;
  userId: string;
  sender: string;
  content: string;
  createdAt: Date;
  read: boolean;
  attachments: string | null;
  senderId?: string;
  receiverId?: string;
}

// Initial mock messages
let mockMessages: MockMessage[] = [
  {
    id: "msg1",
    userId: "mock-user-id",
    sender: "agent",
    content: "Welcome to Visa Mentor! How can I help you today?",
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    read: true,
    attachments: null
  },
  {
    id: "msg2",
    userId: "mock-user-id",
    sender: "user",
    content: "I'm interested in applying for a student visa to the US. What documents do I need?",
    createdAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
    read: true,
    attachments: null
  },
  {
    id: "msg3",
    userId: "mock-user-id",
    sender: "agent",
    content: "For a US student visa, you'll need: acceptance letter from a US school, completed DS-160 form, valid passport, financial evidence, and photographs. Would you like me to explain each requirement in detail?",
    createdAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000), // 6 days ago + 30 minutes
    read: true,
    attachments: null
  },
  {
    id: "msg4",
    userId: "mock-user-id",
    sender: "system",
    content: "Your document 'passport.pdf' has been received and verified.",
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    read: true,
    attachments: null
  }
];

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const userId = searchParams.get("userId") || "mock-user-id";
  
  try {
    // Try to connect to the database
    let messages;
    try {
      // Retrieve messages from the database
      messages = await prisma.message.findMany({
        where: {
          OR: [
            { userId: userId },
            { senderId: userId },
            { receiverId: userId }
          ]
        },
        orderBy: {
          createdAt: 'asc'
        }
      });
    } catch (dbError) {
      console.error("Database error:", dbError);
      // Fallback to mock data if database connection fails
      messages = mockMessages.filter(msg => msg.userId === userId);
    }
    
    // Transform the data to match our frontend expectations
    const transformedMessages = messages.map(msg => ({
      id: msg.id,
      sender: msg.sender,
      text: 'content' in msg ? msg.content : msg.content,
      timestamp: msg.createdAt.toISOString(),
      attachments: msg.attachments ? JSON.parse(msg.attachments.toString()) : [],
      read: msg.read
    }));
    
    return NextResponse.json(transformedMessages);
  } catch (error) {
    console.error("Error fetching messages:", error);
    return NextResponse.json({ error: "Failed to fetch messages" }, { status: 500 });
  }
}

export async function POST(req: NextRequest, res: any) {
  try {
    const { userId = "mock-user-id", sender, text, attachments = [], receiverId = null } = await req.json();
    
    if (!sender || !text) {
      return NextResponse.json({ error: "Missing sender or text" }, { status: 400 });
    }
    
    let newMessage;
    
    try {
      // Create a new message in the database
      newMessage = await prisma.message.create({
        data: {
          userId: userId,
          sender: sender,
          content: text,
          read: sender !== "user", // Messages from agents are immediately read
          attachments: attachments.length ? JSON.stringify(attachments) : null,
          senderId: sender === "user" ? userId : receiverId,
          receiverId: sender !== "user" ? userId : receiverId
        }
      });
    } catch (dbError) {
      console.error("Database error:", dbError);
      // Fallback to mock data if database connection fails
      const newMockMessage: MockMessage = {
        id: `msg-${Date.now()}`,
        userId,
        sender,
        content: text,
        createdAt: new Date(),
        read: sender !== "user",
        attachments: attachments.length ? JSON.stringify(attachments) : null,
        senderId: sender === "user" ? userId : undefined,
        receiverId: sender !== "user" ? userId : undefined
      };
      mockMessages.push(newMockMessage);
      newMessage = newMockMessage;
    }
    
    // Initialize socket if available and emit the new message
    try {
      const io = initSocketServer(req as any, res as NextApiResponseWithSocket);
      const conversationId = `${userId}-${receiverId || 'agent'}`;
      io.to(conversationId).emit('new-message', {
        id: newMessage.id,
        sender: newMessage.sender,
        text: 'content' in newMessage ? newMessage.content : newMessage.content,
        timestamp: newMessage.createdAt.toISOString(),
        attachments: newMessage.attachments ? JSON.parse(newMessage.attachments.toString()) : [],
        read: newMessage.read
      });
    } catch (socketError) {
      console.error("Socket error:", socketError);
      // Continue even if socket fails
    }
    
    return NextResponse.json({
      id: newMessage.id,
      sender: newMessage.sender,
      text: 'content' in newMessage ? newMessage.content : newMessage.content,
      timestamp: newMessage.createdAt.toISOString(),
      attachments: newMessage.attachments ? JSON.parse(newMessage.attachments.toString()) : [],
      read: newMessage.read
    });
  } catch (error) {
    console.error("Error creating message:", error);
    return NextResponse.json({ error: "Failed to create message" }, { status: 500 });
  }
}

export async function PATCH(req: NextRequest, res: any) {
  try {
    const { id, read } = await req.json();
    
    if (!id) {
      return NextResponse.json({ error: "Missing message id" }, { status: 400 });
    }
    
    let updatedMessage;
    
    try {
      // Update the message in the database
      updatedMessage = await prisma.message.update({
        where: { id },
        data: { read }
      });
    } catch (dbError) {
      console.error("Database error:", dbError);
      // Fallback to mock data if database connection fails
      const messageIndex = mockMessages.findIndex(msg => msg.id === id);
      if (messageIndex === -1) {
        return NextResponse.json({ error: "Message not found" }, { status: 404 });
      }
      mockMessages[messageIndex].read = read;
      updatedMessage = mockMessages[messageIndex];
    }
    
    // Initialize socket if available and emit the updated message status
    try {
      const io = initSocketServer(req as any, res as NextApiResponseWithSocket);
      const userId = updatedMessage.userId;
      const receiverId = updatedMessage.receiverId || updatedMessage.senderId;
      const conversationId = `${userId}-${receiverId || 'agent'}`;
      
      io.to(conversationId).emit('message-status-updated', {
        id: updatedMessage.id,
        read: updatedMessage.read
      });
    } catch (socketError) {
      console.error("Socket error:", socketError);
      // Continue even if socket fails
    }
    
    return NextResponse.json({
      id: updatedMessage.id,
      sender: updatedMessage.sender,
      text: 'content' in updatedMessage ? updatedMessage.content : updatedMessage.content,
      timestamp: updatedMessage.createdAt.toISOString(),
      attachments: updatedMessage.attachments ? JSON.parse(updatedMessage.attachments.toString()) : [],
      read: updatedMessage.read
    });
  } catch (error) {
    console.error("Error updating message:", error);
    return NextResponse.json({ error: "Failed to update message" }, { status: 500 });
  }
} 