"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  TrendingUp,
  User,
  Users,
  Filter,
  Search,
  Plus,
  Calendar,
  ArrowUpRight,
  UserPlus,
  DollarSign,
  Activity,
  CheckCircle2,
  Clock,
  Medal,
  Share2,
  ChevronRight,
  Sparkles
} from "lucide-react";

// Mock data for referral metrics
const referralMetrics = {
  totalReferrals: 428,
  activeReferrals: 186,
  conversionRate: 32,
  avgRevenuePerReferral: "₹24,500",
  totalReferralRevenue: "₹3.2M",
  pendingRewards: "₹185,600"
};

// Mock data for referral sources
const referralSources = [
  { name: "Client Referrals", count: 245, conversion: 38, color: "bg-blue-500" },
  { name: "Partner Network", count: 124, conversion: 42, color: "bg-indigo-500" },
  { name: "Employee Referrals", count: 89, conversion: 28, color: "bg-purple-500" },
  { name: "Website", count: 65, conversion: 19, color: "bg-amber-500" },
  { name: "Social Media", count: 35, conversion: 12, color: "bg-green-500" }
];

// Mock data for top referrers
const topReferrers = [
  {
    id: 1,
    name: "Raj Patel",
    type: "Client",
    referrals: 18,
    conversions: 12,
    totalValue: "₹290,000",
    rewardStatus: "Paid"
  },
  {
    id: 2,
    name: "Sunita Sharma",
    type: "Client",
    referrals: 15,
    conversions: 10,
    totalValue: "₹245,000",
    rewardStatus: "Pending"
  },
  {
    id: 3,
    name: "Amar Singh",
    type: "Partner",
    referrals: 22,
    conversions: 15,
    totalValue: "₹365,000",
    rewardStatus: "Partial"
  },
  {
    id: 4,
    name: "Priya Mehta",
    type: "Employee",
    referrals: 14,
    conversions: 9,
    totalValue: "₹220,000",
    rewardStatus: "Paid"
  },
  {
    id: 5,
    name: "Vikram Desai",
    type: "Partner",
    referrals: 19,
    conversions: 13,
    totalValue: "₹310,000",
    rewardStatus: "Pending"
  }
];

// Mock data for recent referrals
const recentReferrals = [
  {
    id: 1,
    referrerName: "Raj Patel",
    referralName: "Ananya Kapoor",
    status: "Converted",
    service: "Student Visa",
    country: "Australia",
    date: "Today, 10:30 AM",
    value: "₹28,500"
  },
  {
    id: 2,
    referrerName: "Meera Shah",
    referralName: "Rahul Verma",
    status: "In Progress",
    service: "Work Visa",
    country: "Canada",
    date: "Yesterday, 3:15 PM",
    value: "₹32,000"
  },
  {
    id: 3,
    referrerName: "Vikram Desai",
    referralName: "Kiran Joshi",
    status: "Initial Contact",
    service: "Business Visa",
    country: "United Kingdom",
    date: "May 10, 2023",
    value: "-"
  },
  {
    id: 4,
    referrerName: "Sunita Sharma",
    referralName: "Deepak Malhotra",
    status: "Converted",
    service: "PR Visa",
    country: "Canada",
    date: "May 8, 2023",
    value: "₹36,000"
  },
  {
    id: 5,
    referrerName: "Amar Singh",
    referralName: "Nisha Reddy",
    status: "Lost",
    service: "Tourist Visa",
    country: "United States",
    date: "May 5, 2023",
    value: "-"
  },
  {
    id: 6,
    referrerName: "Priya Mehta",
    referralName: "Sanjay Kumar",
    status: "In Progress",
    service: "Student Visa",
    country: "New Zealand",
    date: "May 4, 2023",
    value: "₹26,500"
  }
];

// Mock data for referral programs
const referralPrograms = [
  {
    id: 1,
    name: "Client Appreciation Program",
    reward: "₹5,000 per successful conversion",
    participants: 178,
    status: "Active",
    conversions: 86,
    totalPaid: "₹430,000"
  },
  {
    id: 2,
    name: "Partner Rewards",
    reward: "10% of first year revenue",
    participants: 35,
    status: "Active",
    conversions: 124,
    totalPaid: "₹1.2M"
  },
  {
    id: 3,
    name: "Employee Referral Bonus",
    reward: "₹3,000 per successful conversion",
    participants: 42,
    status: "Active",
    conversions: 68,
    totalPaid: "₹204,000"
  },
  {
    id: 4,
    name: "Early Bird Referrals",
    reward: "₹7,500 per successful conversion",
    participants: 23,
    status: "Ended",
    conversions: 45,
    totalPaid: "₹337,500"
  }
];

export default function ReferralsPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedReferrer, setSelectedReferrer] = useState<any>(null);

  // Helper function to get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Converted":
        return "bg-green-100 text-green-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Initial Contact":
        return "bg-amber-100 text-amber-800";
      case "Lost":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Helper function to get reward status badge color
  const getRewardStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Paid":
        return "bg-green-100 text-green-800";
      case "Pending":
        return "bg-amber-100 text-amber-800";
      case "Partial":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Helper function to get program status badge color
  const getProgramStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Ended":
        return "bg-gray-100 text-gray-800";
      case "Paused":
        return "bg-amber-100 text-amber-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Referral Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Referrals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{referralMetrics.totalReferrals}</div>
            <p className="text-xs text-muted-foreground">
              {referralMetrics.activeReferrals} active
            </p>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{referralMetrics.conversionRate}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-green-500 h-2 rounded-full" 
                style={{ width: `${referralMetrics.conversionRate}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Avg. Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{referralMetrics.avgRevenuePerReferral}</div>
            <p className="text-xs text-muted-foreground">
              Per successful referral
            </p>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{referralMetrics.totalReferralRevenue}</div>
            <p className="text-xs text-muted-foreground">
              From referrals
            </p>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending Rewards</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold">{referralMetrics.pendingRewards}</div>
              <Badge className="bg-amber-100 text-amber-800">
                Awaiting Payment
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all referral programs
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Referral Sources and Top Referrers */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Referral Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {referralSources.map((source, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{source.name}</span>
                      <span className="text-sm text-gray-500">{source.count} referrals</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`${source.color} h-2 rounded-full`} 
                        style={{ width: `${(source.count / referralMetrics.totalReferrals) * 100}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-right">
                      {source.conversion}% conversion rate
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6">
                <h3 className="font-medium mb-3">Top Referrers</h3>
                <div className="space-y-3">
                  {topReferrers.slice(0, 3).map((referrer) => (
                    <div 
                      key={referrer.id} 
                      className="p-3 border rounded-md cursor-pointer hover:bg-gray-50"
                      onClick={() => setSelectedReferrer(referrer)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{referrer.name}</div>
                          <div className="text-xs text-gray-500">{referrer.type}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{referrer.conversions} <span className="text-gray-500 text-xs">/ {referrer.referrals}</span></div>
                          <div className="text-xs text-gray-500">Conversions</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <Button variant="ghost" size="sm" className="mt-3 w-full">
                  View All Referrers
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Referral Management Tools */}
        <div className="lg:col-span-2">
          <Tabs 
            defaultValue="overview" 
            className="w-full"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <div className="flex justify-between items-center mb-4">
              <TabsList>
                <TabsTrigger value="overview">Recent Referrals</TabsTrigger>
                <TabsTrigger value="programs">Programs</TabsTrigger>
                <TabsTrigger value="rewards">Rewards</TabsTrigger>
              </TabsList>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Referral
              </Button>
            </div>

            <TabsContent value="overview" className="m-0">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg">Recent Referrals</CardTitle>
                  <div className="w-full max-w-sm flex items-center space-x-2 pb-0 pt-2">
                    <Input 
                      placeholder="Search referrals..." 
                      className="h-8 text-sm"
                    />
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {recentReferrals.map((referral) => (
                      <div key={referral.id} className="p-4 hover:bg-gray-50">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center gap-1">
                              <h4 className="font-medium">{referral.referralName}</h4>
                              <Badge className={getStatusBadgeColor(referral.status)}>
                                {referral.status}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{referral.service} • {referral.country}</p>
                            <div className="flex items-center mt-2 text-xs text-gray-500">
                              <User className="h-3 w-3 mr-1" />
                              <span>Referred by {referral.referrerName}</span>
                              <span className="mx-2">•</span>
                              <Calendar className="h-3 w-3 mr-1" />
                              <span>{referral.date}</span>
                            </div>
                          </div>
                          <div className="text-right">
                            {referral.value !== "-" && (
                              <div className="text-sm font-medium text-green-600">{referral.value}</div>
                            )}
                            <Button variant="ghost" size="sm" className="mt-1">
                              Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="programs" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Referral Programs</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {referralPrograms.map((program) => (
                      <div key={program.id} className="border rounded-md overflow-hidden">
                        <div className="p-4 flex justify-between items-start">
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{program.name}</h3>
                              <Badge className={getProgramStatusBadgeColor(program.status)}>
                                {program.status}
                              </Badge>
                            </div>
                            <p className="text-sm mt-1">{program.reward}</p>
                          </div>
                          <Button variant="outline" size="sm">
                            Manage
                          </Button>
                        </div>
                        <div className="bg-gray-50 p-3 grid grid-cols-3 gap-4 text-sm border-t">
                          <div>
                            <div className="text-gray-500 text-xs">Participants</div>
                            <div className="font-medium">{program.participants}</div>
                          </div>
                          <div>
                            <div className="text-gray-500 text-xs">Conversions</div>
                            <div className="font-medium">{program.conversions}</div>
                          </div>
                          <div>
                            <div className="text-gray-500 text-xs">Total Paid</div>
                            <div className="font-medium">{program.totalPaid}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 flex justify-center">
                    <Button className="mt-2">
                      <Plus className="h-4 w-4 mr-2" />
                      Create New Program
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="rewards" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Rewards Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {topReferrers.map((referrer) => (
                      <div key={referrer.id} className="p-4 border rounded-md hover:bg-gray-50">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{referrer.name}</h3>
                              <span className="text-xs text-gray-500">({referrer.type})</span>
                            </div>
                            <div className="mt-1 flex items-center text-sm">
                              <span className="text-gray-600 mr-2">Conversions: {referrer.conversions}/{referrer.referrals}</span>
                              <span className="text-gray-600">Value: {referrer.totalValue}</span>
                            </div>
                          </div>
                          <div className="flex flex-col items-end">
                            <Badge className={getRewardStatusBadgeColor(referrer.rewardStatus)}>
                              {referrer.rewardStatus}
                            </Badge>
                            {referrer.rewardStatus === "Pending" && (
                              <Button variant="outline" size="sm" className="mt-2">
                                <DollarSign className="h-3 w-3 mr-1" />
                                Process Reward
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    <div className="mt-4 flex justify-end">
                      <Button variant="outline">
                        View All Rewards
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Referral Tools */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Referral Management Tools</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <UserPlus className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Add Referral</span>
              <span className="text-xs text-gray-500 mt-1">Record new referrals</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <Sparkles className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Create Program</span>
              <span className="text-xs text-gray-500 mt-1">Set up new incentives</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <Medal className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Manage Rewards</span>
              <span className="text-xs text-gray-500 mt-1">Process pending rewards</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <Share2 className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Referral Portal</span>
              <span className="text-xs text-gray-500 mt-1">Client access dashboard</span>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 