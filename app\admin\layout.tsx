"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Users,
  FileText,
  CheckSquare,
  BarChart2,
  MessageCircle,
  Shield,
  Settings,
  Menu,
  X,
  LogOut,
  Home,
  Bell,
  Mail
} from "lucide-react";
import Breadcrumb from "@/components/navigation/Breadcrumb";

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
}

const NavItem = ({ href, icon, label, active }: NavItemProps) => (
  <Link href={href as any}>
    <div
      className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
        active
          ? "bg-gradient-to-r from-indigo-600/20 to-purple-700/20 text-indigo-800 font-medium"
          : "text-gray-600 hover:bg-indigo-50/70 hover:text-indigo-700"
      }`}
    >
      <div className={`text-lg ${active ? 'text-indigo-600' : 'text-indigo-400'}`}>{icon}</div>
      <span>{label}</span>
    </div>
  </Link>
);

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
      if (window.innerWidth < 1024) {
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const navItems = [
    { href: "/admin", icon: <Home className="h-5 w-5" />, label: "Dashboard" },
    { href: "/admin/users", icon: <Users className="h-5 w-5" />, label: "User Management" },
    { href: "/admin/applications", icon: <FileText className="h-5 w-5" />, label: "Visa Applications" },
    { href: "/admin/documents", icon: <CheckSquare className="h-5 w-5" />, label: "Document Verification" },
    { href: "/admin/communications", icon: <Mail className="h-5 w-5" />, label: "Communications" },
    { href: "/admin/analytics", icon: <BarChart2 className="h-5 w-5" />, label: "Analytics" },
    { href: "/admin/compliance", icon: <Shield className="h-5 w-5" />, label: "Compliance" },
    { href: "/admin/settings", icon: <Settings className="h-5 w-5" />, label: "Settings" },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex">
      {/* Sidebar */}
      <aside
        className={`bg-white/80 backdrop-blur-lg fixed lg:static inset-y-0 left-0 z-50 transform ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        } lg:translate-x-0 transition-transform duration-200 ease-in-out w-64 border-r border-white/20 shadow-lg`}
      >
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between h-16 px-4 border-b border-white/20">
            <Link href={"/admin" as any} className="flex items-center">
              <div className="text-lg font-semibold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-700">
                Visa Mentor Admin
              </div>
            </Link>
            {isMobile && (
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden p-1 rounded-md"
              >
                <X className="h-5 w-5 text-indigo-600" />
              </button>
            )}
          </div>

          <div className="overflow-y-auto py-4 flex flex-col flex-grow">
            <nav className="px-2 space-y-1">
              {navItems.map((item) => (
                <NavItem
                  key={item.href}
                  href={item.href}
                  icon={item.icon}
                  label={item.label}
                  active={pathname === item.href}
                />
              ))}
            </nav>

            <div className="mt-auto p-4 border-t border-white/20">
              <Link href={"/" as any}>
                <div className="flex items-center gap-3 px-4 py-3 rounded-lg transition-colors text-gray-600 hover:bg-red-50/70 hover:text-red-700">
                  <LogOut className="h-5 w-5" />
                  <span>Logout</span>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Top navigation */}
        <header className="bg-white/80 backdrop-blur-lg border-b border-white/20 shadow-sm">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex">
                {isMobile && (
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="lg:hidden p-1 rounded-md -ml-1 text-indigo-600"
                  >
                    <Menu className="h-5 w-5" />
                  </button>
                )}
              </div>
              <div className="flex items-center gap-4">
                <button className="relative p-1 rounded-full hover:bg-indigo-50/70 transition-colors">
                  <Bell className="h-5 w-5 text-indigo-600" />
                  <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-gradient-to-r from-purple-600 to-pink-600"></span>
                </button>
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-600 to-indigo-700 flex items-center justify-center text-white font-medium shadow-md">
                    A
                  </div>
                  <span className="ml-2 text-sm font-medium text-indigo-800">Admin User</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto p-4 sm:p-6 lg:p-8">
          <div className="mb-4">
            <Breadcrumb showHome={false} />
          </div>
          {children}
        </main>
      </div>
    </div>
  );
} 