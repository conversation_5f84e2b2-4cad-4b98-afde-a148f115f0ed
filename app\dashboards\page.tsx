"use client"

import RoleBasedAccess from "@/components/auth/RoleBasedAccess"
import DashboardGrid from "@/components/navigation/DashboardGrid"
import { useUser } from "@clerk/nextjs"

export default function DashboardsPage() {
  const { user } = useUser()

  return (
    <RoleBasedAccess allowedRoles={['admin', 'sales-manager', 'sales-executive', 'crm', 'hr', 'user']}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 mb-4">
              Welcome{user?.firstName ? `, ${user.firstName}` : ''}!
            </h1>
            <p className="text-xl text-gray-600 mb-2">
              Access your personalized dashboards and tools
            </p>
            <p className="text-gray-500">
              Your role determines which dashboards and features you can access
            </p>
          </div>
          
          <DashboardGrid 
            title="Your Available Dashboards"
            subtitle="Click on any dashboard below to get started"
          />
          
          <div className="mt-12 bg-white/80 backdrop-blur-sm rounded-xl p-8 border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Tips</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">🔐 Role-Based Access</h4>
                <p>Your access level is determined by your role. Contact your administrator if you need access to additional dashboards.</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">🧭 Easy Navigation</h4>
                <p>Use the dashboard switcher in the header to quickly move between different dashboards without returning to this page.</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">📱 Mobile Friendly</h4>
                <p>All dashboards are optimized for mobile devices. Access your tools anywhere, anytime.</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">🔄 Real-time Updates</h4>
                <p>Dashboard data updates in real-time to ensure you always have the latest information.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </RoleBasedAccess>
  )
}
