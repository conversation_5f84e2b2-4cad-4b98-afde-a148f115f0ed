"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  Calendar, 
  Users, 
  Settings, 
  Plus, 
  Edit, 
  Trash, 
  Send, 
  Copy, 
  Clock,
  ChevronRight,
  BarChart3,
  Eye,
  Save
} from "lucide-react";

// Import types from our models
import { EmailTemplate, EmailCampaign, EmailCampaignStats } from "@/lib/models/email-campaigns";

// Mock data for templates
const mockTemplates: EmailTemplate[] = [
  {
    id: "1",
    name: "Visa Approval",
    subject: "Congratulations! Your {{visaType}} Visa Has Been Approved",
    trigger: "visa_approved",
    content: `
      Dear {{name}},
      
      We are pleased to inform you that your {{visaType}} visa application has been approved!
      
      Next steps: {{nextSteps}}
      
      Please contact your visa counselor if you have any questions.
      
      Best regards,
      Visa Mentor Team
    `,
    variables: ["name", "visaType", "nextSteps"],
    createdAt: new Date("2023-09-15"),
    updatedAt: new Date("2023-09-15")
  },
  {
    id: "2",
    name: "Document Request",
    subject: "Important: Additional Documents Required for Your Visa Application",
    trigger: "document_request",
    content: `
      Dear {{name}},
      
      We need the following additional documents to proceed with your {{visaType}} visa application:
      
      {{documentList}}
      
      Please submit these documents by {{deadline}}.
      
      If you have any questions, please contact <NAME_EMAIL>.
      
      Best regards,
      Visa Mentor Team
    `,
    variables: ["name", "visaType", "documentList", "deadline"],
    createdAt: new Date("2023-10-01"),
    updatedAt: new Date("2023-10-05")
  },
  {
    id: "3",
    name: "Visa Interview Preparation",
    subject: "Your Visa Interview: What to Expect and How to Prepare",
    trigger: "interview_prep",
    content: `
      Dear {{name}},
      
      Your {{visaType}} visa interview is scheduled for {{interviewDate}} at {{interviewTime}}.
      
      Here are some tips to help you prepare:
      
      {{preparationTips}}
      
      Don't hesitate to reach out if you need any clarification.
      
      Best regards,
      Visa Mentor Team
    `,
    variables: ["name", "visaType", "interviewDate", "interviewTime", "preparationTips"],
    createdAt: new Date("2023-10-10"),
    updatedAt: new Date("2023-10-10")
  },
];

// Mock data for campaigns
const mockCampaigns: EmailCampaign[] = [
  {
    id: "1",
    name: "October Visa Approval Follow-up",
    description: "Follow-up with clients who received visa approvals in October",
    templateId: "1",
    status: "active",
    segmentIds: ["segment1", "segment2"],
    createdAt: new Date("2023-10-01"),
    updatedAt: new Date("2023-10-01"),
    stats: {
      sent: 124,
      delivered: 120,
      opened: 98,
      clicked: 45,
      bounced: 4,
      unsubscribed: 2,
      openRate: 81.67,
      clickRate: 45.92
    }
  },
  {
    id: "2",
    name: "Missing Document Reminder",
    description: "Reminder for clients with incomplete documentation",
    templateId: "2",
    status: "scheduled",
    scheduledAt: new Date("2023-11-15"),
    segmentIds: ["segment3"],
    createdAt: new Date("2023-10-25"),
    updatedAt: new Date("2023-10-25")
  },
  {
    id: "3",
    name: "November Interview Preparation",
    description: "Preparation guide for clients with upcoming interviews",
    templateId: "3",
    status: "draft",
    segmentIds: ["segment4", "segment5"],
    createdAt: new Date("2023-10-30"),
    updatedAt: new Date("2023-10-30")
  },
];

export function EmailCampaignStudio() {
  const [activeTab, setActiveTab] = useState("campaigns");
  const [templates, setTemplates] = useState(mockTemplates);
  const [campaigns, setCampaigns] = useState(mockCampaigns);
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<EmailTemplate | null>(null);
  const [previewVariables, setPreviewVariables] = useState<Record<string, string>>({});
  
  // Handler for creating/editing template
  const handleEditTemplate = (template: EmailTemplate | null) => {
    setEditingTemplate(template || {
      id: Date.now().toString(),
      name: "",
      subject: "",
      trigger: "",
      content: "",
      variables: [],
      createdAt: new Date(),
      updatedAt: new Date()
    } as EmailTemplate);
    setActiveTab("editTemplate");
  };
  
  // Handler for previewing template
  const handlePreviewTemplate = (template: EmailTemplate) => {
    setPreviewTemplate(template);
    
    // Set default values for all variables
    const defaultVars: Record<string, string> = {};
    template.variables.forEach(variable => {
      defaultVars[variable] = `[${variable}]`;
    });
    setPreviewVariables(defaultVars);
    
    setActiveTab("previewTemplate");
  };
  
  // Handler for saving template
  const handleSaveTemplate = (updatedTemplate: EmailTemplate) => {
    if (templates.some(t => t.id === updatedTemplate.id)) {
      setTemplates(templates.map(t => 
        t.id === updatedTemplate.id ? updatedTemplate : t
      ));
    } else {
      setTemplates([...templates, updatedTemplate]);
    }
    setEditingTemplate(null);
    setActiveTab("templates");
  };
  
  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };
  
  // Parse template content with variables
  const parseContent = (content: string, variables: Record<string, string>) => {
    return content.replace(/{{([^{}]+)}}/g, (match, varName) => {
      const trimmedVarName = varName.trim();
      return variables[trimmedVarName] || match;
    });
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Email Campaign Studio</CardTitle>
            <CardDescription>
              Create, manage, and track email campaigns
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs 
          value={activeTab} 
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="mb-4">
            <TabsTrigger value="campaigns">
              <Mail className="mr-2 h-4 w-4" />
              Campaigns
            </TabsTrigger>
            <TabsTrigger value="templates">
              <Settings className="mr-2 h-4 w-4" />
              Templates
            </TabsTrigger>
            <TabsTrigger value="analytics">
              <BarChart3 className="mr-2 h-4 w-4" />
              Analytics
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="campaigns" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Email Campaigns</h3>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                New Campaign
              </Button>
            </div>
            
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Campaign Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Template</TableHead>
                  <TableHead>Schedule</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {campaigns.map((campaign) => {
                  const template = templates.find(t => t.id === campaign.templateId);
                  
                  return (
                    <TableRow key={campaign.id}>
                      <TableCell className="font-medium">
                        {campaign.name}
                        <div className="text-xs text-muted-foreground">{campaign.description}</div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={
                            campaign.status === "active" ? "default" : 
                            campaign.status === "scheduled" ? "outline" :
                            "secondary"
                          }
                        >
                          {campaign.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{template?.name || "Unknown Template"}</TableCell>
                      <TableCell>
                        {campaign.scheduledAt ? (
                          <div className="flex items-center">
                            <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                            {formatDate(campaign.scheduledAt)}
                          </div>
                        ) : campaign.status === "active" ? (
                          "Running"
                        ) : (
                          "Not scheduled"
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TabsContent>
          
          <TabsContent value="templates" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Email Templates</h3>
              <Button size="sm" onClick={() => handleEditTemplate(null)}>
                <Plus className="mr-2 h-4 w-4" />
                New Template
              </Button>
            </div>
            
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Template Name</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Trigger</TableHead>
                  <TableHead>Variables</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">
                      {template.name}
                      <div className="text-xs text-muted-foreground">
                        Updated {formatDate(template.updatedAt)}
                      </div>
                    </TableCell>
                    <TableCell>{template.subject}</TableCell>
                    <TableCell>
                      {template.trigger && (
                        <Badge variant="outline">
                          {template.trigger}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {template.variables.map((variable) => (
                          <Badge 
                            variant="secondary" 
                            key={variable}
                            className="text-xs"
                          >
                            {variable}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handlePreviewTemplate(template)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleEditTemplate(template)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TabsContent>
          
          <TabsContent value="analytics" className="space-y-4">
            <h3 className="text-lg font-medium">Campaign Performance</h3>
            
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Campaign</TableHead>
                  <TableHead className="text-center">Sent</TableHead>
                  <TableHead className="text-center">Delivered</TableHead>
                  <TableHead className="text-center">Opens</TableHead>
                  <TableHead className="text-center">Clicks</TableHead>
                  <TableHead className="text-center">Open Rate</TableHead>
                  <TableHead className="text-center">Click Rate</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {campaigns
                  .filter(campaign => campaign.stats)
                  .map((campaign) => {
                    const stats = campaign.stats as EmailCampaignStats;
                    
                    return (
                      <TableRow key={campaign.id}>
                        <TableCell className="font-medium">
                          {campaign.name}
                        </TableCell>
                        <TableCell className="text-center">{stats.sent}</TableCell>
                        <TableCell className="text-center">{stats.delivered}</TableCell>
                        <TableCell className="text-center">{stats.opened}</TableCell>
                        <TableCell className="text-center">{stats.clicked}</TableCell>
                        <TableCell className="text-center">{stats.openRate.toFixed(1)}%</TableCell>
                        <TableCell className="text-center">{stats.clickRate.toFixed(1)}%</TableCell>
                      </TableRow>
                    );
                })}
              </TableBody>
            </Table>
          </TabsContent>
          
          <TabsContent value="editTemplate">
            {editingTemplate && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">
                    {editingTemplate.id ? "Edit Template" : "Create Template"}
                  </h3>
                  <Button 
                    variant="ghost"
                    onClick={() => setActiveTab("templates")}
                  >
                    Cancel
                  </Button>
                </div>
                
                <div className="grid gap-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="template-name">Template Name</Label>
                      <Input 
                        id="template-name" 
                        value={editingTemplate.name} 
                        onChange={(e) => setEditingTemplate({
                          ...editingTemplate,
                          name: e.target.value
                        })}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="template-trigger">Trigger Event</Label>
                      <Input 
                        id="template-trigger" 
                        value={editingTemplate.trigger} 
                        onChange={(e) => setEditingTemplate({
                          ...editingTemplate,
                          trigger: e.target.value
                        })}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="template-subject">Email Subject</Label>
                    <Input 
                      id="template-subject" 
                      value={editingTemplate.subject} 
                      onChange={(e) => setEditingTemplate({
                        ...editingTemplate,
                        subject: e.target.value
                      })}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="template-content">Email Content</Label>
                    <Textarea 
                      id="template-content" 
                      rows={12}
                      value={editingTemplate.content} 
                      onChange={(e) => setEditingTemplate({
                        ...editingTemplate,
                        content: e.target.value
                      })}
                      className="font-mono text-sm"
                    />
                    <p className="text-xs text-muted-foreground">
                      Use {'{{'}<span>variableName</span>{'}}'} syntax for dynamic content.
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="template-variables">Template Variables</Label>
                    <Input 
                      id="template-variables" 
                      value={editingTemplate.variables.join(", ")} 
                      onChange={(e) => setEditingTemplate({
                        ...editingTemplate,
                        variables: e.target.value.split(",").map(v => v.trim()).filter(Boolean)
                      })}
                    />
                    <p className="text-xs text-muted-foreground">
                      Comma-separated list of variables used in the template.
                    </p>
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <Button 
                      onClick={() => handlePreviewTemplate(editingTemplate)}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Preview
                    </Button>
                    <Button 
                      onClick={() => handleSaveTemplate({
                        ...editingTemplate,
                        updatedAt: new Date()
                      })}
                    >
                      <Save className="mr-2 h-4 w-4" />
                      Save Template
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="previewTemplate">
            {previewTemplate && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Template Preview: {previewTemplate.name}</h3>
                  <Button 
                    variant="ghost"
                    onClick={() => setActiveTab(editingTemplate ? "editTemplate" : "templates")}
                  >
                    Back
                  </Button>
                </div>
                
                <div className="grid grid-cols-[300px_1fr] gap-4">
                  <div className="space-y-4">
                    <h4 className="font-medium">Preview Variables</h4>
                    
                    {previewTemplate.variables.map((variable) => (
                      <div key={variable} className="space-y-2">
                        <Label htmlFor={`var-${variable}`}>{variable}</Label>
                        <Input 
                          id={`var-${variable}`}
                          value={previewVariables[variable] || ""}
                          onChange={(e) => setPreviewVariables({
                            ...previewVariables,
                            [variable]: e.target.value
                          })}
                        />
                      </div>
                    ))}
                  </div>
                  
                  <div className="border rounded-lg p-4 space-y-4">
                    <div className="border-b pb-2">
                      <h3 className="font-medium">
                        {parseContent(previewTemplate.subject, previewVariables)}
                      </h3>
                      <div className="text-sm text-muted-foreground">
                        From: Visa Mentor &lt;<EMAIL>&gt;
                      </div>
                    </div>
                    
                    <div className="whitespace-pre-line">
                      {parseContent(previewTemplate.content, previewVariables)}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 