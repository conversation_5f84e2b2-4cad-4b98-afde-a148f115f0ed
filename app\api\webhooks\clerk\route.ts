import { NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { Webhook } from 'svix'
import { UserService } from '@/lib/services/userService'

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET

export async function POST(req: Request) {
  if (!webhookSecret) {
    console.error('Missing CLERK_WEBHOOK_SECRET environment variable')
    return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 })
  }

  // Get the headers
  const headerPayload = headers()
  const svix_id = headerPayload.get('svix-id')
  const svix_timestamp = headerPayload.get('svix-timestamp')
  const svix_signature = headerPayload.get('svix-signature')

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return NextResponse.json({ error: 'Missing svix headers' }, { status: 400 })
  }

  // Get the body
  const payload = await req.text()
  const body = JSON.parse(payload)

  // Create a new Svix instance with your secret.
  const wh = new Webhook(webhookSecret)

  let evt: any

  // Verify the payload with the headers
  try {
    evt = wh.verify(payload, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    })
  } catch (err) {
    console.error('Error verifying webhook:', err)
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
  }

  // Handle the webhook
  const eventType = evt.type
  const { id, email_addresses, first_name, last_name, image_url } = evt.data

  try {
    switch (eventType) {
      case 'user.created':
        // Create user in database when they sign up
        await UserService.upsertUserFromClerk(id, {
          clerkId: id,
          email: email_addresses[0]?.email_address || '',
          name: `${first_name || ''} ${last_name || ''}`.trim() || null,
          image: image_url || null,
        })
        console.log('✅ User created in database:', email_addresses[0]?.email_address)
        break

      case 'user.updated':
        // Update user in database when they update their profile
        await UserService.upsertUserFromClerk(id, {
          clerkId: id,
          email: email_addresses[0]?.email_address || '',
          name: `${first_name || ''} ${last_name || ''}`.trim() || null,
          image: image_url || null,
        })
        console.log('✅ User updated in database:', email_addresses[0]?.email_address)
        break

      case 'user.deleted':
        // Delete user from database when they delete their account
        await UserService.deleteUser(id)
        console.log('✅ User deleted from database:', id)
        break

      default:
        console.log('Unhandled webhook event type:', eventType)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error handling webhook:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
