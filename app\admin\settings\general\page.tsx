"use client";

import { useState } from "react";
import Link from "next/link";
import { ChevronLeft, Save, Upload, Trash2, AlertCircle } from "lucide-react";

// Form Field Component
interface FormFieldProps {
  label: string;
  children: React.ReactNode;
  helper?: string;
  required?: boolean;
}

const FormField = ({ label, children, helper, required = false }: FormFieldProps) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start py-4 border-b border-gray-100 last:border-0">
    <div>
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {helper && <p className="mt-1 text-xs text-gray-500">{helper}</p>}
    </div>
    <div className="md:col-span-2">
      {children}
    </div>
  </div>
);

export default function GeneralSettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  // General settings state
  const [settings, setSettings] = useState({
    siteName: "Visa Mentor",
    legalName: "Visa Mentor Inc.",
    description: "Your trusted advisor for visa applications",
    supportEmail: "<EMAIL>",
    contactEmail: "<EMAIL>",
    contactPhone: "+****************",
    address: "123 Visa Street, Application City, 10001",
    timezone: "UTC",
    language: "en",
    dateFormat: "MM/DD/YYYY",
    currencyCode: "USD"
  });
  
  // Logo upload state
  const [logo, setLogo] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>("/logo-placeholder.png");

  // Handle changes to form fields
  const handleChange = (field: string, value: string) => {
    setSettings({
      ...settings,
      [field]: value
    });
  };

  // Handle logo upload
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setLogo(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Clear logo
  const clearLogo = () => {
    setLogo(null);
    setLogoPreview("/logo-placeholder.png");
  };
  
  // Save settings
  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Link href="/admin/settings" className="text-gray-500 hover:text-gray-700 mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-800">General Settings</h1>
        </div>
        <p className="text-gray-600">Configure application name, description, and basic preferences</p>
      </div>
      
      {/* Success Message */}
      {showSuccess && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-800 rounded-lg p-4 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <span>Settings have been successfully saved.</span>
        </div>
      )}
      
      {/* Settings Form */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Application Information</h2>
          
          <div className="space-y-2">
            <FormField 
              label="Application Logo" 
              helper="Upload your application logo (recommended size: 200x60 px)"
            >
              <div className="flex flex-col space-y-3">
                {logoPreview && (
                  <div className="relative inline-block">
                    <img 
                      src={logoPreview} 
                      alt="Logo Preview" 
                      className="h-16 object-contain border rounded p-2"
                    />
                    <button 
                      onClick={clearLogo}
                      className="absolute -top-2 -right-2 bg-red-100 text-red-600 rounded-full p-1 hover:bg-red-200"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                )}
                
                <div className="flex items-center space-x-2">
                  <label className="cursor-pointer bg-indigo-50 text-indigo-600 px-3 py-2 rounded hover:bg-indigo-100 transition-colors flex items-center">
                    <Upload className="h-4 w-4 mr-2" />
                    <span>Upload Logo</span>
                    <input 
                      type="file" 
                      className="hidden" 
                      accept="image/*"
                      onChange={handleLogoChange}
                    />
                  </label>
                  {logo && <span className="text-sm text-gray-500">{logo.name}</span>}
                </div>
              </div>
            </FormField>
            
            <FormField 
              label="Application Name" 
              helper="The name of your application as displayed to users"
              required
            >
              <input
                type="text"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.siteName}
                onChange={(e) => handleChange('siteName', e.target.value)}
              />
            </FormField>
            
            <FormField 
              label="Legal Entity Name" 
              helper="The official legal name of your organization"
            >
              <input
                type="text"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.legalName}
                onChange={(e) => handleChange('legalName', e.target.value)}
              />
            </FormField>
            
            <FormField 
              label="Description" 
              helper="A brief description of your application"
            >
              <textarea
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
                value={settings.description}
                onChange={(e) => handleChange('description', e.target.value)}
              />
            </FormField>
          </div>
        </div>
        
        <div className="border-t border-gray-100 p-6">
          <h2 className="text-lg font-semibold mb-4">Contact Information</h2>
          
          <div className="space-y-2">
            <FormField 
              label="Support Email" 
              helper="The email address users can contact for support"
              required
            >
              <input
                type="email"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.supportEmail}
                onChange={(e) => handleChange('supportEmail', e.target.value)}
              />
            </FormField>
            
            <FormField 
              label="Contact Email" 
              helper="General contact email for your organization"
            >
              <input
                type="email"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.contactEmail}
                onChange={(e) => handleChange('contactEmail', e.target.value)}
              />
            </FormField>
            
            <FormField 
              label="Contact Phone" 
              helper="Phone number for your organization"
            >
              <input
                type="tel"
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.contactPhone}
                onChange={(e) => handleChange('contactPhone', e.target.value)}
              />
            </FormField>
            
            <FormField 
              label="Business Address" 
              helper="Physical address of your organization"
            >
              <textarea
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={2}
                value={settings.address}
                onChange={(e) => handleChange('address', e.target.value)}
              />
            </FormField>
          </div>
        </div>
        
        <div className="border-t border-gray-100 p-6">
          <h2 className="text-lg font-semibold mb-4">Regional Settings</h2>
          
          <div className="space-y-2">
            <FormField 
              label="Default Timezone" 
              helper="The default timezone for the application"
            >
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.timezone}
                onChange={(e) => handleChange('timezone', e.target.value)}
              >
                <option value="UTC">UTC</option>
                <option value="EST">Eastern Time (EST)</option>
                <option value="CST">Central Time (CST)</option>
                <option value="MST">Mountain Time (MST)</option>
                <option value="PST">Pacific Time (PST)</option>
                <option value="GMT">Greenwich Mean Time (GMT)</option>
                <option value="CET">Central European Time (CET)</option>
                <option value="IST">Indian Standard Time (IST)</option>
                <option value="JST">Japan Standard Time (JST)</option>
                <option value="AEST">Australian Eastern Standard Time (AEST)</option>
              </select>
            </FormField>
            
            <FormField 
              label="Default Language" 
              helper="The default language for the application"
            >
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.language}
                onChange={(e) => handleChange('language', e.target.value)}
              >
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="zh">Chinese</option>
                <option value="ja">Japanese</option>
                <option value="ar">Arabic</option>
                <option value="ru">Russian</option>
                <option value="pt">Portuguese</option>
                <option value="hi">Hindi</option>
              </select>
            </FormField>
            
            <FormField 
              label="Date Format" 
              helper="The default date format for the application"
            >
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.dateFormat}
                onChange={(e) => handleChange('dateFormat', e.target.value)}
              >
                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                <option value="YYYY/MM/DD">YYYY/MM/DD</option>
                <option value="DD-MMM-YYYY">DD-MMM-YYYY</option>
              </select>
            </FormField>
            
            <FormField 
              label="Default Currency" 
              helper="The default currency for the application"
            >
              <select
                className="w-full p-2 border border-gray-300 rounded-md"
                value={settings.currencyCode}
                onChange={(e) => handleChange('currencyCode', e.target.value)}
              >
                <option value="USD">US Dollar (USD)</option>
                <option value="EUR">Euro (EUR)</option>
                <option value="GBP">British Pound (GBP)</option>
                <option value="CAD">Canadian Dollar (CAD)</option>
                <option value="AUD">Australian Dollar (AUD)</option>
                <option value="JPY">Japanese Yen (JPY)</option>
                <option value="INR">Indian Rupee (INR)</option>
                <option value="CNY">Chinese Yuan (CNY)</option>
              </select>
            </FormField>
          </div>
        </div>
        
        <div className="border-t border-gray-100 p-6 bg-gray-50 flex justify-end">
          <div className="flex gap-3">
            <Link 
              href="/admin/settings"
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
            >
              Cancel
            </Link>
            <button 
              onClick={saveSettings}
              className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
                isSaving 
                  ? "bg-gray-200 text-gray-600 cursor-not-allowed" 
                  : "bg-indigo-600 text-white hover:bg-indigo-700"
              }`}
              disabled={isSaving}
            >
              <Save className="h-4 w-4" />
              {isSaving ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 