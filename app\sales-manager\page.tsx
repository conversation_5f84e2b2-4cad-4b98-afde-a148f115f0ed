"use client";

import { useState } from "react";
import {
  Users,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  CheckCircle2,
  AlertCircle,
  Zap,
  BarChart3,
  Clock
} from "lucide-react";
import { TeamOverview, Executive } from "./components/team-overview";
import { PerformanceMetrics } from "./components/performance-metrics";
import { LeadDistribution } from "./components/lead-distribution";
import { ComplianceTracker } from "./components/compliance-tracker";
import { WorkloadHeatmap } from "./components/workload-heatmap";
import GlassCard from "@/components/GlassCard";
import RoleBasedAccess from "@/components/auth/RoleBasedAccess";

// Mock data
const salesData = {
  totalLeads: 128,
  assignedLeads: 97,
  conversionRate: 68,
  conversionTrend: 4.2,
  responseTime: 2.1,
  responseTimeTrend: -0.3,
  clientSatisfaction: 4.8,
  satisfactionTrend: 0.1,
  visaTypes: [
    { type: 'EB-1', count: 35, percentage: 27 },
    { type: 'O-1', count: 28, percentage: 22 },
    { type: 'H-1B', count: 42, percentage: 33 },
    { type: 'Student', count: 23, percentage: 18 },
  ]
};

const executives: Executive[] = [
  { id: 'exec1', name: 'Priya Sharma', avatar: '', metrics: { conversions: 42, revenue: '₹128k', satisfaction: 4.7 }, status: 'exceeding', lastActivity: '2h ago', workload: 12 },
  { id: 'exec2', name: 'Rahul Singh', avatar: '', metrics: { conversions: 35, revenue: '₹105k', satisfaction: 4.5 }, status: 'meeting', lastActivity: '1h ago', workload: 15 },
  { id: 'exec3', name: 'Amit Patel', avatar: '', metrics: { conversions: 38, revenue: '₹116k', satisfaction: 4.6 }, status: 'meeting', lastActivity: '30m ago', workload: 17 },
  { id: 'exec4', name: 'Neha Kumar', avatar: '', metrics: { conversions: 44, revenue: '₹132k', satisfaction: 4.8 }, status: 'exceeding', lastActivity: '15m ago', workload: 11 },
];

const recentViolations = [
  { id: 'vio1', type: 'document-accuracy', executive: 'Rahul Singh', date: '2023-05-15', severity: 'low' },
  { id: 'vio2', type: 'response-time', executive: 'Amit Patel', date: '2023-05-14', severity: 'medium' }
];

const pendingAlerts = [
  { id: 'alert1', type: 'deadline-approaching', description: 'EB-1 submission deadline for client ABC Corp', dueDate: '2023-05-20' },
  { id: 'alert2', type: 'follow-up-required', description: 'O-1 document collection from client John Doe', dueDate: '2023-05-18' }
];

const pendingLeads = [
  { id: 'lead1', name: 'Global Tech Inc.', service: 'EB-1', priority: 'high', status: 'new' },
  { id: 'lead2', name: 'Johnson Family', service: 'Student Visa', priority: 'medium', status: 'new' },
  { id: 'lead3', name: 'Sarah Williams', service: 'O-1', priority: 'high', status: 'new' },
  { id: 'lead4', name: 'Micron Solutions', service: 'H-1B', priority: 'medium', status: 'new' },
];

export default function SalesManagerDashboard() {
  const [timeframe, setTimeframe] = useState("This Month");
  const [distributionStrategy, setDistributionStrategy] = useState("balanced");

  return (
    <RoleBasedAccess allowedRoles={['admin', 'sales-manager']}>
      <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Sales Manager Dashboard
          </h1>
          <p className="text-gray-600 mt-1">Welcome back! Here's what's happening with your team today.</p>
        </div>
        <div className="flex items-center gap-3">
          <select 
            className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 py-2 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 shadow-sm"
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
          >
            <option>Today</option>
            <option>This Week</option>
            <option>This Month</option>
            <option>This Quarter</option>
          </select>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Total Leads</p>
              <p className="text-2xl font-bold text-indigo-800">{salesData.totalLeads}</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Users size={24} />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <p className="text-sm text-gray-600">{salesData.assignedLeads} assigned</p>
              <p className="ml-auto text-sm font-medium text-green-600 flex items-center">
                <ArrowUpRight size={16} className="mr-1" />
                {Math.round((salesData.assignedLeads / salesData.totalLeads) * 100)}%
              </p>
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-600 to-indigo-700 rounded-full" style={{width: `${(salesData.assignedLeads / salesData.totalLeads) * 100}%`}}></div>
            </div>
          </div>
        </GlassCard>

        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Conversion Rate</p>
              <p className="text-2xl font-bold text-indigo-800">{salesData.conversionRate}%</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-green-400 to-emerald-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <CheckCircle2 size={24} />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <p className="text-sm text-gray-600">vs. previous period</p>
              <p className="ml-auto text-sm font-medium text-green-600 flex items-center">
                <ArrowUpRight size={16} className="mr-1" />
                {salesData.conversionTrend}%
              </p>
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-green-500 to-emerald-400 rounded-full" style={{width: `${salesData.conversionRate}%`}}></div>
            </div>
          </div>
        </GlassCard>

        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Avg. Response Time</p>
              <p className="text-2xl font-bold text-indigo-800">{salesData.responseTime}h</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-blue-400 to-cyan-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Clock size={24} />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <p className="text-sm text-gray-600">vs. previous period</p>
              <p className="ml-auto text-sm font-medium text-green-600 flex items-center">
                <ArrowDownRight size={16} className="mr-1" />
                {Math.abs(salesData.responseTimeTrend)}h
              </p>
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-500 to-cyan-400 rounded-full" style={{width: `${60}%`}}></div>
            </div>
          </div>
        </GlassCard>

        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Client Satisfaction</p>
              <p className="text-2xl font-bold text-indigo-800">{salesData.clientSatisfaction}★</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-purple-400 to-pink-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Zap size={24} />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <p className="text-sm text-gray-600">vs. previous period</p>
              <p className="ml-auto text-sm font-medium text-green-600 flex items-center">
                <ArrowUpRight size={16} className="mr-1" />
                {salesData.satisfactionTrend}
              </p>
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-purple-500 to-pink-400 rounded-full" style={{width: `${(salesData.clientSatisfaction / 5) * 100}%`}}></div>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Main 3-column Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2">
          <div className="space-y-6">
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <h2 className="text-lg font-semibold text-indigo-800 mb-4">Team Performance</h2>
              <TeamOverview executives={executives} />
            </GlassCard>

            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <h2 className="text-lg font-semibold text-indigo-800 mb-4">Team Workload</h2>
              <WorkloadHeatmap 
                executives={executives} 
                thresholds={{
                  optimal: 12,
                  warning: 15,
                  critical: 18
                }}
              />
            </GlassCard>
          </div>
        </div>

        {/* Middle Column (Main Content) */}
        <div className="lg:col-span-3">
          <div className="space-y-6">
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-indigo-800">Lead Distribution</h2>
                <select 
                  className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-sm text-indigo-700 py-1.5 px-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  value={distributionStrategy}
                  onChange={(e) => setDistributionStrategy(e.target.value)}
                >
                  <option value="balanced">Balanced</option>
                  <option value="skill-based">Skill-based</option>
                  <option value="priority">Priority</option>
                </select>
              </div>
              <LeadDistribution 
                executives={executives} 
                leads={pendingLeads}
                strategy={distributionStrategy}
                rules={{
                  maxPerExecutive: 15,
                  serviceSpecialization: {
                    'EB-1': ['exec1', 'exec3'],
                    'O-1': ['exec2', 'exec4']
                  }
                }}
              />
            </GlassCard>

            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <h2 className="text-lg font-semibold text-indigo-800 mb-4">Performance Metrics</h2>
              <PerformanceMetrics 
                data={executives} 
                metrics={[
                  { label: 'Conversion Rate', value: '68%', trend: 4.2 },
                  { label: 'Avg. Response Time', value: '2.1h', trend: -0.3 },
                  { label: 'Client Satisfaction', value: '4.8★', trend: 0.1 }
                ]} 
              />
            </GlassCard>
          </div>
        </div>

        {/* Right Column */}
        <div className="lg:col-span-2">
          <div className="space-y-6">
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <h2 className="text-lg font-semibold text-indigo-800 mb-4">Compliance Oversight</h2>
              <ComplianceTracker 
                violations={recentViolations}
                alerts={pendingAlerts}
                kpis={{
                  documentAccuracy: 98.4,
                  policyAdherence: 99.1
                }}
              />
            </GlassCard>

            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <h2 className="text-lg font-semibold text-indigo-800 mb-4">Service Distribution</h2>
              <div className="space-y-4">
                {salesData.visaTypes.map((type) => (
                  <div key={type.type}>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600">{type.type}</span>
                      <span className="font-medium text-indigo-800">{type.count} leads ({type.percentage}%)</span>
                    </div>
                    <div className="w-full bg-indigo-100/40 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          type.type === 'EB-1' ? 'bg-gradient-to-r from-blue-500 to-indigo-600' :
                          type.type === 'O-1' ? 'bg-gradient-to-r from-indigo-500 to-purple-600' :
                          type.type === 'H-1B' ? 'bg-gradient-to-r from-purple-500 to-pink-600' : 
                          'bg-gradient-to-r from-blue-400 to-cyan-500'
                        }`} 
                        style={{ width: `${type.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
      </div>
    </RoleBasedAccess>
  );
}