import crypto from 'crypto';

// Environment variables should be properly set in production
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-dev-key-32-chars-exactly!';
const ENCRYPTION_ALGORITHM = 'aes-256-cbc';

// Interface for encrypted data
interface EncryptedData {
  iv: string;
  data: string;
}

/**
 * Encrypts sensitive lead data for GDPR compliance
 * @param data - The data to encrypt
 * @returns Object containing the initialization vector and encrypted data
 */
export function encryptLeadData(data: Record<string, any>): EncryptedData {
  try {
    // Generate a random initialization vector
    const iv = crypto.randomBytes(16);
    
    // Create a cipher using the encryption key and iv
    const cipher = crypto.createCipheriv(
      ENCRYPTION_ALGORITHM, 
      Buffer.from(ENCRYPTION_KEY), 
      iv
    );
    
    // Encrypt the data
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Return the iv and encrypted data
    return {
      iv: iv.toString('hex'),
      data: encrypted
    };
  } catch (error) {
    console.error('Error encrypting data:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypts encrypted lead data
 * @param encryptedData - Object containing the iv and encrypted data
 * @returns The decrypted data as an object
 */
export function decryptLeadData(encryptedData: EncryptedData): Record<string, any> {
  try {
    // Convert the iv from hex to bytes
    const iv = Buffer.from(encryptedData.iv, 'hex');
    
    // Create a decipher using the encryption key and iv
    const decipher = crypto.createDecipheriv(
      ENCRYPTION_ALGORITHM, 
      Buffer.from(ENCRYPTION_KEY), 
      iv
    );
    
    // Decrypt the data
    let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    // Parse the decrypted JSON string back to an object
    return JSON.parse(decrypted);
  } catch (error) {
    console.error('Error decrypting data:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Anonymizes personal data for GDPR compliance
 * @param data - The data object containing personal information
 * @param fieldsToAnonymize - Array of field names to anonymize
 * @returns The anonymized data object
 */
export function anonymizeData(
  data: Record<string, any>, 
  fieldsToAnonymize: string[]
): Record<string, any> {
  // Create a copy of the data to avoid mutating the original
  const anonymizedData = { ...data };
  
  // Anonymize specified fields
  for (const field of fieldsToAnonymize) {
    if (field in anonymizedData) {
      if (typeof anonymizedData[field] === 'string') {
        // Replace string values with asterisks, preserving length
        anonymizedData[field] = '*'.repeat(anonymizedData[field].length);
      } else if (typeof anonymizedData[field] === 'number') {
        // Replace numbers with 0
        anonymizedData[field] = 0;
      } else if (typeof anonymizedData[field] === 'object' && anonymizedData[field] !== null) {
        // For objects, indicate they've been anonymized
        anonymizedData[field] = '[REDACTED]';
      } else {
        // For other types, set to null
        anonymizedData[field] = null;
      }
    }
  }
  
  return anonymizedData;
}

/**
 * Logs data access for audit trail
 * @param userId - ID of the user accessing the data
 * @param action - Type of action performed
 * @param dataId - ID of the data being accessed
 * @param reason - Reason for access
 */
export function logDataAccess(
  userId: string,
  action: 'view' | 'edit' | 'delete' | 'export',
  dataId: string,
  reason: string
): void {
  // In a real implementation, this would write to a secure audit log
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    userId,
    action,
    dataId,
    reason,
    ipAddress: '[IP ADDRESS WOULD BE CAPTURED HERE]'
  };
  
  console.log('Data Access Log:', logEntry);
}

/**
 * Creates a data retention policy function that can be applied to data
 * @param retentionPeriodDays - Number of days to retain data
 * @returns Function that determines if data should be retained
 */
export function createRetentionPolicy(retentionPeriodDays: number) {
  return function shouldRetainData(createdAt: Date): boolean {
    const now = new Date();
    const ageInDays = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
    return ageInDays <= retentionPeriodDays;
  };
} 