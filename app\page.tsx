"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { ArrowRight, GraduationCap, Briefcase, Plane, Calendar, MessageCircle } from "lucide-react"
import StatsSection from "@/components/home/<USER>"
import UniversityExplorer from "@/components/home/<USER>"
import ImmigrationPathways from "@/components/home/<USER>"
import TestimonialsSection from "@/components/home/<USER>"
import useLeadForm from "@/hooks/useLeadForm"
import { ReactNode } from "react"
import { SignUpButton, SignedIn, SignedOut } from "@clerk/nextjs"

interface GlassCardProps {
  children: ReactNode;
  className?: string;
}

function GlassCard({ children, className = "" }: GlassCardProps) {
  return (
    <div
      className={`rounded-xl p-4 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl ${className}`}
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {children}
    </div>
  )
}

export default function Home() {
  const { openLeadForm: openCounselingForm, LeadFormComponent: CounselingForm } = useLeadForm({
    defaultService: "Counseling Session",
    source: "Home Page - CTA",
    formType: "counseling"
  });

  return (
    <div className="flex flex-col bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Hero Section */}
      <section className="py-16 md:py-24">
        <div className="container max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
                Your Gateway to Global Success
              </h1>
              <p className="text-lg text-gray-600 mb-8">
                Join thousands of students and professionals who trust Visa Mentor to guide their journey to study,
                work, and settle abroad.
              </p>
              <div className="flex flex-wrap gap-4">
                <SignedOut>
                  <SignUpButton>
                    <Button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-3 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md">
                      Join the Network
                    </Button>
                  </SignUpButton>
                </SignedOut>
                <SignedIn>
                  <Button
                    className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-3 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
                    asChild
                  >
                    <Link href={"/user"}>Go to Dashboard</Link>
                  </Button>
                </SignedIn>
                <Button
                  variant="outline"
                  className="border border-indigo-300 rounded-lg text-sm py-2 px-3 bg-white text-indigo-800 font-medium"
                  asChild
                >
                  <Link href={"/services" as any}>Explore Services</Link>
                </Button>
              </div>
            </div>
            <GlassCard className="transform transition hover:translate-y-[-4px]">
              <div className="relative h-[400px] rounded-lg overflow-hidden">
              <Image
                src="/images/visa-mentor-logo.png"
                alt="Visa Mentor Logo"
                width={200}
                height={50}
                className="w-auto h-auto"
              />
            </div>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <StatsSection />

      {/* Pathways Section */}
      <section className="py-16">
        <div className="container max-w-7xl mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 text-indigo-800">
            Discover the Right Immigration Pathway
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <GlassCard className="transform transition hover:translate-y-[-4px]">
              <CardContent className="p-6">
                <div className="mb-4 text-indigo-600">
                  <GraduationCap size={48} />
                </div>
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Study Abroad at Top Universities</h3>
                <p className="text-gray-600 mb-4">
                  Explore world-class institutions across the US, UK, Canada, Australia, Germany, and more.
                </p>
                <Link href={"/services/study-abroad" as any} className="text-indigo-600 font-medium flex items-center">
                  Learn more <ArrowRight size={16} className="ml-1" />
                </Link>
              </CardContent>
            </GlassCard>

            <GlassCard className="transform transition hover:translate-y-[-4px]">
              <CardContent className="p-6">
                <div className="mb-4 text-indigo-600">
                  <Plane size={48} />
                </div>
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Get a Visa to Travel Now</h3>
                <p className="text-gray-600 mb-4">
                  Seamless visa processing for tourism, business, medical visits, and more.
                </p>
                <Link href={"/services/travel-visas" as any} className="text-indigo-600 font-medium flex items-center">
                  Learn more <ArrowRight size={16} className="ml-1" />
                </Link>
              </CardContent>
            </GlassCard>

            <GlassCard className="transform transition hover:translate-y-[-4px]">
              <CardContent className="p-6">
                <div className="mb-4 text-indigo-600">
                  <Briefcase size={48} />
                </div>
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Work Abroad with a Talent-Based Visa</h3>
                <p className="text-gray-600 mb-4">
                  Specialized assistance for work permits, skilled migration, and entrepreneur visas.
                </p>
                <Link href={"/services/work-visas" as any} className="text-indigo-600 font-medium flex items-center">
                  Learn more <ArrowRight size={16} className="ml-1" />
                </Link>
              </CardContent>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* University Explorer */}
      <UniversityExplorer />

      {/* Immigration Pathways */}
      <ImmigrationPathways />

      {/* Testimonials */}
      <TestimonialsSection />

      {/* CTA Section */}
      <section className="py-16 mb-6">
        <div className="container max-w-7xl mx-auto px-4">
          <GlassCard className="transform transition hover:translate-y-[-4px] py-8">
            <div className="text-center">
              <h2 className="text-lg font-semibold text-indigo-800 mb-6">Get in Touch</h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Button 
              onClick={openCounselingForm}
                  className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-3 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center"
            >
                  <Calendar className="w-4 h-4 mr-2" />
              Book a Counseling Session
            </Button>
                <Button 
                  className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-lg py-3 px-4 font-medium hover:from-indigo-700 hover:to-purple-800 transition shadow-md flex items-center"
                >
                  <ArrowRight className="w-4 h-4 mr-2" />
              Download the Visa Mentor App
            </Button>
                <Button 
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg py-3 px-4 font-medium hover:from-purple-700 hover:to-pink-700 transition shadow-md flex items-center"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
              Connect on WhatsApp
            </Button>
          </div>
            </div>
          </GlassCard>
        </div>
      </section>
      
      {/* Lead form component */}
      <CounselingForm />
    </div>
  )
}
