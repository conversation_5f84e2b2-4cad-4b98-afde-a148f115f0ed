import Link from "next/link";
import { FileText, ArrowRight } from "lucide-react";

interface GuideCardProps {
  title: string;
  description: string;
  category: string;
  readTime: string;
  href: string;
}

function GuideCard({ title, description, category, readTime, href }: GuideCardProps) {
  return (
    <div className="rounded-xl p-6 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl hover:translate-y-[-4px]"
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}>
      <div className="flex justify-between items-start mb-3">
        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
          {category}
        </span>
        <span className="text-xs text-gray-500">{readTime}</span>
      </div>
      <h3 className="text-lg font-semibold text-indigo-800 mb-2">{title}</h3>
      <p className="text-sm text-gray-600 mb-4">
        {description}
      </p>
      <Link href={href as any} className="text-indigo-600 font-medium flex items-center">
        Read Guide <ArrowRight size={16} className="ml-1" />
      </Link>
    </div>
  );
}

export default function GuidesPage() {
  const guides = [
    {
      title: "Student Visa Application Process",
      description: "Complete walkthrough of the student visa application process for major study destinations including the US, UK, Canada, and Australia.",
      category: "Student Visa",
      readTime: "15 min read",
      href: "#student-visa",
    },
    {
      title: "Work Visa Requirements Guide",
      description: "Detailed explanation of eligibility criteria and documentation needed for various work visa categories across popular destinations.",
      category: "Work Visa",
      readTime: "18 min read",
      href: "#work-visa",
    },
    {
      title: "Family Reunification Visas",
      description: "Step-by-step guide to applying for family reunification and dependent visas with sample documentation and timelines.",
      category: "Family Visa",
      readTime: "12 min read",
      href: "#family-visa",
    },
    {
      title: "Immigration Points Calculator",
      description: "How to calculate your points for skilled migration programs in Australia, Canada, and New Zealand with practical examples.",
      category: "Skilled Migration",
      readTime: "10 min read",
      href: "#points-calculator",
    },
    {
      title: "Tourist Visa Application Tips",
      description: "Best practices and common pitfalls to avoid when applying for tourist visas, including documentation and interview preparation.",
      category: "Tourist Visa",
      readTime: "8 min read",
      href: "#tourist-visa",
    },
    {
      title: "Post-Graduate Work Permits",
      description: "A comprehensive guide to obtaining post-study work rights after completing your education abroad.",
      category: "Graduate Visa",
      readTime: "14 min read",
      href: "#post-graduate",
    }
  ];

  return (
    <div>
      <h2 className="text-xl font-semibold text-indigo-800 mb-6">Immigration & Visa Guides</h2>
      
      <p className="text-gray-600 mb-8">
        Our step-by-step guides provide detailed instructions and insights to help you navigate 
        various visa application processes with confidence. Each guide is carefully curated by 
        our immigration experts to ensure accuracy and relevance.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {guides.map((guide, index) => (
          <GuideCard 
            key={index}
            title={guide.title}
            description={guide.description}
            category={guide.category}
            readTime={guide.readTime}
            href={guide.href}
          />
        ))}
      </div>
      
      <div className="mt-12 p-6 rounded-xl bg-indigo-50/70 border border-indigo-100">
        <h3 className="text-lg font-semibold text-indigo-800 mb-3">Need a Custom Guide?</h3>
        <p className="text-gray-600 mb-4">
          Don't see what you're looking for? Our immigration specialists can create a 
          customized guide tailored to your specific situation and requirements.
        </p>
        <Link href="/contact" className="inline-block bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md">
          Request Custom Guide
        </Link>
      </div>
    </div>
  );
} 