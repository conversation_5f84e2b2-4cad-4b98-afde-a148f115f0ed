"use client";

import { useState, useEffect } from "react";
import { Plus, Search, Edit, Trash2, Shield, UserX, User, CheckCircle } from "lucide-react";

// Import our reusable components
import SettingsHeader from "@/app/components/admin/SettingsHeader";
import SettingsSection from "@/app/components/admin/SettingsSection";
import Form<PERSON>ield from "@/app/components/admin/FormField";
import SaveButton from "@/app/components/admin/SaveButton";
import SuccessMessage from "@/app/components/admin/SuccessMessage";

// Mock user data
const mockUsers = [
  { 
    id: "user-1", 
    name: "<PERSON>", 
    email: "<EMAIL>", 
    role: "Admin", 
    status: "Active",
    lastLogin: "2023-05-12T10:30:00Z"
  },
  { 
    id: "user-2", 
    name: "<PERSON>", 
    email: "<EMAIL>", 
    role: "Manager", 
    status: "Active",
    lastLogin: "2023-05-10T14:20:00Z" 
  },
  { 
    id: "user-3", 
    name: "<PERSON>", 
    email: "<EMAIL>", 
    role: "Analyst", 
    status: "Inactive",
    lastLogin: "2023-04-20T09:15:00Z" 
  },
  { 
    id: "user-4", 
    name: "<PERSON> <PERSON>", 
    email: "<EMAIL>", 
    role: "Support", 
    status: "Active",
    lastLogin: "2023-05-11T16:45:00Z" 
  },
  { 
    id: "user-5", 
    name: "<PERSON> <PERSON>", 
    email: "<EMAIL>", 
    role: "Manager", 
    status: "Active",
    lastLogin: "2023-05-09T11:30:00Z" 
  },
];

// Mock roles data
const mockRoles = [
  { id: "role-1", name: "Admin", description: "Full system access", userCount: 2 },
  { id: "role-2", name: "Manager", description: "Limited administrative access", userCount: 4 },
  { id: "role-3", name: "Analyst", description: "View and analyze data", userCount: 7 },
  { id: "role-4", name: "Support", description: "Customer support access", userCount: 3 },
];

export default function UserManagementPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("users");
  const [searchQuery, setSearchQuery] = useState("");
  const [users, setUsers] = useState(mockUsers);
  const [roles, setRoles] = useState(mockRoles);
  
  // Save settings
  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  // Filter users based on search query
  const filteredUsers = users.filter(user => 
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Add a new user (mock functionality)
  const addNewUser = () => {
    alert("Open new user modal (mock functionality)");
  };

  // Format date to a readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short'
    }).format(date);
  };

  return (
    <div>
      {/* Header */}
      <SettingsHeader 
        title="User Management" 
        description="Manage users, roles, and permissions"
        actions={
          <SaveButton isLoading={isSaving} onClick={saveSettings} />
        }
      />
      
      {/* Success Message */}
      {showSuccess && (
        <SuccessMessage message="User settings have been successfully saved." />
      )}
      
      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === "users" 
              ? "text-indigo-600 border-b-2 border-indigo-600" 
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("users")}
        >
          Users
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === "roles" 
              ? "text-indigo-600 border-b-2 border-indigo-600" 
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("roles")}
        >
          Roles & Permissions
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === "settings" 
              ? "text-indigo-600 border-b-2 border-indigo-600" 
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => setActiveTab("settings")}
        >
          User Settings
        </button>
      </div>
      
      {/* Users Tab */}
      {activeTab === "users" && (
        <>
          <div className="flex justify-between items-center mb-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search users..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <button
              onClick={addNewUser}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-indigo-700"
            >
              <Plus className="h-4 w-4" />
              Add User
            </button>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Login
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <User className="h-5 w-5 text-indigo-600" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{user.name}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.status === "Active" 
                            ? "bg-green-100 text-green-800" 
                            : "bg-gray-100 text-gray-800"
                        }`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(user.lastLogin)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-indigo-600 hover:text-indigo-900 mr-3">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-900">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </>
      )}
      
      {/* Roles Tab */}
      {activeTab === "roles" && (
        <SettingsSection title="Roles & Permissions" description="Manage user roles and their permissions">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Users
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {roles.map((role) => (
                  <tr key={role.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center">
                          <Shield className="h-4 w-4 text-indigo-600" />
                        </div>
                        <div className="ml-3 text-sm font-medium text-gray-900">{role.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {role.description}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {role.userCount} users
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button className="text-indigo-600 hover:text-indigo-900 mr-3">
                        Edit
                      </button>
                      <button className="text-indigo-600 hover:text-indigo-900">
                        Permissions
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="mt-4">
            <button className="bg-indigo-50 text-indigo-600 px-4 py-2 rounded-md flex items-center gap-2 hover:bg-indigo-100">
              <Plus className="h-4 w-4" />
              Add New Role
            </button>
          </div>
        </SettingsSection>
      )}
      
      {/* User Settings Tab */}
      {activeTab === "settings" && (
        <>
          <SettingsSection title="User Registration" description="Configure how users can register and join the system">
            <FormField label="Registration Mode" helper="Control how new users can join your system">
              <select className="w-full p-2 border border-gray-300 rounded-md">
                <option value="open">Open Registration</option>
                <option value="invite">Invite Only</option>
                <option value="closed">Closed</option>
                <option value="approval">Admin Approval Required</option>
              </select>
            </FormField>
            
            <FormField label="Email Verification" helper="Require users to verify their email address">
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="email-verify" 
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  defaultChecked 
                />
                <label htmlFor="email-verify" className="ml-2 block text-sm text-gray-700">
                  Require email verification
                </label>
              </div>
            </FormField>
            
            <FormField label="Default Role" helper="Role assigned to new users upon registration">
              <select className="w-full p-2 border border-gray-300 rounded-md">
                {roles.map(role => (
                  <option key={role.id} value={role.id}>{role.name}</option>
                ))}
              </select>
            </FormField>
          </SettingsSection>
          
          <SettingsSection title="Account Settings" description="Configure user account policies">
            <FormField label="Username Policy" helper="Set requirements for usernames">
              <div className="space-y-2">
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="username-alphanum" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    defaultChecked 
                  />
                  <label htmlFor="username-alphanum" className="ml-2 block text-sm text-gray-700">
                    Alphanumeric characters only
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="username-unique" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    defaultChecked 
                  />
                  <label htmlFor="username-unique" className="ml-2 block text-sm text-gray-700">
                    Require unique usernames
                  </label>
                </div>
                
                <div className="flex items-center mt-2">
                  <label htmlFor="username-min" className="block text-sm text-gray-700 mr-2 w-44">
                    Minimum length:
                  </label>
                  <input 
                    type="number" 
                    id="username-min" 
                    className="w-20 p-1 border border-gray-300 rounded-md"
                    defaultValue="4" 
                  />
                </div>
              </div>
            </FormField>
            
            <FormField label="Account Suspension" helper="Policy for handling inactive accounts">
              <div className="space-y-2">
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="auto-suspend" 
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="auto-suspend" className="ml-2 block text-sm text-gray-700">
                    Automatically suspend inactive accounts
                  </label>
                </div>
                
                <div className="flex items-center mt-2">
                  <label htmlFor="suspend-days" className="block text-sm text-gray-700 mr-2 w-44">
                    Inactivity period (days):
                  </label>
                  <input 
                    type="number" 
                    id="suspend-days" 
                    className="w-20 p-1 border border-gray-300 rounded-md"
                    defaultValue="90" 
                  />
                </div>
              </div>
            </FormField>
          </SettingsSection>
        </>
      )}
    </div>
  );
} 