"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useUserRole } from "@/components/auth/RoleBasedAccess"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import {
  LayoutDashboard,
  Users,
  BarChart3,
  UserCheck,
  Building,
  ChevronDown,
  Home,
  Settings
} from "lucide-react"

interface DashboardOption {
  name: string
  href: string
  icon: React.ReactNode
  description: string
  roles: string[]
}

const dashboardOptions: DashboardOption[] = [
  {
    name: "Admin Dashboard",
    href: "/admin",
    icon: <Settings className="h-4 w-4" />,
    description: "System administration and user management",
    roles: ["admin"]
  },
  {
    name: "CRM Dashboard", 
    href: "/crm",
    icon: <Users className="h-4 w-4" />,
    description: "Customer relationship management",
    roles: ["admin", "crm", "sales-manager"]
  },
  {
    name: "Sales Manager",
    href: "/sales-manager", 
    icon: <BarChart3 className="h-4 w-4" />,
    description: "Team performance and lead distribution",
    roles: ["admin", "sales-manager"]
  },
  {
    name: "Sales Executive",
    href: "/sales-executive",
    icon: <UserCheck className="h-4 w-4" />,
    description: "Lead management and client communications", 
    roles: ["admin", "sales-manager", "sales-executive"]
  },
  {
    name: "HR Dashboard",
    href: "/hr",
    icon: <Building className="h-4 w-4" />,
    description: "Human resources and compliance",
    roles: ["admin", "hr"]
  },
  {
    name: "General Dashboard",
    href: "/dashboard", 
    icon: <LayoutDashboard className="h-4 w-4" />,
    description: "Applications and appointments",
    roles: ["admin", "sales-manager", "sales-executive", "crm", "hr", "user"]
  },
  {
    name: "User Portal",
    href: "/user",
    icon: <Home className="h-4 w-4" />,
    description: "Personal visa journey and bookings",
    roles: ["admin", "sales-manager", "sales-executive", "crm", "hr", "user"]
  }
]

export default function DashboardSwitcher() {
  const { role, isLoading } = useUserRole()
  const pathname = usePathname()
  const [open, setOpen] = useState(false)

  if (isLoading || !role) {
    return (
      <div className="flex items-center gap-2">
        <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
      </div>
    )
  }

  // Filter dashboards based on user role
  const availableDashboards = dashboardOptions.filter(dashboard => 
    dashboard.roles.includes(role)
  )

  // Find current dashboard
  const currentDashboard = availableDashboards.find(dashboard => 
    pathname.startsWith(dashboard.href)
  ) || availableDashboards[0]

  if (availableDashboards.length <= 1) {
    // If user only has access to one dashboard, show it as a simple link
    return (
      <Link href={currentDashboard.href} className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900">
        {currentDashboard.icon}
        {currentDashboard.name}
      </Link>
    )
  }

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2 min-w-[200px] justify-between">
          <div className="flex items-center gap-2">
            {currentDashboard.icon}
            <span className="font-medium">{currentDashboard.name}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[300px]">
        <DropdownMenuLabel>Switch Dashboard</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {availableDashboards.map((dashboard) => (
          <DropdownMenuItem key={dashboard.href} asChild>
            <Link 
              href={dashboard.href}
              className={`flex items-start gap-3 p-3 cursor-pointer ${
                pathname.startsWith(dashboard.href) 
                  ? 'bg-blue-50 text-blue-700' 
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setOpen(false)}
            >
              <div className="mt-0.5">{dashboard.icon}</div>
              <div className="flex-1">
                <div className="font-medium">{dashboard.name}</div>
                <div className="text-xs text-gray-500 mt-0.5">{dashboard.description}</div>
              </div>
            </Link>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Compact version for mobile/smaller spaces
export function DashboardSwitcherCompact() {
  const { role, isLoading } = useUserRole()
  const pathname = usePathname()

  if (isLoading || !role) {
    return <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
  }

  const availableDashboards = dashboardOptions.filter(dashboard => 
    dashboard.roles.includes(role)
  )

  const currentDashboard = availableDashboards.find(dashboard => 
    pathname.startsWith(dashboard.href)
  ) || availableDashboards[0]

  if (availableDashboards.length <= 1) {
    return (
      <Link href={currentDashboard.href} className="p-2 rounded-lg hover:bg-gray-100">
        {currentDashboard.icon}
      </Link>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="p-2">
          {currentDashboard.icon}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuLabel>Dashboards</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {availableDashboards.map((dashboard) => (
          <DropdownMenuItem key={dashboard.href} asChild>
            <Link 
              href={dashboard.href}
              className={`flex items-center gap-2 ${
                pathname.startsWith(dashboard.href) ? 'bg-blue-50 text-blue-700' : ''
              }`}
            >
              {dashboard.icon}
              {dashboard.name}
            </Link>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
