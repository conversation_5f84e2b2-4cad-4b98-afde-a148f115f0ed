"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { 
  Users, 
  UserCheck, 
  Bell, 
  FileText, 
  MessageSquare, 
  Calendar, 
  Clock, 
  BarChart2, 
  DollarSign, 
  Star, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Phone,
  Mail,
  MessageCircle,
  Map,
  QrCode,
  Upload,
  Search,
  Filter,
  ChevronDown,
  ArrowUpRight
} from "lucide-react";
import GlassCard from "@/components/GlassCard";
import RoleBasedAccess from "@/components/auth/RoleBasedAccess";

// Mock data for the dashboard
const mockPerformanceData = {
  name: "<PERSON><PERSON> <PERSON>",
  role: "Senior Visa Counselor",
  monthlyQuota: 1500000, // 15 lakhs
  achieved: 985000, // 9.85 lakhs
  percentageAchieved: 65.7,
  topClient: "<PERSON><PERSON><PERSON> (EB-1)",
  pendingDocs: 3,
  overdueFollowups: 2,
  upcomingMeetings: 4,
  activeLeads: 18,
  conversionRate: 72,
  averageResponseTime: 3.2, // hours
};

// Mock data for active leads
const activeLeads = [
  {
    id: "LD-2023-5721",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    visaType: "EB-1",
    stage: "Document Collection",
    leadScore: 87,
    lastContact: "2023-05-18T14:30:00",
    nextFollowUp: "2023-05-21T11:00:00",
    pendingDocs: ["Financial Statements", "Publication Evidence"],
    sentiment: "Positive",
    notes: "Highly qualified candidate, professor at IIT with multiple publications"
  },
  {
    id: "LD-2023-5695",
    name: "Sneha Reddy",
    email: "<EMAIL>",
    phone: "+91 87654 32109",
    visaType: "Student Visa",
    stage: "Interview Preparation",
    leadScore: 92,
    lastContact: "2023-05-17T16:45:00",
    nextFollowUp: "2023-05-20T14:30:00",
    pendingDocs: ["Statement of Purpose"],
    sentiment: "Very Positive",
    notes: "Accepted to Stanford University with scholarship"
  },
  {
    id: "LD-2023-5682",
    name: "Rajat Gupta",
    email: "<EMAIL>",
    phone: "+91 76543 21098",
    visaType: "H1-B",
    stage: "Application Review",
    leadScore: 75,
    lastContact: "2023-05-16T10:15:00",
    nextFollowUp: "2023-05-19T11:30:00",
    pendingDocs: ["Employment Verification", "Degree Certificates"],
    sentiment: "Neutral",
    notes: "Software engineer with 6 years of experience, offer from Google"
  },
  {
    id: "LD-2023-5673",
    name: "Meera Patel",
    email: "<EMAIL>",
    phone: "+91 65432 10987",
    visaType: "O-1",
    stage: "Initial Consultation",
    leadScore: 68,
    lastContact: "2023-05-15T15:00:00",
    nextFollowUp: "2023-05-19T16:00:00",
    pendingDocs: ["Portfolio", "Awards Documentation", "Press Coverage"],
    sentiment: "Cautious",
    notes: "Talented artist with international exhibitions, but worried about visa process"
  },
];

// Mock data for upcoming meetings
const upcomingMeetings = [
  {
    id: 1,
    client: "Aditya Mehta",
    purpose: "Document Review",
    scheduledTime: "2023-05-19T10:30:00",
    mode: "In-person",
    location: "Office - Conference Room 2",
    duration: 45
  },
  {
    id: 2,
    client: "Sneha Reddy",
    purpose: "Interview Preparation",
    scheduledTime: "2023-05-20T14:30:00",
    mode: "Video Call",
    location: "Zoom",
    duration: 60
  },
  {
    id: 3,
    client: "Rajat Gupta",
    purpose: "Application Review",
    scheduledTime: "2023-05-19T11:30:00",
    mode: "Phone Call",
    location: "N/A",
    duration: 30
  },
  {
    id: 4,
    client: "Meera Patel",
    purpose: "Consultation Follow-up",
    scheduledTime: "2023-05-19T16:00:00",
    mode: "Video Call",
    location: "Google Meet",
    duration: 45
  }
];

// Mock data for recent communications
const recentCommunications = [
  {
    id: 1,
    client: "Aditya Mehta",
    type: "Email",
    timestamp: "2023-05-18T14:30:00",
    summary: "Sent document checklist and requested financial statements",
    sentiment: "Positive"
  },
  {
    id: 2,
    client: "Sneha Reddy",
    type: "Call",
    timestamp: "2023-05-17T16:45:00",
    summary: "Discussed interview preparation strategy and common questions",
    sentiment: "Very Positive"
  },
  {
    id: 3,
    client: "Meera Patel",
    type: "WhatsApp",
    timestamp: "2023-05-17T11:20:00",
    summary: "Answered questions about O-1 visa requirements and portfolio",
    sentiment: "Neutral"
  },
  {
    id: 4,
    client: "Rajat Gupta",
    type: "Email",
    timestamp: "2023-05-16T10:15:00",
    summary: "Requested additional employment verification documents",
    sentiment: "Neutral"
  }
];

// Available visa slots
const visaSlots = [
  {
    visaType: "B1/B2",
    consulate: "Mumbai",
    nextAvailable: "2023-07-15",
    slotsRemaining: 3
  },
  {
    visaType: "F1",
    consulate: "Delhi",
    nextAvailable: "2023-06-10",
    slotsRemaining: 0
  },
  {
    visaType: "H1-B",
    consulate: "Chennai",
    nextAvailable: "2023-08-05",
    slotsRemaining: 5
  },
  {
    visaType: "O-1",
    consulate: "Hyderabad",
    nextAvailable: "2023-07-22",
    slotsRemaining: 2
  }
];

// Type definitions for component props
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  className?: string;
}

interface PerformanceCardProps {
  monthlyQuota: number;
  achieved: number;
  topClient: string;
  pendingDocs: number;
  overdueFollowups: number;
}

// Component for stat card
function StatCard({ title, value, icon, color, className = "" }: StatCardProps) {
  return (
    <GlassCard className={`p-5 transform transition hover:translate-y-[-4px] ${className}`}>
      <div className="flex justify-between items-center">
        <div>
          <p className="text-sm text-indigo-700 font-medium">{title}</p>
          <p className="text-2xl font-bold text-indigo-800 mt-1">{value}</p>
        </div>
        <div className={`h-12 w-12 bg-gradient-to-br ${
          title === "Active Leads" ? "from-blue-400 to-indigo-300" : 
          title === "Conversion Rate" ? "from-green-400 to-emerald-300" : 
          title === "Upcoming Meetings" ? "from-amber-400 to-orange-300" : 
          "from-blue-400 to-cyan-300"
        } rounded-full flex items-center justify-center text-white shadow-sm`}>
          {icon}
        </div>
      </div>
    </GlassCard>
  );
}

// Performance Card component
function PerformanceCard({ monthlyQuota, achieved, topClient, pendingDocs, overdueFollowups }: PerformanceCardProps) {
  const percentage = (achieved / monthlyQuota) * 100;
  
  return (
    <GlassCard className="p-4 sm:p-6 transform transition hover:translate-y-[-4px]">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-indigo-800">Monthly Performance</h3>
        <span className="text-xs text-indigo-600 px-2 py-1 bg-indigo-50/70 backdrop-blur-sm rounded-lg">May 2023</span>
      </div>
      
      <div className="mb-6">
        <div className="flex justify-between mb-2">
          <span className="text-sm text-indigo-700 font-medium">Revenue Target</span>
          <span className="text-sm text-indigo-800">{Math.round(percentage)}% Achieved</span>
        </div>
        <div className="h-3 w-full bg-indigo-100/40 rounded-full overflow-hidden">
          <div 
            className={`h-full rounded-full ${
              percentage >= 80 ? 'bg-gradient-to-r from-green-500 to-emerald-400' : 
              percentage >= 50 ? 'bg-gradient-to-r from-blue-500 to-indigo-400' : 
              'bg-gradient-to-r from-amber-500 to-orange-400'
            }`}
            style={{ width: `${Math.min(100, percentage)}%` }}
          ></div>
        </div>
        <div className="flex justify-between mt-1 text-xs text-indigo-600">
          <span>₹0</span>
          <span>₹{(monthlyQuota / 100000).toFixed(1)}L</span>
        </div>
      </div>
      
      <hr className="border-indigo-100/30 my-4" />
      
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <UserCheck className="h-4 w-4 text-indigo-600 mr-2" />
            <span className="text-sm text-indigo-800">Top Client</span>
          </div>
          <span className="text-sm font-medium text-indigo-800">{topClient}</span>
        </div>
        
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <FileText className="h-4 w-4 text-indigo-600 mr-2" />
            <span className="text-sm text-indigo-800">Pending Documents</span>
          </div>
          <span className={`text-sm font-medium ${pendingDocs > 0 ? 'text-amber-600' : 'text-green-600'}`}>{pendingDocs}</span>
        </div>
        
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 text-indigo-600 mr-2" />
            <span className="text-sm text-indigo-800">Overdue Follow-ups</span>
          </div>
          <span className={`text-sm font-medium ${overdueFollowups > 0 ? 'text-red-600' : 'text-green-600'}`}>{overdueFollowups}</span>
        </div>
      </div>
    </GlassCard>
  );
}

// Function to get sentiment badge color
function getSentimentBadge(sentiment: string) {
  switch (sentiment) {
    case "Very Positive":
      return "bg-green-100/70 text-green-800";
    case "Positive":
      return "bg-emerald-100/70 text-emerald-800";
    case "Neutral":
      return "bg-blue-100/70 text-blue-800";
    case "Cautious":
      return "bg-amber-100/70 text-amber-800";
    case "Negative":
      return "bg-red-100/70 text-red-800";
    default:
      return "bg-gray-100/70 text-gray-800";
  }
}

// Function to format date
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString("en-US", {
    day: "numeric",
    month: "short"
  });
}

// Function to format time
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit"
  });
}

export default function SalesExecutiveDashboard() {
  const [selectedLead, setSelectedLead] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Function to get communication icon based on type
  const getCommunicationIcon = (type: string) => {
    switch (type) {
      case "Email":
        return <Mail className="h-4 w-4 text-blue-500" />;
      case "Call":
        return <Phone className="h-4 w-4 text-green-500" />;
      case "WhatsApp":
        return <MessageCircle className="h-4 w-4 text-emerald-500" />;
      default:
        return <MessageSquare className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <RoleBasedAccess allowedRoles={['admin', 'sales-manager', 'sales-executive']}>
      <div className="space-y-8">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 md:gap-0">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Sales Executive Dashboard
          </h1>
          <p className="text-gray-600 mt-1">Lead conversion and client relationship management</p>
        </div>
        
        <div className="flex items-center gap-4 self-end">
          <div className="relative">
            <button className="p-1.5 rounded-full bg-indigo-50/70 backdrop-blur-sm text-indigo-600 hover:bg-indigo-100/70 transition-colors">
              <Bell className="h-5 w-5" />
            </button>
            <span className="absolute top-0 right-0 h-2.5 w-2.5 rounded-full bg-gradient-to-r from-red-500 to-rose-600 border-2 border-white"></span>
          </div>
        </div>
      </div>
      
      {/* Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard 
          title="Active Leads" 
          value={mockPerformanceData.activeLeads}
          icon={<Users className="h-5 w-5 text-white" />}
          color="bg-gradient-to-br from-blue-400 to-indigo-300"
        />
        <StatCard 
          title="Conversion Rate" 
          value={`${mockPerformanceData.conversionRate}%`}
          icon={<BarChart2 className="h-5 w-5 text-white" />}
          color="bg-gradient-to-br from-green-400 to-emerald-300"
        />
        <StatCard 
          title="Upcoming Meetings" 
          value={mockPerformanceData.upcomingMeetings}
          icon={<Calendar className="h-5 w-5 text-white" />}
          color="bg-gradient-to-br from-amber-400 to-orange-300"
        />
        <StatCard 
          title="Avg. Response Time" 
          value={`${mockPerformanceData.averageResponseTime}h`}
          icon={<Clock className="h-5 w-5 text-white" />}
          color="bg-gradient-to-br from-blue-400 to-cyan-300"
        />
      </div>
      
      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        
        {/* Left column */}
        <div className="lg:col-span-2 space-y-6">
          {/* Lead Search and Filter */}
          <GlassCard className="p-4">
            <div className="flex flex-wrap gap-3">
              <div className="relative flex-1 min-w-0">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-indigo-400" size={18} />
                <input
                  type="text"
                  placeholder="Search leads by name, email or phone..."
                  className="pl-10 pr-4 py-2 border border-indigo-200/60 rounded-lg w-full bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="relative">
                <select className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <option>All Visa Types</option>
                  <option>EB-1</option>
                  <option>EB-2</option>
                  <option>O-1</option>
                  <option>H1-B</option>
                  <option>Student Visa</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
              
              <div className="relative">
                <select className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <option>All Stages</option>
                  <option>Initial Consultation</option>
                  <option>Document Collection</option>
                  <option>Application Review</option>
                  <option>Interview Preparation</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
            </div>
          </GlassCard>
          
          {/* Active Leads */}
          <GlassCard className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-indigo-800">Active Leads</h2>
              <button className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-lg py-1.5 px-3 text-xs font-medium hover:from-indigo-700 hover:to-purple-800 transition-all shadow-md flex items-center">
                View All Leads
                <ArrowUpRight className="ml-1 h-3.5 w-3.5" />
              </button>
            </div>
            
            <div className="space-y-4">
              {activeLeads.map((lead) => (
                <div 
                  key={lead.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all duration-300 ${
                    selectedLead === lead.id 
                      ? 'border-indigo-300 bg-indigo-50/70 backdrop-blur-sm transform scale-[1.02]' 
                      : 'border-indigo-100/30 bg-white/50 backdrop-blur-sm hover:border-indigo-200 hover:bg-white/80 hover:shadow-md'
                  }`}
                  onClick={() => setSelectedLead(lead.id === selectedLead ? null : lead.id)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h3 className="font-medium text-indigo-800">{lead.name}</h3>
                        <span className="ml-2 px-2 py-0.5 bg-gradient-to-r from-blue-600 to-indigo-700 text-white text-xs font-medium rounded-full">
                          {lead.visaType}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">{lead.email} • {lead.phone}</div>
                    </div>
                    <div className="flex flex-col items-end">
                      <div className="flex items-center">
                        <span className="text-sm font-medium mr-1 text-indigo-600">Lead Score:</span>
                        <span className={`px-2 py-0.5 rounded-full text-xs ${
                          lead.leadScore >= 80 ? 'bg-green-100/70 text-green-800' : 
                          lead.leadScore >= 60 ? 'bg-amber-100/70 text-amber-800' : 
                          'bg-red-100/70 text-red-800'
                        }`}>
                          {lead.leadScore}/100
                        </span>
                      </div>
                      <span className={`px-2 py-0.5 rounded-full text-xs mt-1 ${getSentimentBadge(lead.sentiment)}`}>
                        {lead.sentiment}
                      </span>
                    </div>
                  </div>
                  
                  <div className="mt-3 grid grid-cols-2 gap-2">
                    <div>
                      <div className="text-xs text-indigo-600 font-medium">Stage</div>
                      <div className="text-sm text-indigo-800">{lead.stage}</div>
                    </div>
                    <div>
                      <div className="text-xs text-indigo-600 font-medium">Last Contact</div>
                      <div className="text-sm text-indigo-800">
                        {formatDate(lead.lastContact)} at {formatTime(lead.lastContact)}
                      </div>
                    </div>
                  </div>
                  
                  {selectedLead === lead.id && (
                    <div className="mt-4 pt-4 border-t border-indigo-100/30">
                      <div className="text-sm font-medium mb-2 text-indigo-800">Pending Documents:</div>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {lead.pendingDocs.map((doc, index) => (
                          <span key={index} className="px-2 py-1 bg-amber-50/70 backdrop-blur-sm text-amber-800 rounded-lg text-xs flex items-center">
                            <FileText className="h-3 w-3 mr-1" />
                            {doc}
                          </span>
                        ))}
                      </div>
                      
                      <div className="text-sm font-medium mb-2 text-indigo-800">Notes:</div>
                      <p className="text-sm text-gray-600">{lead.notes}</p>
                      
                      <div className="flex flex-wrap justify-end mt-4 gap-2">
                        <button className="px-3 py-1.5 bg-white/80 backdrop-blur-sm border border-indigo-200/60 rounded-lg text-sm text-indigo-800 font-medium flex items-center hover:bg-indigo-50/70 transition-colors">
                          <Phone className="h-4 w-4 mr-1 text-indigo-600" />
                          Call
                        </button>
                        <button className="px-3 py-1.5 bg-white/80 backdrop-blur-sm border border-indigo-200/60 rounded-lg text-sm text-indigo-800 font-medium flex items-center hover:bg-indigo-50/70 transition-colors">
                          <Mail className="h-4 w-4 mr-1 text-indigo-600" />
                          Email
                        </button>
                        <Link href="/sales-executive/communications" className="px-3 py-1.5 bg-white/80 backdrop-blur-sm border border-indigo-200/60 rounded-lg text-sm text-indigo-800 font-medium flex items-center hover:bg-indigo-50/70 transition-colors">
                          <MessageCircle className="h-4 w-4 mr-1 text-indigo-600" />
                          Chat
                        </Link>
                        <button className="px-3 py-1.5 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Schedule
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </GlassCard>
          
          {/* Recent Communications */}
          <GlassCard className="p-6">
            <h2 className="text-lg font-semibold text-indigo-800 mb-4">Recent Communications</h2>
            
            <div className="space-y-3">
              {recentCommunications.map((comm) => (
                <div key={comm.id} className="p-3 border border-indigo-100/30 rounded-lg bg-white/50 backdrop-blur-sm">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-indigo-50/70 backdrop-blur-sm flex items-center justify-center mr-3">
                        {getCommunicationIcon(comm.type)}
                      </div>
                      <div>
                        <span className="font-medium text-indigo-800">{comm.client}</span>
                        <span className="text-xs text-indigo-600 ml-2 px-2 py-0.5 bg-indigo-50/70 backdrop-blur-sm rounded-full">{comm.type}</span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatDate(comm.timestamp)} at {formatTime(comm.timestamp)}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">{comm.summary}</p>
                  <div className="mt-1 flex justify-between items-center">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getSentimentBadge(comm.sentiment)}`}>
                      {comm.sentiment}
                    </span>
                    <button className="text-xs text-indigo-600 font-medium hover:text-indigo-800">View Details</button>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 flex justify-center">
              <Link href="/sales-executive/communications">
                <button className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 py-2 px-4 rounded-lg text-indigo-700 text-sm font-medium hover:bg-indigo-50/70 transition-colors">
                  View All Communications
                </button>
              </Link>
            </div>
          </GlassCard>
        </div>
        
        {/* Right column */}
        <div className="space-y-6">
          {/* Performance Card */}
          <PerformanceCard 
            monthlyQuota={mockPerformanceData.monthlyQuota}
            achieved={mockPerformanceData.achieved}
            topClient={mockPerformanceData.topClient}
            pendingDocs={mockPerformanceData.pendingDocs}
            overdueFollowups={mockPerformanceData.overdueFollowups}
          />
          
          {/* Upcoming Meetings */}
          <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-indigo-800">Upcoming Meetings</h2>
              <Link href="/sales-executive/calendar">
                <button className="text-xs text-indigo-700 font-medium flex items-center">
                  <Calendar className="h-3.5 w-3.5 mr-1" />
                  View Calendar
                </button>
              </Link>
            </div>
            
            <div className="space-y-3">
              {upcomingMeetings.map((meeting) => (
                <div key={meeting.id} className="p-3 border border-indigo-100/30 rounded-lg bg-white/50 backdrop-blur-sm">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="font-medium text-indigo-800">{meeting.client}</div>
                      <div className="text-sm text-gray-600">{meeting.purpose}</div>
                      
                      <div className="mt-2 flex items-center text-xs text-indigo-600">
                        <Clock className="h-3.5 w-3.5 mr-1" />
                        <span>{formatDate(meeting.scheduledTime)} at {formatTime(meeting.scheduledTime)}</span>
                        <span className="mx-1">•</span>
                        <span>{meeting.duration} mins</span>
                      </div>
                    </div>
                    
                    <div className={`px-2 py-1 text-xs font-medium rounded-full ${
                      meeting.mode === 'In-person' ? 'bg-indigo-100/70 text-indigo-800' : 
                      meeting.mode === 'Video Call' ? 'bg-emerald-100/70 text-emerald-800' : 
                      'bg-blue-100/70 text-blue-800'
                    }`}>
                      {meeting.mode}
                    </div>
                  </div>
                  
                  <div className="mt-2 flex justify-between items-center">
                    <span className="text-xs text-gray-500">{meeting.location}</span>
                    
                    <div className="flex space-x-2">
                      <button className="p-1 bg-indigo-50/70 backdrop-blur-sm rounded-lg text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                        <Calendar className="h-4 w-4" />
                      </button>
                      <button className="p-1 bg-indigo-50/70 backdrop-blur-sm rounded-lg text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                        <MessageSquare className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </GlassCard>
          
          {/* Visa Slots */}
          <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
            <h2 className="text-lg font-semibold text-indigo-800 mb-4">Available Visa Slots</h2>
            
            <div className="space-y-3">
              {visaSlots.map((slot, index) => (
                <div key={index} className="p-3 border border-indigo-100/30 rounded-lg bg-white/50 backdrop-blur-sm">
                  <div className="flex justify-between items-center mb-1">
                    <div className="font-medium text-indigo-800">{slot.visaType}</div>
                    <div className={`text-xs px-2 py-0.5 rounded-full ${
                      slot.slotsRemaining === 0 ? 'bg-red-100/70 text-red-800' : 
                      slot.slotsRemaining < 3 ? 'bg-amber-100/70 text-amber-800' : 
                      'bg-green-100/70 text-green-800'
                    }`}>
                      {slot.slotsRemaining > 0 ? `${slot.slotsRemaining} slots` : 'No slots'}
                    </div>
                  </div>
                  
                  <div className="flex items-center text-xs text-gray-600">
                    <Map className="h-3.5 w-3.5 mr-1 text-indigo-600" />
                    <span>{slot.consulate}</span>
                    <span className="mx-1">•</span>
                    <span>Next: {new Date(slot.nextAvailable).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          </GlassCard>
        </div>
      </div>
      </div>
    </RoleBasedAccess>
  );
}