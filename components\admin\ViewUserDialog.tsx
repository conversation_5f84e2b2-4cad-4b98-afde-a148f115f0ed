"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogD<PERSON><PERSON>,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/utils";

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
  status?: string;
  country?: string;
  phone?: string;
  applications?: number;
  lastLogin?: string | null;
}

interface ViewUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
}

export default function ViewUserDialog({ 
  open, 
  onOpenChange,
  user
}: ViewUserDialogProps) {
  if (!user) return null;

  const getStatusClass = (status: string | undefined) => {
    if (!status) return "bg-gray-100 text-gray-800";
    
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl">User Details</DialogTitle>
          <DialogDescription>
            Complete information about the selected user.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 rounded-full bg-indigo-100 flex items-center justify-center">
              <span className="text-xl font-medium text-indigo-800">
                {user.name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
            <div>
              <h3 className="text-lg font-medium">{user.name}</h3>
              <div className="text-sm text-gray-500">{user.email}</div>
              <div className="mt-1">
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(user.status)}`}>
                  {user.status || 'Unknown'}
                </span>
                <span className="ml-2 text-xs font-medium text-indigo-700 bg-indigo-50 px-2 py-1 rounded-full">
                  {user.role}
                </span>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 border-t pt-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500">Phone</h4>
              <p className="mt-1">{user.phone || 'Not provided'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">Country</h4>
              <p className="mt-1">{user.country || 'Not specified'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">Member Since</h4>
              <p className="mt-1">{formatDate(user.createdAt)}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">Last Login</h4>
              <p className="mt-1">{user.lastLogin ? formatDate(user.lastLogin) : 'Never'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">Applications</h4>
              <p className="mt-1">{user.applications !== undefined ? user.applications : 'N/A'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">Last Updated</h4>
              <p className="mt-1">{formatDate(user.updatedAt)}</p>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 