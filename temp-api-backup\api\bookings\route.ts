import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

const bookingSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  country: z.string(),
  visaType: z.string(),
  date: z.string(),
  time: z.string(),
  notes: z.string().optional(),
})

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const validatedData = bookingSchema.parse(body)

    const booking = await prisma.booking.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        phone: validatedData.phone,
        country: validatedData.country,
        visaType: validatedData.visaType,
        date: new Date(validatedData.date),
        time: validatedData.time,
        notes: validatedData.notes,
        status: "PENDING",
      },
    })

    return NextResponse.json(booking, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Booking creation error:", error)
    return NextResponse.json(
      { error: "Failed to create booking" },
      { status: 500 }
    )
  }
}

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url)
  const email = searchParams.get("email")
  const userId = searchParams.get("userId")
  let where: any = {}
  if (email) where.email = email
  if (userId) where.userId = userId
  const bookings = await prisma.booking.findMany({
    where,
    orderBy: { date: "asc" },
  })
  return NextResponse.json(bookings)
} 