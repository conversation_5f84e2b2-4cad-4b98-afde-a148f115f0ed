import React, { useState } from 'react';
import { 
  Video, 
  Play, 
  Download, 
  CheckSquare, 
  CalendarDays, 
  VideoOff, 
  PieChart, 
  DollarSign,
  Globe,
  Compass,
  Search,
  School,
  UserCheck,
  ChevronRight,
  MessageSquare,
  Send,
  Award,
  FileText,
  ArrowRight
} from 'lucide-react';

export default function ClientEducation() {
  const [activeTab, setActiveTab] = useState("interview");
  const [searchQuery, setSearchQuery] = useState("");
  
  // Filter resources based on search
  const filteredResources = (type: string) => {
    if (type === "interview") {
      return interviewSimulators.filter(resource => 
        resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.country.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.visaType.toLowerCase().includes(searchQuery.toLowerCase())
      );
    } else if (type === "cultural") {
      return culturalVideos.filter(resource =>
        resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.country.toLowerCase().includes(searchQuery.toLowerCase())
      );
    } else {
      return financialTools.filter(resource =>
        resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
  };
  
  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-sm p-4">
        <div className="flex flex-wrap gap-2">
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              activeTab === "interview" 
                ? "bg-indigo-100 text-indigo-700" 
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            onClick={() => setActiveTab("interview")}
          >
            <div className="flex items-center">
              <Video className="h-4 w-4 mr-2" />
              Interview Simulators
            </div>
          </button>
          
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              activeTab === "cultural" 
                ? "bg-indigo-100 text-indigo-700" 
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            onClick={() => setActiveTab("cultural")}
          >
            <div className="flex items-center">
              <Globe className="h-4 w-4 mr-2" />
              Cultural Orientation
            </div>
          </button>
          
          <button
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              activeTab === "financial" 
                ? "bg-indigo-100 text-indigo-700" 
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            onClick={() => setActiveTab("financial")}
          >
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              Financial Planning
            </div>
          </button>
        </div>
      </div>
      
      {/* Search Bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md"
          placeholder={`Search ${
            activeTab === "interview" ? "interview simulations" : 
            activeTab === "cultural" ? "cultural orientation videos" : 
            "financial planning tools"
          }...`}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      
      {/* Content based on active tab */}
      {activeTab === "interview" && (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Interactive Visa Interview Simulators</h3>
            <p className="text-sm text-gray-600 mb-6">
              Practice for visa interviews with interactive simulations of common questions and scenarios
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredResources("interview").map((simulator, index) => (
                <div 
                  key={index} 
                  className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                >
                  <div className="relative h-40 bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Play className="h-12 w-12 text-white opacity-80" />
                    </div>
                    <div className="absolute top-3 left-3 px-2 py-1 bg-white/80 rounded text-xs font-medium">
                      {simulator.duration} min
                    </div>
                    <div className="absolute top-3 right-3 px-2 py-1 bg-white/80 rounded text-xs font-medium">
                      {simulator.difficulty}
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs font-medium">
                          {simulator.visaType}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500 flex items-center">
                        <Globe className="h-3 w-3 mr-1" />
                        {simulator.country}
                      </span>
                    </div>
                    
                    <h4 className="font-medium mb-1">{simulator.title}</h4>
                    <p className="text-sm text-gray-600 mb-2">{simulator.description}</p>
                    
                    <div className="flex justify-between items-center mt-3">
                      <div className="flex items-center text-xs text-gray-500">
                        <UserCheck className="h-4 w-4 mr-1" />
                        {simulator.completions} completions
                      </div>
                      <button className="text-indigo-600 hover:text-indigo-800 font-medium text-sm">
                        Start Simulation
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredResources("interview").length === 0 && (
              <div className="text-center py-10 text-gray-500">
                <VideoOff className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                <p>No interview simulators found matching your search</p>
                <p className="text-sm">Try adjusting your search terms</p>
              </div>
            )}
            
            {filteredResources("interview").length > 0 && (
              <div className="mt-6 text-center">
                <button className="text-indigo-600 hover:text-indigo-800 font-medium flex items-center mx-auto">
                  View All Interview Simulators
                  <ChevronRight className="h-4 w-4 ml-1" />
                </button>
              </div>
            )}
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Interview Preparation Material</h3>
            
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4 flex items-start">
                <div className="p-2 bg-blue-100 text-blue-600 rounded-lg mr-3">
                  <FileText className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium mb-1">Common EB-1 Interview Questions</h4>
                  <p className="text-sm text-gray-600">Comprehensive list of 50+ frequently asked questions for Extraordinary Ability visas</p>
                  <div className="flex justify-between items-center mt-3">
                    <span className="text-xs text-gray-500">PDF • 12 pages</span>
                    <button className="text-indigo-600 hover:text-indigo-800 font-medium text-sm flex items-center">
                      Download
                      <Download className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4 flex items-start">
                <div className="p-2 bg-indigo-100 text-indigo-600 rounded-lg mr-3">
                  <Video className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium mb-1">Expert Interview Tips: Student Visas</h4>
                  <p className="text-sm text-gray-600">Video guide with tips from former consular officers on student visa interviews</p>
                  <div className="flex justify-between items-center mt-3">
                    <span className="text-xs text-gray-500">Video • 35 min</span>
                    <button className="text-indigo-600 hover:text-indigo-800 font-medium text-sm flex items-center">
                      Watch Now
                      <Play className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4 flex items-start">
                <div className="p-2 bg-green-100 text-green-600 rounded-lg mr-3">
                  <CheckSquare className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium mb-1">Interview Preparation Checklist</h4>
                  <p className="text-sm text-gray-600">Step-by-step checklist to ensure you're fully prepared for any visa interview</p>
                  <div className="flex justify-between items-center mt-3">
                    <span className="text-xs text-gray-500">Interactive Guide</span>
                    <button className="text-indigo-600 hover:text-indigo-800 font-medium text-sm flex items-center">
                      Open Checklist
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {activeTab === "cultural" && (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Country-Specific Cultural Orientation</h3>
            <p className="text-sm text-gray-600 mb-6">
              Learn about cultural norms, business etiquette, and daily life in your destination country
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredResources("cultural").map((video, index) => (
                <div 
                  key={index} 
                  className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                >
                  <div 
                    className="relative h-40 bg-cover bg-center" 
                    style={{ 
                      backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url(${video.thumbnail || '/images/placeholder-cultural.jpg'})` 
                    }}
                  >
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="bg-white/20 backdrop-blur-sm p-3 rounded-full">
                        <Play className="h-8 w-8 text-white" />
                      </div>
                    </div>
                    <div className="absolute bottom-3 left-3 text-white text-sm font-medium">
                      {video.duration} min
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                        {video.category}
                      </span>
                      <span className="text-xs text-gray-500 flex items-center">
                        <Globe className="h-3 w-3 mr-1" />
                        {video.country}
                      </span>
                    </div>
                    
                    <h4 className="font-medium mb-1">{video.title}</h4>
                    <p className="text-sm text-gray-600 mb-2">{video.description}</p>
                    
                    <div className="flex justify-between items-center mt-3">
                      <div className="flex items-center text-xs text-gray-500">
                        <School className="h-4 w-4 mr-1" />
                        {video.viewers} viewers
                      </div>
                      <button className="text-indigo-600 hover:text-indigo-800 font-medium text-sm">
                        Watch Video
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredResources("cultural").length === 0 && (
              <div className="text-center py-10 text-gray-500">
                <VideoOff className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                <p>No cultural orientation videos found matching your search</p>
                <p className="text-sm">Try adjusting your search terms</p>
              </div>
            )}
            
            {filteredResources("cultural").length > 0 && (
              <div className="mt-6 text-center">
                <button className="text-indigo-600 hover:text-indigo-800 font-medium flex items-center mx-auto">
                  View All Cultural Orientation Videos
                  <ChevronRight className="h-4 w-4 ml-1" />
                </button>
              </div>
            )}
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">One-on-One Cultural Coaching</h3>
            
            <div className="border border-indigo-100 bg-indigo-50 rounded-lg p-5">
              <div className="flex items-start">
                <div className="p-3 bg-indigo-600 text-white rounded-lg mr-4">
                  <MessageSquare className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-lg mb-1">Book a Cultural Orientation Session</h4>
                  <p className="text-gray-600 mb-3">
                    Schedule a personalized one-on-one session with a cultural expert who has lived in your destination country.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div className="flex items-center">
                      <div className="mr-3 p-2 bg-indigo-100 rounded-full">
                        <CalendarDays className="h-5 w-5 text-indigo-600" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">Flexible Scheduling</p>
                        <p className="text-xs text-gray-500">30 or 60 minute sessions</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <div className="mr-3 p-2 bg-indigo-100 rounded-full">
                        <Compass className="h-5 w-5 text-indigo-600" />
                      </div>
                      <div>
                        <p className="font-medium text-sm">Expert Guidance</p>
                        <p className="text-xs text-gray-500">Local cultural experts</p>
                      </div>
                    </div>
                  </div>
                  
                  <button className="w-full mt-4 bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 rounded-md flex items-center justify-center">
                    <CalendarDays className="h-4 w-4 mr-2" />
                    Schedule Session
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {activeTab === "financial" && (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Financial Planning Calculators</h3>
            <p className="text-sm text-gray-600 mb-6">
              Interactive tools to help clients prepare financial documentation and plan for expenses
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredResources("financial").map((tool, index) => (
                <div 
                  key={index} 
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start">
                    <div className={`p-3 rounded-lg mr-3 ${tool.iconBg} ${tool.iconColor}`}>
                      {tool.icon === "dollar" ? (
                        <DollarSign className="h-6 w-6" />
                      ) : tool.icon === "chart" ? (
                        <PieChart className="h-6 w-6" />
                      ) : (
                        <CalendarDays className="h-6 w-6" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{tool.title}</h4>
                      <p className="text-sm text-gray-600 mt-1 mb-3">{tool.description}</p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs font-medium">
                            {tool.type}
                          </span>
                        </div>
                        <button className="text-indigo-600 hover:text-indigo-800 font-medium text-sm">
                          Open Calculator
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredResources("financial").length === 0 && (
              <div className="text-center py-10 text-gray-500">
                <DollarSign className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                <p>No financial planning tools found matching your search</p>
                <p className="text-sm">Try adjusting your search terms</p>
              </div>
            )}
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Country-Specific Financial Guides</h3>
            
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 text-blue-600 rounded mr-3">
                      <Award className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">US Student Visa: Financial Documentation Guide</h4>
                      <p className="text-sm text-gray-600">Comprehensive guide to preparing financial documents for F-1 student visas</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <button className="text-indigo-600 hover:text-indigo-800 text-sm">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 text-blue-600 rounded mr-3">
                      <Award className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">UK Tier 2 Visa: Proof of Funds Guide</h4>
                      <p className="text-sm text-gray-600">Guidelines for meeting the financial requirements for UK work visas</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <button className="text-indigo-600 hover:text-indigo-800 text-sm">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 text-blue-600 rounded mr-3">
                      <Award className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">Canada Study Permit: Financial Requirements</h4>
                      <p className="text-sm text-gray-600">Detailed breakdown of financial requirements for Canadian study permits</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <button className="text-indigo-600 hover:text-indigo-800 text-sm">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 text-center">
              <button className="text-indigo-600 hover:text-indigo-800 font-medium flex items-center mx-auto">
                View All Financial Guides
                <ChevronRight className="h-4 w-4 ml-1" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Mock data
const interviewSimulators = [
  {
    title: "F-1 Student Visa Interview Simulator",
    description: "Practice answering common questions for US student visa interviews",
    visaType: "F-1 Student",
    country: "United States",
    duration: 15,
    difficulty: "Beginner",
    completions: 1243
  },
  {
    title: "UK Tier 4 Student Visa Interview",
    description: "Interactive simulation for UK university admission interviews",
    visaType: "Tier 4 Student",
    country: "United Kingdom",
    duration: 20,
    difficulty: "Intermediate",
    completions: 892
  },
  {
    title: "H1-B Technical Professional Interview",
    description: "Specialized practice for IT professionals applying for H1-B visas",
    visaType: "H1-B",
    country: "United States",
    duration: 25,
    difficulty: "Advanced",
    completions: 756
  },
  {
    title: "EB-1 Extraordinary Ability Interview",
    description: "Preparation for EB-1 interviews with focus on achievement evidence",
    visaType: "EB-1",
    country: "United States",
    duration: 30,
    difficulty: "Advanced",
    completions: 412
  },
  {
    title: "Canada Study Permit Interview",
    description: "Practice answering questions about your study plans in Canada",
    visaType: "Study Permit",
    country: "Canada",
    duration: 18,
    difficulty: "Intermediate",
    completions: 678
  },
  {
    title: "Australia Student Visa Interview",
    description: "Interactive preparation for Australian student visa interviews",
    visaType: "Student Visa",
    country: "Australia",
    duration: 20,
    difficulty: "Beginner",
    completions: 523
  }
];

const culturalVideos = [
  {
    title: "US Business Etiquette Guide",
    description: "Learn about professional workplace norms in the United States",
    category: "Business Culture",
    country: "United States",
    duration: 12,
    thumbnail: "",
    viewers: 2345
  },
  {
    title: "Living in London: A Newcomer's Guide",
    description: "Essential information for settling in the UK capital",
    category: "Daily Life",
    country: "United Kingdom",
    duration: 18,
    thumbnail: "",
    viewers: 1876
  },
  {
    title: "Understanding Canadian Education System",
    description: "Overview of academic expectations for international students",
    category: "Education",
    country: "Canada",
    duration: 15,
    thumbnail: "",
    viewers: 1543
  },
  {
    title: "Silicon Valley Tech Culture",
    description: "Insights into the unique work culture of tech companies",
    category: "Business Culture",
    country: "United States",
    duration: 20,
    thumbnail: "",
    viewers: 2156
  },
  {
    title: "German Workplace Communication",
    description: "Learn direct communication styles in German professional settings",
    category: "Business Culture",
    country: "Germany",
    duration: 14,
    thumbnail: "",
    viewers: 1243
  },
  {
    title: "Australian Social Norms and Customs",
    description: "Understanding social interactions and customs in Australia",
    category: "Social Norms",
    country: "Australia",
    duration: 17,
    thumbnail: "",
    viewers: 967
  }
];

const financialTools = [
  {
    title: "Proof of Funds Calculator",
    description: "Calculate the minimum funds required for different visa types and countries",
    type: "Calculator",
    icon: "dollar",
    iconBg: "bg-green-100",
    iconColor: "text-green-600"
  },
  {
    title: "Cost of Living Estimator",
    description: "Estimate monthly expenses based on location and lifestyle",
    type: "Interactive Tool",
    icon: "chart",
    iconBg: "bg-blue-100",
    iconColor: "text-blue-600"
  },
  {
    title: "Student Budget Planner",
    description: "Create a comprehensive budget plan for studying abroad",
    type: "Planner",
    icon: "calendar",
    iconBg: "bg-indigo-100",
    iconColor: "text-indigo-600"
  },
  {
    title: "Currency Converter & Trends",
    description: "Calculate currency equivalents with historical trend analysis",
    type: "Calculator",
    icon: "dollar",
    iconBg: "bg-purple-100",
    iconColor: "text-purple-600"
  }
]; 