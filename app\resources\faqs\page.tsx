"use client";

import { useState, ReactNode } from "react";
import Link from "next/link";
import { ChevronDown, ChevronUp, Search } from "lucide-react";

interface FAQItemProps {
  question: string;
  answer: string | ReactNode;
  isOpen: boolean;
  onClick: () => void;
  category: string;
}

function FAQItem({ question, answer, isOpen, onClick, category }: FAQItemProps) {
  return (
    <div className="border border-indigo-100 rounded-lg mb-4 overflow-hidden shadow-sm">
      <button 
        className={`w-full text-left px-6 py-4 flex items-center justify-between ${
          isOpen ? "bg-indigo-50" : "bg-white"
        }`}
        onClick={onClick}
      >
        <div>
          <span className="text-xs font-medium text-indigo-600 mb-1 block">{category}</span>
          <h3 className="font-medium text-indigo-900">{question}</h3>
        </div>
        {isOpen ? (
          <ChevronUp className="flex-shrink-0 h-5 w-5 text-indigo-500" />
        ) : (
          <ChevronDown className="flex-shrink-0 h-5 w-5 text-indigo-500" />
        )}
      </button>
      
      {isOpen && (
        <div className="px-6 py-4 bg-white">
          <div className="text-gray-600 prose prose-sm max-w-none">
            {typeof answer === "string" ? (
              <p>{answer}</p>
            ) : (
              answer
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default function FAQsPage() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  
  const faqs = [
    {
      question: "What documents do I need for a student visa application?",
      answer: (
        <>
          <p>Generally, you will need the following documents for a student visa application:</p>
          <ul className="list-disc pl-5 mt-2">
            <li>Valid passport with at least 6 months validity beyond your intended stay</li>
            <li>Acceptance letter from an accredited educational institution</li>
            <li>Proof of financial resources to cover tuition and living expenses</li>
            <li>Completed visa application forms</li>
            <li>Passport-sized photographs meeting visa specifications</li>
            <li>Academic transcripts and certificates from previous education</li>
            <li>Proof of English language proficiency (IELTS, TOEFL, etc.)</li>
            <li>Statement of purpose or study plan</li>
          </ul>
          <p className="mt-2">Additional documents may be required depending on the country and your specific circumstances.</p>
        </>
      ),
      category: "Student Visas"
    },
    {
      question: "How long does it take to process a work visa?",
      answer: "Work visa processing times vary greatly depending on the country, visa type, and your nationality. Generally, work visas can take anywhere from 2 weeks to 6 months to process. Premium processing options may be available in some countries for an additional fee. We recommend applying at least 3 months before your intended start date to allow for any unexpected delays.",
      category: "Work Visas"
    },
    {
      question: "Can I apply for a visa without a job offer?",
      answer: "Yes, many countries offer visa options that don't require a job offer, such as skilled migration visas, entrepreneur visas, and investment visas. Countries like Canada (Express Entry), Australia (Skilled Independent Visa), and New Zealand (Skilled Migrant Category) operate points-based systems that allow qualified individuals to immigrate without pre-arranged employment.",
      category: "Work Visas"
    },
    {
      question: "What happens if my visa application gets rejected?",
      answer: "If your visa application is rejected, you'll typically receive a letter explaining the reasons for the rejection. Depending on the country and visa type, you may have the right to appeal the decision, reapply with additional documentation, or apply for a different visa category. Before reapplying, it's crucial to address the specific reasons for rejection. Our advisors can help review your case and suggest the best course of action.",
      category: "General"
    },
    {
      question: "Can I work while studying on a student visa?",
      answer: "Most student visas allow for some form of part-time work, but restrictions vary by country. For example, the US allows F-1 students to work up to 20 hours per week on-campus during the academic year. Canada permits up to 20 hours per week off-campus during academic sessions. The UK allows up to 20 hours per week during term time. Australia allows up to 40 hours per fortnight. Always check the specific regulations for your destination country.",
      category: "Student Visas"
    },
    {
      question: "What is the difference between a visa and a residence permit?",
      answer: "A visa is typically an entry document that allows you to enter a country for a specific purpose (tourism, study, work, etc.). A residence permit (or residence card) is a document that allows you to legally reside in the country for a longer period. In many cases, you'll first obtain a visa to enter the country, then apply for or receive a residence permit after arrival for your authorized stay. Some countries combine these into a single process.",
      category: "General"
    },
    {
      question: "How much money do I need to show for a visa application?",
      answer: "The financial requirements vary by country and visa type. For student visas, you typically need to show funds to cover tuition fees plus living expenses for one year (ranges from $10,000-$25,000 USD depending on the country and city). For settlement or permanent residency visas, countries may require proof of settlement funds (e.g., Canada requires $13,000-$34,000 CAD depending on family size). Work visas may have lower or no specific financial requirements if you have a job offer.",
      category: "Financial Requirements"
    },
    {
      question: "Can my family join me on my visa?",
      answer: "Many work, study, and permanent residency visas allow you to include family members (typically spouse/partner and dependent children) as part of your application or as subsequent applicants. They would usually receive dependent visas tied to your primary visa. Eligibility, application processes, and rights granted to dependents vary by country and visa type. Some visas may have minimum income requirements to sponsor family members.",
      category: "Family Visas"
    },
  ];
  
  // Get unique categories for the filter
  const categories = Array.from(new Set(faqs.map(faq => faq.category)));
  
  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  
  const filteredFaqs = faqs.filter(faq => {
    const matchesSearch = searchQuery === "" || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (typeof faq.answer === "string" && faq.answer.toLowerCase().includes(searchQuery.toLowerCase()));
      
    const matchesCategory = selectedCategory === null || faq.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div>
      <h2 className="text-xl font-semibold text-indigo-800 mb-6">Frequently Asked Questions</h2>
      
      <p className="text-gray-600 mb-8">
        Find answers to common questions about visa applications, immigration processes,
        and requirements. Can't find what you're looking for? Contact our visa experts for
        personalized assistance.
      </p>
      
      {/* Search and Filter */}
      <div className="mb-8 space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search questions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
        
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory(null)}
            className={`px-3 py-1 rounded-full text-sm ${
              selectedCategory === null
                ? "bg-indigo-100 text-indigo-800"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
          >
            All Categories
          </button>
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 rounded-full text-sm ${
                selectedCategory === category
                  ? "bg-indigo-100 text-indigo-800"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>
      
      {/* FAQ Items */}
      {filteredFaqs.length > 0 ? (
        <div>
          {filteredFaqs.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isOpen={openIndex === index}
              onClick={() => toggleFAQ(index)}
              category={faq.category}
            />
          ))}
        </div>
      ) : (
        <div className="text-center p-8 bg-gray-50 rounded-lg">
          <p className="text-gray-600 mb-4">No FAQs match your search criteria</p>
          <button 
            onClick={() => {
              setSearchQuery("");
              setSelectedCategory(null);
            }}
            className="text-indigo-600 font-medium"
          >
            Clear filters
          </button>
        </div>
      )}
      
      {/* Contact Section */}
      <div className="mt-12 p-6 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 border border-indigo-100 flex items-center justify-between flex-col md:flex-row gap-4">
        <div>
          <h3 className="text-lg font-semibold text-indigo-800 mb-2">Need More Help?</h3>
          <p className="text-gray-600">
            Our immigration experts are ready to answer your specific questions
          </p>
        </div>
        <Link href="/contact" className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-6 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md whitespace-nowrap">
          Contact Us
        </Link>
      </div>
    </div>
  );
} 