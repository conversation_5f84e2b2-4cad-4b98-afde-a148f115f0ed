"use client";

import { useState } from "react";
import { Globe, Plug, ToggleLeft, ExternalLink, RefreshCw, KeyRound } from "lucide-react";

// Import our reusable components
import SettingsHeader from "@/app/components/admin/SettingsHeader";
import SettingsSection from "@/app/components/admin/SettingsSection";
import Form<PERSON>ield from "@/app/components/admin/FormField";
import SaveButton from "@/app/components/admin/SaveButton";
import SuccessMessage from "@/app/components/admin/SuccessMessage";
import TabNav from "@/app/components/admin/TabNav";

// Mock integrations data
const mockIntegrations = [
  {
    id: "payment-stripe",
    name: "<PERSON><PERSON>",
    description: "Process credit card payments securely",
    category: "payment",
    connected: true,
    icon: "💳"
  },
  {
    id: "payment-paypal",
    name: "PayPal",
    description: "Accept PayPal payments",
    category: "payment",
    connected: false,
    icon: "💰"
  },
  {
    id: "email-sendgrid",
    name: "Send<PERSON>rid",
    description: "Send transactional emails",
    category: "email",
    connected: true,
    icon: "📧"
  },
  {
    id: "email-mailchimp",
    name: "Mailchimp",
    description: "Marketing email campaigns",
    category: "email",
    connected: false,
    icon: "📨"
  },
  {
    id: "crm-salesforce",
    name: "Salesforce",
    description: "Customer relationship management",
    category: "crm",
    connected: false,
    icon: "👥"
  },
  {
    id: "auth-google",
    name: "Google",
    description: "Login with Google accounts",
    category: "auth",
    connected: true,
    icon: "🔑"
  },
  {
    id: "auth-facebook",
    name: "Facebook",
    description: "Login with Facebook accounts",
    category: "auth",
    connected: false,
    icon: "🔑"
  }
];

export default function IntegrationsPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const [integrations, setIntegrations] = useState(mockIntegrations);
  
  // Save settings
  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  // Toggle integration connection status
  const toggleConnection = (id: string) => {
    setIntegrations(prev => 
      prev.map(item => 
        item.id === id ? { ...item, connected: !item.connected } : item
      )
    );
  };

  // Filter integrations by category
  const filteredIntegrations = activeTab === "all" 
    ? integrations 
    : integrations.filter(item => item.category === activeTab);

  // Define tabs for the tab navigation
  const tabs = [
    { id: "all", label: "All Integrations" },
    { id: "payment", label: "Payment Gateways" },
    { id: "email", label: "Email Services" },
    { id: "crm", label: "CRM & Data" },
    { id: "auth", label: "Authentication" }
  ];

  return (
    <div>
      {/* Header */}
      <SettingsHeader 
        title="Integrations" 
        description="Connect third-party services, APIs, and external systems"
        actions={
          <SaveButton isLoading={isSaving} onClick={saveSettings} />
        }
      />
      
      {/* Success Message */}
      {showSuccess && (
        <SuccessMessage message="Integration settings have been successfully saved." />
      )}
      
      {/* Tabs */}
      <TabNav tabs={tabs} activeTab={activeTab} onChange={setActiveTab} />
      
      {/* API Keys Section */}
      <SettingsSection 
        title="API Configuration" 
        description="Manage your application's API keys and authentication credentials"
      >
        <FormField 
          label="API Key" 
          helper="Your application's API key for external services"
        >
          <div className="flex items-center">
            <input 
              type="password" 
              className="flex-1 p-2 border border-gray-300 rounded-md mr-2" 
              value="sk_test_51HZgsKJHxK8kQgTYiM1cwHGZjjLBJGZkTDwzXZPbWkQtsdpvFwuf9KHrfsKsgAK7t4WXtP9XNkQV3gXnZhNqs3iB00vy9QZdRh"
              readOnly
            />
            <button className="bg-gray-100 text-gray-600 px-3 py-2 rounded hover:bg-gray-200">
              Copy
            </button>
          </div>
        </FormField>
        
        <FormField 
          label="Webhook URL" 
          helper="Used by external services to communicate with your application"
        >
          <div className="flex items-center">
            <input 
              type="text" 
              className="flex-1 p-2 border border-gray-300 rounded-md mr-2" 
              value="https://yourdomain.com/api/webhooks" 
              readOnly
            />
            <button className="bg-gray-100 text-gray-600 px-3 py-2 rounded hover:bg-gray-200">
              Copy
            </button>
          </div>
        </FormField>
        
        <div className="mt-4">
          <button className="bg-indigo-50 text-indigo-600 px-4 py-2 rounded-md flex items-center gap-2 hover:bg-indigo-100">
            <RefreshCw className="h-4 w-4" />
            Regenerate API Keys
          </button>
        </div>
      </SettingsSection>
      
      {/* Integrations List */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
        {filteredIntegrations.map((integration) => (
          <div 
            key={integration.id} 
            className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 flex justify-between items-start"
          >
            <div className="flex">
              <div className="w-10 h-10 rounded-lg bg-indigo-100 flex items-center justify-center text-xl mr-4">
                {integration.icon}
              </div>
              <div>
                <h3 className="text-lg font-semibold">{integration.name}</h3>
                <p className="text-sm text-gray-500">{integration.description}</p>
                <div className="mt-2">
                  {integration.connected ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Connected
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Not Connected
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex flex-col items-end gap-2">
              <button
                onClick={() => toggleConnection(integration.id)}
                className={`px-3 py-1 rounded text-sm flex items-center gap-1 ${
                  integration.connected 
                    ? "bg-red-50 text-red-600 hover:bg-red-100"
                    : "bg-green-50 text-green-600 hover:bg-green-100"
                }`}
              >
                {integration.connected ? "Disconnect" : "Connect"}
              </button>
              
              <button className="text-indigo-600 text-sm flex items-center hover:underline">
                <span>Configure</span>
                <ExternalLink className="h-3 w-3 ml-1" />
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {/* OAuth Configuration */}
      <SettingsSection 
        title="OAuth Configuration" 
        description="Configure OAuth settings for social login providers"
        className="mt-6"
      >
        <FormField 
          label="Google OAuth" 
          helper="Configuration for Google authentication"
        >
          <div className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <input 
                type="text" 
                placeholder="Client ID" 
                className="p-2 border border-gray-300 rounded-md" 
              />
              <input 
                type="password" 
                placeholder="Client Secret" 
                className="p-2 border border-gray-300 rounded-md" 
              />
            </div>
            <input 
              type="text" 
              placeholder="Redirect URI" 
              className="w-full p-2 border border-gray-300 rounded-md" 
              value="https://yourdomain.com/auth/google/callback"
            />
          </div>
        </FormField>
        
        <FormField 
          label="Facebook OAuth" 
          helper="Configuration for Facebook authentication"
        >
          <div className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <input 
                type="text" 
                placeholder="App ID" 
                className="p-2 border border-gray-300 rounded-md" 
              />
              <input 
                type="password" 
                placeholder="App Secret" 
                className="p-2 border border-gray-300 rounded-md" 
              />
            </div>
            <input 
              type="text" 
              placeholder="Redirect URI" 
              className="w-full p-2 border border-gray-300 rounded-md" 
              value="https://yourdomain.com/auth/facebook/callback"
            />
          </div>
        </FormField>
      </SettingsSection>
    </div>
  );
} 