import React, { useState } from 'react';
import { 
  FileText, 
  Download, 
  Upload, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Check,
  X,
  File,
  FileCheck,
  Search,
  History,
  Shield
} from 'lucide-react';

export default function DocumentWorkflow() {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Document Templates</h3>
        <p className="text-sm text-gray-600 mb-6">
          Generate standardized documents with auto-populated client information
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {documentTemplates.map((template) => (
            <div 
              key={template.id}
              className={`border rounded-lg p-4 cursor-pointer transition-colors
                ${selectedTemplate === template.id 
                  ? 'border-indigo-500 bg-indigo-50' 
                  : 'border-gray-200 hover:border-indigo-300 hover:bg-indigo-50/30'
                }`}
              onClick={() => setSelectedTemplate(template.id)}
            >
              <div className="flex items-start">
                <div className={`p-2 rounded-lg mr-3 ${getTemplateIconColor(template.type)}`}>
                  <FileText className="h-6 w-6" />
                </div>
                <div>
                  <h4 className="font-medium text-sm">{template.name}</h4>
                  <p className="text-xs text-gray-500 mt-1">{template.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-end">
          <button 
            className={`px-4 py-2 rounded-md text-white font-medium ${
              selectedTemplate ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-gray-300 cursor-not-allowed'
            }`}
            disabled={!selectedTemplate}
          >
            Generate Document
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Plagiarism Checker */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Plagiarism Checker</h3>
            <div className="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded">
              Turnitin API
            </div>
          </div>
          
          <p className="text-sm text-gray-600 mb-6">
            Verify document originality and prevent potential fraud by checking against Turnitin database
          </p>
          
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center mb-6">
            <div className="mb-3">
              <div className="inline-flex p-3 rounded-full bg-indigo-100 text-indigo-600 mb-2">
                <Upload className="h-6 w-6" />
              </div>
              <h4 className="font-medium">Upload Document</h4>
              <p className="text-sm text-gray-500 mt-1">
                Drag and drop or click to upload a document
              </p>
            </div>
            <input type="file" className="hidden" id="plagiarism-file" />
            <button 
              className="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium"
              onClick={() => document.getElementById('plagiarism-file')?.click()}
            >
              Browse Files
            </button>
          </div>
          
          <div className="flex justify-end">
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-md font-medium">
              Check for Plagiarism
            </button>
          </div>
        </div>
        
        {/* Version Control */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">Version Control</h3>
          <p className="text-sm text-gray-600 mb-6">
            Track document changes, compare versions, and maintain an audit trail with digital signatures
          </p>
          
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm mb-4"
              placeholder="Search documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="space-y-3 mb-6">
            {documentVersions.map((doc, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-3">
                <div className="flex justify-between items-start">
                  <div className="flex items-start">
                    <File className="h-5 w-5 text-gray-500 mt-1 mr-2" />
                    <div>
                      <h4 className="font-medium text-sm">{doc.name}</h4>
                      <div className="flex items-center mt-1">
                        <span className="text-xs text-gray-500">Version {doc.version}</span>
                        <span className="mx-2 text-gray-300">•</span>
                        <span className="text-xs text-gray-500">{doc.date}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {doc.signed && (
                      <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded flex items-center">
                        <Shield className="h-3 w-3 mr-1" />
                        Signed
                      </div>
                    )}
                    <button className="p-1 text-gray-500 hover:text-gray-700">
                      <History className="h-4 w-4" />
                    </button>
                    <button className="p-1 text-gray-500 hover:text-gray-700">
                      <Download className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex justify-end">
            <button className="px-4 py-2 bg-indigo-600 text-white rounded-md font-medium">
              Upload New Version
            </button>
          </div>
        </div>
      </div>
      
      {/* Document Status Overview */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Document Status Overview</h3>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Document
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Verified
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {documentStatuses.map((doc, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg mr-2 ${getDocumentTypeColor(doc.type)}`}>
                        <FileText className="h-4 w-4" />
                      </div>
                      <div className="text-sm font-medium text-gray-900">{doc.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {doc.client}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {doc.createdDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(doc.status)}`}>
                      {doc.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {doc.verified ? (
                      <div className="text-green-600 flex items-center">
                        <Check className="h-4 w-4 mr-1" />
                        Yes
                      </div>
                    ) : (
                      <div className="text-gray-500 flex items-center">
                        <X className="h-4 w-4 mr-1" />
                        No
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button className="text-indigo-600 hover:text-indigo-900">
                        View
                      </button>
                      <button className="text-blue-600 hover:text-blue-900">
                        Edit
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        Download
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// Mock data for document templates
const documentTemplates = [
  {
    id: 'sop-template',
    name: 'Statement of Purpose',
    description: 'Template for university applications with academic guidelines',
    type: 'sop'
  },
  {
    id: 'loa-template',
    name: 'Letter of Authorization',
    description: 'Legal authorization for visa representation',
    type: 'loa'
  },
  {
    id: 'loi-template',
    name: 'Letter of Invitation',
    description: 'Formal invitation from sponsoring organization',
    type: 'loi'
  },
  {
    id: 'cv-template',
    name: 'CV/Resume Template',
    description: 'Standardized format for professional experience',
    type: 'cv'
  },
  {
    id: 'financial-template',
    name: 'Financial Statement',
    description: 'Format for proof of funds and financial capacity',
    type: 'financial'
  },
  {
    id: 'cover-letter',
    name: 'Cover Letter',
    description: 'Professional introduction for visa application',
    type: 'cover'
  }
];

// Mock data for document versions
const documentVersions = [
  {
    name: 'Statement of Purpose - Aditya Mehta.docx',
    version: '2.3',
    date: 'May 17, 2023',
    signed: true
  },
  {
    name: 'Financial Support Letter - Sneha Reddy.pdf',
    version: '1.1',
    date: 'May 15, 2023',
    signed: true
  },
  {
    name: 'Employment Verification - Rajat Gupta.pdf',
    version: '3.0',
    date: 'May 14, 2023',
    signed: false
  },
  {
    name: 'Artist Portfolio - Meera Patel.pdf',
    version: '1.0',
    date: 'May 10, 2023',
    signed: false
  }
];

// Mock data for document statuses
const documentStatuses = [
  {
    name: 'EB-1 Application Form',
    type: 'application',
    client: 'Aditya Mehta',
    createdDate: 'May 18, 2023',
    status: 'Completed',
    verified: true
  },
  {
    name: 'Statement of Purpose',
    type: 'sop',
    client: 'Sneha Reddy',
    createdDate: 'May 17, 2023',
    status: 'In Review',
    verified: false
  },
  {
    name: 'Employment Verification',
    type: 'employment',
    client: 'Rajat Gupta',
    createdDate: 'May 16, 2023',
    status: 'Pending',
    verified: false
  },
  {
    name: 'O-1 Support Letters',
    type: 'reference',
    client: 'Meera Patel',
    createdDate: 'May 15, 2023',
    status: 'Rejected',
    verified: false
  },
  {
    name: 'Financial Documents',
    type: 'financial',
    client: 'Aditya Mehta',
    createdDate: 'May 14, 2023',
    status: 'Completed',
    verified: true
  }
];

// Helper functions
function getTemplateIconColor(type: string) {
  switch (type) {
    case 'sop':
      return 'bg-blue-100 text-blue-600';
    case 'loa':
      return 'bg-purple-100 text-purple-600';
    case 'loi':
      return 'bg-green-100 text-green-600';
    case 'cv':
      return 'bg-indigo-100 text-indigo-600';
    case 'financial':
      return 'bg-amber-100 text-amber-600';
    case 'cover':
      return 'bg-rose-100 text-rose-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
}

function getDocumentTypeColor(type: string) {
  switch (type) {
    case 'application':
      return 'bg-indigo-100 text-indigo-600';
    case 'sop':
      return 'bg-blue-100 text-blue-600';
    case 'employment':
      return 'bg-purple-100 text-purple-600';
    case 'reference':
      return 'bg-green-100 text-green-600';
    case 'financial':
      return 'bg-amber-100 text-amber-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
}

function getStatusBadgeColor(status: string) {
  switch (status) {
    case 'Completed':
      return 'bg-green-100 text-green-800';
    case 'In Review':
      return 'bg-blue-100 text-blue-800';
    case 'Pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'Rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
} 