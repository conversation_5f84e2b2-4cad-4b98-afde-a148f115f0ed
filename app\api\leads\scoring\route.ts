import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    // Authentication removed - allow access to lead scoring

    const body = await request.json();
    const { leadData } = body;

    // Mock lead scoring algorithm
    let score = 0;
    
    // Score based on various factors
    if (leadData.hasPassport) score += 20;
    if (leadData.hasValidVisa) score += 15;
    if (leadData.income && leadData.income > 50000) score += 25;
    if (leadData.education === 'masters' || leadData.education === 'phd') score += 20;
    if (leadData.englishProficiency === 'native' || leadData.englishProficiency === 'fluent') score += 15;
    if (leadData.workExperience && leadData.workExperience > 2) score += 15;

    // Determine lead quality
    let quality = 'low';
    if (score >= 70) quality = 'high';
    else if (score >= 40) quality = 'medium';

    const scoringResult = {
      leadId: leadData.id || 'mock-lead-id',
      score,
      quality,
      factors: {
        passport: leadData.hasPassport ? 20 : 0,
        visa: leadData.hasValidVisa ? 15 : 0,
        income: leadData.income > 50000 ? 25 : 0,
        education: ['masters', 'phd'].includes(leadData.education) ? 20 : 0,
        english: ['native', 'fluent'].includes(leadData.englishProficiency) ? 15 : 0,
        experience: leadData.workExperience > 2 ? 15 : 0
      },
      recommendations: score >= 70 ? 
        ['Priority follow-up', 'Schedule consultation'] :
        score >= 40 ?
        ['Standard follow-up', 'Send information packet'] :
        ['Nurture campaign', 'Educational content']
    };

    return NextResponse.json(scoringResult);
  } catch (error) {
    console.error('API: Error scoring lead:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    // Authentication removed - allow access to lead scoring

    const { searchParams } = new URL(request.url);
    const leadId = searchParams.get('id');
    
    if (!leadId) {
      return NextResponse.json(
        { error: 'Lead ID is required' },
        { status: 400 }
      );
    }
    
    // Mock lead data
    const mockLead = {
      id: leadId,
      name: 'John Doe',
      email: '<EMAIL>',
      visaType: 'work',
      nationality: 'Indian',
      documentCompleteness: 85,
      responseTime: 'fast',
      financialCapacity: 'high',
      score: 75,
      status: 'active',
      assigneeId: 'agent-1',
      category: 'high'
    };
    
    return NextResponse.json(mockLead);
  } catch (error) {
    console.error('API: Error retrieving lead:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 