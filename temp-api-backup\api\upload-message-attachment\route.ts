import { NextRequest, NextResponse } from "next/server";
import { writeFile } from "fs/promises";
import { join } from "path";
import { v4 as uuidv4 } from "uuid";

// Define allowed file types
const allowedTypes = [
  "application/pdf",
  "image/jpeg",
  "image/png",
  "image/gif",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
];

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get("file") as File;
    
    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }
    
    // Validate file type
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: "File type not allowed" }, { status: 400 });
    }
    
    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: "File too large (max 10MB)" }, { status: 400 });
    }
    
    // Generate unique filename
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Create unique filename with original extension
    const originalName = file.name;
    const extension = originalName.split('.').pop();
    const filename = `${uuidv4()}.${extension}`;
    
    // Save to public directory so it's accessible
    const uploadDir = join(process.cwd(), "public", "uploads", "messages");
    const filePath = join(uploadDir, filename);
    
    try {
      await writeFile(filePath, buffer);
    } catch (error) {
      console.error("Error writing file:", error);
      // Create directory if it doesn't exist
      const { mkdir } = require('fs/promises');
      await mkdir(uploadDir, { recursive: true });
      await writeFile(filePath, buffer);
    }
    
    // Return the file information
    return NextResponse.json({
      success: true,
      filename: filename,
      originalName: originalName,
      size: file.size,
      mimetype: file.type,
      url: `/uploads/messages/${filename}`
    });
    
  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json({ error: "Failed to upload file" }, { status: 500 });
  }
} 