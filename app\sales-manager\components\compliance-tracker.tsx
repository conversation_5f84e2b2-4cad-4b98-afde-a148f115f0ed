"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, AlertCircle } from "lucide-react";

interface Violation {
  id: string;
  type: string;
  executive: string;
  date: string;
  severity: string;
}

interface Alert {
  id: string;
  type: string;
  description: string;
  dueDate: string;
}

interface ComplianceKpis {
  documentAccuracy: number;
  policyAdherence: number;
}

interface ComplianceTrackerProps {
  violations: Violation[];
  alerts: Alert[];
  kpis: ComplianceKpis;
}

export function ComplianceTracker({ violations, alerts, kpis }: ComplianceTrackerProps) {
  const getViolationTitle = (type: string) => {
    switch (type) {
      case 'document-accuracy': return 'Document Accuracy Issue';
      case 'response-time': return 'Slow Response Time';
      case 'policy-violation': return 'Policy Violation';
      default: return type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };
  
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'deadline-approaching': return <Clock className="text-amber-500 h-5 w-5" />;
      case 'follow-up-required': return <AlertCircle className="text-blue-500 h-5 w-5" />;
      case 'escalation-needed': return <AlertTriangle className="text-red-500 h-5 w-5" />;
      default: return <AlertCircle className="text-gray-500 h-5 w-5" />;
    }
  };
  
  const getAlertTitle = (type: string) => {
    switch (type) {
      case 'deadline-approaching': return 'Deadline Approaching';
      case 'follow-up-required': return 'Follow-up Required';
      case 'escalation-needed': return 'Escalation Needed';
      default: return type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };
  
  const getDaysRemaining = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-3">
        <div className="flex-1 bg-green-50 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-green-600 font-medium">Document Accuracy</p>
              <p className="text-xl font-bold text-green-700">{kpis.documentAccuracy}%</p>
            </div>
            <FileCheck className="h-8 w-8 text-green-500" />
          </div>
          <div className="mt-2 h-1.5 w-full bg-green-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-green-500 rounded-full"
              style={{ width: `${kpis.documentAccuracy}%` }}
            ></div>
          </div>
        </div>
        
        <div className="flex-1 bg-blue-50 p-3 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-blue-600 font-medium">Policy Adherence</p>
              <p className="text-xl font-bold text-blue-700">{kpis.policyAdherence}%</p>
            </div>
            <CheckCircle className="h-8 w-8 text-blue-500" />
          </div>
          <div className="mt-2 h-1.5 w-full bg-blue-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-blue-500 rounded-full"
              style={{ width: `${kpis.policyAdherence}%` }}
            ></div>
          </div>
        </div>
      </div>
      
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3">Recent Violations</h3>
        {violations.length === 0 ? (
          <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg text-center">
            No recent violations recorded.
          </div>
        ) : (
          <div className="space-y-2">
            {violations.map((violation) => (
              <div 
                key={violation.id} 
                className={`p-3 rounded-lg flex items-start ${
                  violation.severity === 'high' ? 'bg-red-50 border border-red-100' :
                  violation.severity === 'medium' ? 'bg-amber-50 border border-amber-100' :
                  'bg-blue-50 border border-blue-100'
                }`}
              >
                <div 
                  className={`rounded-full p-1.5 mr-3 ${
                    violation.severity === 'high' ? 'bg-red-100 text-red-600' :
                    violation.severity === 'medium' ? 'bg-amber-100 text-amber-600' :
                    'bg-blue-100 text-blue-600'
                  }`}
                >
                  <AlertTriangle className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <h4 className="text-sm font-medium">{getViolationTitle(violation.type)}</h4>
                    <span 
                      className={`text-xs font-medium capitalize ${
                        violation.severity === 'high' ? 'text-red-600' :
                        violation.severity === 'medium' ? 'text-amber-600' :
                        'text-blue-600'
                      }`}
                    >
                      {violation.severity}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">Executive: {violation.executive}</p>
                  <p className="text-xs text-gray-500 mt-0.5">Date: {violation.date}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3">Pending Alerts</h3>
        {alerts.length === 0 ? (
          <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg text-center">
            No pending alerts at this time.
          </div>
        ) : (
          <div className="space-y-2">
            {alerts.map((alert) => {
              const daysRemaining = getDaysRemaining(alert.dueDate);
              
              return (
                <div 
                  key={alert.id} 
                  className={`p-3 rounded-lg flex items-start ${
                    daysRemaining <= 1 ? 'bg-red-50 border border-red-100' :
                    daysRemaining <= 3 ? 'bg-amber-50 border border-amber-100' :
                    'bg-blue-50 border border-blue-100'
                  }`}
                >
                  <div className="mr-3 flex-shrink-0">
                    {getAlertIcon(alert.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <h4 className="text-sm font-medium">{getAlertTitle(alert.type)}</h4>
                      <span 
                        className={`text-xs font-medium ${
                          daysRemaining <= 1 ? 'text-red-600' :
                          daysRemaining <= 3 ? 'text-amber-600' :
                          'text-blue-600'
                        }`}
                      >
                        {daysRemaining === 0 ? 'Due today' :
                         daysRemaining < 0 ? `Overdue by ${Math.abs(daysRemaining)} days` :
                         `${daysRemaining} days left`}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">{alert.description}</p>
                    <p className="text-xs text-gray-500 mt-0.5">Due: {alert.dueDate}</p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
} 