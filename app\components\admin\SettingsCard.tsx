"use client";

import React from "react";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import GlassCard from "@/components/GlassCard";

interface SettingsCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  badge?: {
    text: string;
    color: string;
  };
}

const SettingsCard = ({ title, description, icon, path, badge }: SettingsCardProps) => (
  <Link 
    href={path as any}
    className="block transform transition-all duration-300 hover:translate-y-[-4px]"
  >
    <GlassCard>
      <div className="p-5">
        <div className="flex justify-between items-start">
          <div>
            <div className="rounded-full w-12 h-12 bg-indigo-50/50 flex items-center justify-center mb-3">
              {icon}
            </div>
            <h3 className="text-lg font-semibold text-indigo-800 flex items-center">
              {title}
              {badge && (
                <span className={`ml-2 text-xs font-medium px-2 py-0.5 rounded-full ${badge.color}`}>
                  {badge.text}
                </span>
              )}
            </h3>
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
              {description}
            </p>
          </div>
          <ChevronRight className="h-5 w-5 text-indigo-400" />
        </div>
      </div>
    </GlassCard>
  </Link>
);

export default SettingsCard; 