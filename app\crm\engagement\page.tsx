"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  Calendar,
  Clock,
  Mail,
  Phone,
  MessageSquare,
  FileText,
  Users,
  Filter,
  Search,
  Plus,
  ChevronRight,
  CheckCircle2,
  AlertCircle
} from "lucide-react";

// Mock data for engagement activities
const recentActivities = [
  {
    id: 1,
    clientName: "<PERSON><PERSON>",
    activityType: "Call",
    description: "Follow-up call regarding document submission",
    date: "Today, 10:30 AM",
    status: "Completed",
    duration: "15 min",
    agent: "<PERSON><PERSON>"
  },
  {
    id: 2,
    clientName: "<PERSON>",
    activityType: "Email",
    description: "Sent visa interview preparation materials",
    date: "Yesterday, 2:45 PM",
    status: "Completed",
    duration: "-",
    agent: "<PERSON><PERSON> <PERSON>"
  },
  {
    id: 3,
    clientName: "<PERSON><PERSON>",
    activityType: "Meeting",
    description: "Pre-visa interview coaching session",
    date: "May 10, 2023, 11:00 AM",
    status: "Scheduled",
    duration: "45 min",
    agent: "Vikram <PERSON>"
  },
  {
    id: 4,
    clientName: "Suresh Kumar",
    activityType: "Document Review",
    description: "Financial documents verification",
    date: "May 9, 2023, 4:00 PM",
    status: "Pending",
    duration: "-",
    agent: "Anjali Patel"
  },
  {
    id: 5,
    clientName: "Meera Kapoor",
    activityType: "SMS",
    description: "Reminder for upcoming visa appointment",
    date: "May 8, 2023, 9:15 AM",
    status: "Completed",
    duration: "-",
    agent: "System"
  }
];

// Mock data for communication templates
const communicationTemplates = [
  {
    id: 1,
    title: "Visa Interview Preparation",
    category: "Email",
    lastUsed: "2 days ago",
    usage: 145
  },
  {
    id: 2,
    title: "Document Submission Reminder",
    category: "Email",
    lastUsed: "5 days ago",
    usage: 230
  },
  {
    id: 3,
    title: "Application Status Update",
    category: "Email",
    lastUsed: "1 week ago",
    usage: 187
  },
  {
    id: 4,
    title: "Appointment Confirmation",
    category: "SMS",
    lastUsed: "3 days ago",
    usage: 321
  },
  {
    id: 5,
    title: "Payment Receipt",
    category: "Email",
    lastUsed: "2 weeks ago",
    usage: 93
  }
];

// Mock data for client engagement stats
const clientEngagementStats = {
  totalActivities: 1243,
  completedActivities: 982,
  pendingFollowUps: 37,
  upcomingMeetings: 18,
  averageResponseTime: "4.5 hours",
  clientSatisfactionRate: 92
};

// Mock clients list for engagement
const clients = [
  {
    id: 1,
    name: "Priya Sharma",
    service: "Student Visa",
    country: "Australia",
    lastContact: "2 days ago",
    nextAction: "Document Review",
    nextActionDate: "May 15, 2023",
    engagementScore: 85
  },
  {
    id: 2,
    name: "Raj Patel",
    service: "Work Visa",
    country: "Canada",
    lastContact: "1 week ago",
    nextAction: "Visa Interview",
    nextActionDate: "May 20, 2023",
    engagementScore: 72
  },
  {
    id: 3,
    name: "Ananya Desai",
    service: "Tourist Visa",
    country: "United States",
    lastContact: "3 weeks ago",
    nextAction: "Application Review",
    nextActionDate: "May 12, 2023",
    engagementScore: 45
  },
  {
    id: 4,
    name: "Vikram Singh",
    service: "Business Visa",
    country: "United Kingdom",
    lastContact: "1 month ago",
    nextAction: "Document Collection",
    nextActionDate: "May 18, 2023",
    engagementScore: 58
  }
];

export default function EngagementPage() {
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("activities");

  // Get engagement score color
  const getEngagementScoreColor = (score: number) => {
    if (score >= 80) return "text-green-500";
    if (score >= 60) return "text-amber-500";
    return "text-red-500";
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "Scheduled":
        return "bg-blue-100 text-blue-800";
      case "Pending":
        return "bg-amber-100 text-amber-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get activity type icon
  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case "Call":
        return <Phone className="h-4 w-4" />;
      case "Email":
        return <Mail className="h-4 w-4" />;
      case "Meeting":
        return <Users className="h-4 w-4" />;
      case "Document Review":
        return <FileText className="h-4 w-4" />;
      case "SMS":
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Engagement Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clientEngagementStats.totalActivities}</div>
            <p className="text-xs text-muted-foreground">
              {clientEngagementStats.completedActivities} completed
            </p>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending Follow-ups</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clientEngagementStats.pendingFollowUps}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Meetings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clientEngagementStats.upcomingMeetings}</div>
            <p className="text-xs text-muted-foreground">
              This week
            </p>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-1">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{clientEngagementStats.averageResponseTime}</div>
            <p className="text-xs text-muted-foreground">
              Average
            </p>
          </CardContent>
        </Card>
        
        <Card className="lg:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Client Satisfaction</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <div className="text-2xl font-bold">{clientEngagementStats.clientSatisfactionRate}%</div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className="bg-green-500 h-2.5 rounded-full" 
                  style={{ width: `${clientEngagementStats.clientSatisfactionRate}%` }}
                ></div>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Based on recent feedback
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Client List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg">Clients</CardTitle>
              <div className="w-full max-w-sm flex items-center space-x-2 pb-0 pt-2">
                <Input 
                  placeholder="Search clients..." 
                  className="h-8 text-sm"
                />
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {clients.map((client) => (
                  <div 
                    key={client.id} 
                    className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedClient && selectedClient.id === client.id ? 'bg-gray-50 border-l-4 border-indigo-500' : ''
                    }`}
                    onClick={() => setSelectedClient(client)}
                  >
                    <div className="flex justify-between">
                      <h3 className="font-medium">{client.name}</h3>
                      <span className={getEngagementScoreColor(client.engagementScore)}>
                        {client.engagementScore}%
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{client.service} • {client.country}</p>
                    <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                      <span>Last Contact: {client.lastContact}</span>
                      <ChevronRight className="h-4 w-4" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Engagement Activities & Communication Tools */}
        <div className="lg:col-span-2">
          <Tabs 
            defaultValue="activities" 
            className="w-full"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <div className="flex justify-between items-center mb-4">
              <TabsList>
                <TabsTrigger value="activities">Activities</TabsTrigger>
                <TabsTrigger value="communications">Communications</TabsTrigger>
                <TabsTrigger value="templates">Templates</TabsTrigger>
              </TabsList>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Activity
              </Button>
            </div>

            <TabsContent value="activities" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Recent Activities</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {recentActivities.map((activity) => (
                      <div key={activity.id} className="p-4 hover:bg-gray-50">
                        <div className="flex items-start gap-3">
                          <div className={`mt-0.5 p-2 rounded-full ${
                            activity.status === "Completed" 
                              ? 'bg-green-100' 
                              : activity.status === "Scheduled" 
                                ? 'bg-blue-100' 
                                : 'bg-amber-100'
                          }`}>
                            {getActivityTypeIcon(activity.activityType)}
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between">
                              <h4 className="font-medium">{activity.clientName}</h4>
                              <Badge className={getStatusBadgeColor(activity.status)}>
                                {activity.status}
                              </Badge>
                            </div>
                            <p className="text-sm mt-1">{activity.description}</p>
                            <div className="flex justify-between mt-2 text-xs text-gray-500">
                              <div className="flex items-center gap-3">
                                <span className="flex items-center">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {activity.duration}
                                </span>
                                <span className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  {activity.date}
                                </span>
                              </div>
                              <span>{activity.agent}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="communications" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Communication Tools</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <Phone className="h-6 w-6 mb-2 text-indigo-600" />
                      <span className="text-sm">Call Client</span>
                    </button>
                    <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <Mail className="h-6 w-6 mb-2 text-indigo-600" />
                      <span className="text-sm">Send Email</span>
                    </button>
                    <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <MessageSquare className="h-6 w-6 mb-2 text-indigo-600" />
                      <span className="text-sm">Send SMS</span>
                    </button>
                    <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <Calendar className="h-6 w-6 mb-2 text-indigo-600" />
                      <span className="text-sm">Schedule Meeting</span>
                    </button>
                  </div>

                  {selectedClient ? (
                    <div className="mt-6 p-4 border rounded-lg">
                      <h3 className="font-medium text-lg mb-2">{selectedClient.name}</h3>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Next Action:</p>
                          <p className="font-medium">{selectedClient.nextAction}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Due Date:</p>
                          <p className="font-medium">{selectedClient.nextActionDate}</p>
                        </div>
                      </div>
                      <div className="mt-4 flex justify-end gap-2">
                        <Button variant="outline" size="sm">View History</Button>
                        <Button size="sm">Take Action</Button>
                      </div>
                    </div>
                  ) : (
                    <div className="mt-6 p-4 border rounded-lg bg-gray-50 text-center">
                      <p className="text-gray-500">Select a client to view details and take action</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="templates" className="m-0">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-lg">Communication Templates</CardTitle>
                  <Button size="sm" variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Template
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="divide-y">
                    {communicationTemplates.map((template) => (
                      <div key={template.id} className="py-3 first:pt-0 last:pb-0">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium">{template.title}</h4>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline">{template.category}</Badge>
                              <span className="text-xs text-gray-500">Used {template.usage} times</span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">Edit</Button>
                            <Button size="sm">Use</Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Upcoming Engagements Calendar */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Upcoming Engagements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-4 rounded-lg border border-dashed border-gray-300 text-center">
            <Calendar className="h-12 w-12 mx-auto text-gray-400" />
            <h3 className="mt-2 font-medium">Calendar Integration</h3>
            <p className="text-sm text-gray-500 mt-1">
              The calendar module shows upcoming client engagements, meetings, and follow-ups.
            </p>
            <Button className="mt-4" variant="outline">View Full Calendar</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 