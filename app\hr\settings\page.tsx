"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Settings,
  Users,
  Bell,
  Database,
  Shield,
  Mail,
  Clock,
  Languages,
  Save,
  Trash2,
  Download,
  Upload,
  Eye,
  EyeOff,
  Lock,
  Key,
  UserPlus,
  AlertTriangle,
  HelpCircle,
  FileText,
  RefreshCw
} from "lucide-react";

export default function HRSettings() {
  const [activeTab, setActiveTab] = useState("system");
  
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">HR Settings</h1>
          <p className="text-muted-foreground">
            Configure HR system settings, user access, and preferences
          </p>
        </div>
        <Button>
          <Save className="h-4 w-4 mr-2" />
          Save All Changes
        </Button>
      </div>

      <Tabs defaultValue="system" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-4 w-full max-w-3xl">
          <TabsTrigger value="system" className="flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            System
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            User Management
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center">
            <Database className="h-4 w-4 mr-2" />
            Data Management
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Configure system-wide settings for the HR module</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Company Name</label>
                  <Input defaultValue="Visa Mentor" />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Department Name</label>
                  <Input defaultValue="Human Resources" />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Fiscal Year Start</label>
                  <Select defaultValue="january">
                    <SelectTrigger>
                      <SelectValue placeholder="Select month" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="january">January</SelectItem>
                      <SelectItem value="april">April</SelectItem>
                      <SelectItem value="july">July</SelectItem>
                      <SelectItem value="october">October</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Date Format</label>
                  <Select defaultValue="mdy">
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mdy">MM/DD/YYYY</SelectItem>
                      <SelectItem value="dmy">DD/MM/YYYY</SelectItem>
                      <SelectItem value="ymd">YYYY/MM/DD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Security Settings</h3>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Two-Factor Authentication</label>
                    <p className="text-sm text-muted-foreground">Require 2FA for all HR staff members</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Session Timeout</label>
                    <p className="text-sm text-muted-foreground">Log users out after period of inactivity</p>
                  </div>
                  <Select defaultValue="30">
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                      <SelectItem value="120">2 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-lg font-medium">Localization</h3>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Default Language</label>
                    <p className="text-sm text-muted-foreground">Set default language for the system</p>
                  </div>
                  <Select defaultValue="en">
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                      <SelectItem value="de">German</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Time Zone</label>
                    <p className="text-sm text-muted-foreground">Set organization default time zone</p>
                  </div>
                  <Select defaultValue="utc-5">
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="utc-8">Pacific (UTC-8)</SelectItem>
                      <SelectItem value="utc-7">Mountain (UTC-7)</SelectItem>
                      <SelectItem value="utc-6">Central (UTC-6)</SelectItem>
                      <SelectItem value="utc-5">Eastern (UTC-5)</SelectItem>
                      <SelectItem value="utc">UTC</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
              <CardDescription>Manage user access and permissions for HR modules</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <Input className="max-w-sm" placeholder="Search users..." />
                <Button>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Active</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[
                    { name: "Sarah Johnson", email: "<EMAIL>", role: "HR Administrator", status: "Active", lastActive: "Today" },
                    { name: "Michael Chen", email: "<EMAIL>", role: "HR Manager", status: "Active", lastActive: "Yesterday" },
                    { name: "Priya Patel", email: "<EMAIL>", role: "HR Specialist", status: "Inactive", lastActive: "2 weeks ago" },
                    { name: "David Wilson", email: "<EMAIL>", role: "Recruiting Manager", status: "Active", lastActive: "Today" }
                  ].map((user, i) => (
                    <TableRow key={i}>
                      <TableCell>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{user.role}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={user.status === "Active" ? "default" : "secondary"}>
                          {user.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{user.lastActive}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon">
                            <Key className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              <div className="pt-4 border-t">
                <h3 className="text-lg font-medium mb-4">Role Permissions</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>HR Module</TableHead>
                      <TableHead>Administrator</TableHead>
                      <TableHead>HR Manager</TableHead>
                      <TableHead>HR Specialist</TableHead>
                      <TableHead>Recruiting Manager</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {[
                      { module: "Dashboard", admin: true, manager: true, specialist: true, recruiting: true },
                      { module: "Talent Management", admin: true, manager: true, specialist: true, recruiting: true },
                      { module: "Performance", admin: true, manager: true, specialist: false, recruiting: false },
                      { module: "Compliance", admin: true, manager: true, specialist: true, recruiting: false },
                      { module: "Learning", admin: true, manager: true, specialist: true, recruiting: true },
                      { module: "Settings", admin: true, manager: false, specialist: false, recruiting: false }
                    ].map((item, i) => (
                      <TableRow key={i}>
                        <TableCell>{item.module}</TableCell>
                        <TableCell><Checkbox checked={item.admin} /></TableCell>
                        <TableCell><Checkbox checked={item.manager} /></TableCell>
                        <TableCell><Checkbox checked={item.specialist} /></TableCell>
                        <TableCell><Checkbox checked={item.recruiting} /></TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>Configure how and when HR notifications are sent</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Email Notifications</h3>
                
                {[
                  { title: "New Employee Onboarding", desc: "Send notifications for new employee onboarding tasks" },
                  { title: "Performance Review Cycles", desc: "Send reminders for upcoming and overdue reviews" },
                  { title: "Employee Off-boarding", desc: "Alert when employee exit processes are initiated" },
                  { title: "Compliance Alerts", desc: "Notify about compliance deadlines and violations" },
                  { title: "Training Completion", desc: "Updates on employee training completion status" }
                ].map((item, i) => (
                  <div key={i} className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <label className="text-sm font-medium">{item.title}</label>
                      <p className="text-sm text-muted-foreground">{item.desc}</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                ))}
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">In-App Notifications</h3>
                
                {[
                  { title: "Dashboard Alerts", desc: "Show high priority alerts on the dashboard" },
                  { title: "Task Reminders", desc: "Show reminders for assigned HR tasks" },
                  { title: "System Announcements", desc: "Display system updates and maintenance notices" },
                  { title: "Document Approvals", desc: "Notify when documents need review or approval" }
                ].map((item, i) => (
                  <div key={i} className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <label className="text-sm font-medium">{item.title}</label>
                      <p className="text-sm text-muted-foreground">{item.desc}</p>
                    </div>
                    <Switch defaultChecked={i !== 2} />
                  </div>
                ))}
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Notification Schedule</h3>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <label className="text-sm font-medium">Delivery Frequency</label>
                    <p className="text-sm text-muted-foreground">How often to send grouped notifications</p>
                  </div>
                  <Select defaultValue="immediate">
                    <SelectTrigger className="w-36">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="immediate">Immediate</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily Digest</SelectItem>
                      <SelectItem value="weekly">Weekly Summary</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Data Management</CardTitle>
              <CardDescription>Manage HR data, backups, and integrations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Backup & Recovery</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="rounded-lg border p-4">
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Automated Backups</h4>
                      <p className="text-sm text-muted-foreground">System automatically backs up data daily at 2:00 AM</p>
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-sm">Last backup: Yesterday, 2:00 AM</span>
                        <Switch defaultChecked />
                      </div>
                    </div>
                  </div>
                  
                  <div className="rounded-lg border p-4">
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Manual Data Backup</h4>
                      <p className="text-sm text-muted-foreground">Create a backup of all HR data</p>
                      <Button className="mt-2 w-full" variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Backup Now
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Data Retention</h3>
                
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data Category</TableHead>
                      <TableHead>Retention Period</TableHead>
                      <TableHead>Auto-Archive</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {[
                      { category: "Employee Records", period: "7 years after termination", archive: true },
                      { category: "Performance Reviews", period: "5 years", archive: true },
                      { category: "Payroll Data", period: "7 years", archive: true },
                      { category: "Recruitment Data", period: "2 years", archive: false },
                      { category: "Training Records", period: "3 years", archive: false }
                    ].map((item, i) => (
                      <TableRow key={i}>
                        <TableCell>{item.category}</TableCell>
                        <TableCell>
                          <Select defaultValue={item.period}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1 year">1 year</SelectItem>
                              <SelectItem value="2 years">2 years</SelectItem>
                              <SelectItem value="3 years">3 years</SelectItem>
                              <SelectItem value="5 years">5 years</SelectItem>
                              <SelectItem value="7 years">7 years</SelectItem>
                              <SelectItem value="7 years after termination">7 years after termination</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Switch defaultChecked={item.archive} />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">System Integrations</h3>
                
                <div className="space-y-3">
                  {[
                    { name: "Payroll System", status: "Connected", lastSync: "Today, 9:00 AM" },
                    { name: "Applicant Tracking", status: "Connected", lastSync: "Yesterday, 4:30 PM" },
                    { name: "Learning Management", status: "Not Connected", lastSync: "Never" },
                    { name: "Time & Attendance", status: "Connected", lastSync: "Today, 8:15 AM" }
                  ].map((item, i) => (
                    <div key={i} className="flex items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <label className="font-medium">{item.name}</label>
                        <div className="flex items-center text-sm">
                          <Badge variant={item.status === "Connected" ? "outline" : "secondary"} className="mr-2">
                            {item.status}
                          </Badge>
                          {item.status === "Connected" && (
                            <span className="text-muted-foreground">Last sync: {item.lastSync}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {item.status === "Connected" ? (
                          <Button variant="outline" size="sm">
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Sync Now
                          </Button>
                        ) : (
                          <Button variant="outline" size="sm">
                            Connect
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 