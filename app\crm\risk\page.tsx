"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  AlertTriangle,
  AlertCircle,
  CheckCircle,
  ShieldCheck,
  FileText,
  User,
  ChevronRight,
  Filter,
  Search,
  Plus,
  XCircle,
  Clock,
  Calendar,
  Shield
} from "lucide-react";

// Mock data for risk metrics
const riskMetrics = {
  criticalRisks: 12,
  highRisks: 28,
  mediumRisks: 47,
  lowRisks: 103,
  totalAssessments: 245,
  complianceRate: 87,
  avgResolutionTime: "6.5 days",
  openIssues: 34
};

// Mock data for clients with risk assessments
const clients = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    service: "Student Visa",
    country: "Australia",
    lastAssessment: "3 days ago",
    riskScore: 82,
    riskLevel: "High",
    criticalFactors: 3,
    flagged: true
  },
  {
    id: 2,
    name: "<PERSON>",
    service: "Work Visa",
    country: "Canada",
    lastAssessment: "1 week ago",
    riskScore: 45,
    riskLevel: "Medium",
    criticalFactors: 1,
    flagged: false
  },
  {
    id: 3,
    name: "Ananya Desai",
    service: "Tourist Visa",
    country: "United States",
    lastAssessment: "2 days ago",
    riskScore: 28,
    riskLevel: "Low",
    criticalFactors: 0,
    flagged: false
  },
  {
    id: 4,
    name: "Vikram Singh",
    service: "Business Visa",
    country: "United Kingdom",
    lastAssessment: "5 days ago",
    riskScore: 67,
    riskLevel: "Medium",
    criticalFactors: 2,
    flagged: true
  },
  {
    id: 5,
    name: "Meera Kapoor",
    service: "Family Visa",
    country: "New Zealand",
    lastAssessment: "Yesterday",
    riskScore: 92,
    riskLevel: "Critical",
    criticalFactors: 4,
    flagged: true
  }
];

// Mock risk factors data
const riskFactors = [
  {
    id: 1,
    category: "Documentation",
    factors: [
      { id: 101, name: "Incomplete financial documents", severity: "High", impact: 80 },
      { id: 102, name: "Inconsistent educational records", severity: "Critical", impact: 95 },
      { id: 103, name: "Missing proof of ties to home country", severity: "High", impact: 85 },
      { id: 104, name: "Passport validity issues", severity: "Medium", impact: 60 }
    ]
  },
  {
    id: 2,
    category: "Application History",
    factors: [
      { id: 201, name: "Previous visa rejections", severity: "Critical", impact: 90 },
      { id: 202, name: "Overstay on previous visas", severity: "Critical", impact: 95 },
      { id: 203, name: "Multiple concurrent applications", severity: "Medium", impact: 65 },
      { id: 204, name: "Frequent short-term visits", severity: "Low", impact: 40 }
    ]
  },
  {
    id: 3,
    category: "Background Checks",
    factors: [
      { id: 301, name: "Criminal record", severity: "Critical", impact: 100 },
      { id: 302, name: "Identity verification issues", severity: "High", impact: 85 },
      { id: 303, name: "Security concerns", severity: "Critical", impact: 95 },
      { id: 304, name: "Employment verification failed", severity: "High", impact: 80 }
    ]
  },
  {
    id: 4,
    category: "Financial Assessment",
    factors: [
      { id: 401, name: "Insufficient funds", severity: "High", impact: 85 },
      { id: 402, name: "Unexplained financial transactions", severity: "Critical", impact: 90 },
      { id: 403, name: "Financial sponsor issues", severity: "Medium", impact: 70 },
      { id: 404, name: "Recent large deposits", severity: "Medium", impact: 65 }
    ]
  }
];

// Mock compliance alerts
const complianceAlerts = [
  {
    id: 1,
    clientName: "Meera Kapoor",
    alertType: "Critical",
    description: "Multiple critical risk factors identified in financial documentation",
    date: "Today, 09:15 AM",
    status: "Unresolved",
    assignedTo: "Rahul Gupta"
  },
  {
    id: 2,
    clientName: "Priya Sharma",
    alertType: "High",
    description: "Inconsistency in educational records requires immediate verification",
    date: "Yesterday, 03:45 PM",
    status: "In Progress",
    assignedTo: "Anjali Patel"
  },
  {
    id: 3,
    clientName: "Vikram Singh",
    alertType: "Medium",
    description: "Employment verification delayed; follow-up required",
    date: "May 10, 2023, 11:30 AM",
    status: "In Progress",
    assignedTo: "Suresh Kumar"
  },
  {
    id: 4,
    clientName: "Sunil Verma",
    alertType: "High",
    description: "Previous visa rejection not disclosed in application",
    date: "May 9, 2023, 02:15 PM",
    status: "Resolved",
    assignedTo: "Rahul Gupta"
  },
  {
    id: 5,
    clientName: "Nisha Reddy",
    alertType: "Critical",
    description: "Security check flagged potential identity concerns",
    date: "May 8, 2023, 10:00 AM",
    status: "Escalated",
    assignedTo: "Vikram Singh"
  }
];

export default function RiskPage() {
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("overview");

  // Helper function to get risk level color
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case "Critical":
        return "bg-red-100 text-red-800";
      case "High":
        return "bg-orange-100 text-orange-800";
      case "Medium":
        return "bg-amber-100 text-amber-800";
      case "Low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Helper function to get risk score color
  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return "text-red-500";
    if (score >= 60) return "text-orange-500";
    if (score >= 40) return "text-amber-500";
    return "text-green-500";
  };

  // Helper function to get alert status color
  const getAlertStatusColor = (status: string) => {
    switch (status) {
      case "Resolved":
        return "bg-green-100 text-green-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Unresolved":
        return "bg-red-100 text-red-800";
      case "Escalated":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Risk Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Risk Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">{riskMetrics.criticalRisks} Critical</div>
            <div className="flex flex-col mt-2 space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-orange-500">{riskMetrics.highRisks} High</span>
                <span className="text-amber-500">{riskMetrics.mediumRisks} Medium</span>
                <span className="text-green-500">{riskMetrics.lowRisks} Low</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                <div className="flex h-1.5 rounded-full overflow-hidden">
                  <div className="bg-red-500 h-full" style={{ width: `${(riskMetrics.criticalRisks / riskMetrics.totalAssessments) * 100}%` }}></div>
                  <div className="bg-orange-500 h-full" style={{ width: `${(riskMetrics.highRisks / riskMetrics.totalAssessments) * 100}%` }}></div>
                  <div className="bg-amber-500 h-full" style={{ width: `${(riskMetrics.mediumRisks / riskMetrics.totalAssessments) * 100}%` }}></div>
                  <div className="bg-green-500 h-full" style={{ width: `${(riskMetrics.lowRisks / riskMetrics.totalAssessments) * 100}%` }}></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Compliance Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{riskMetrics.complianceRate}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
              <div 
                className="bg-blue-500 h-2.5 rounded-full" 
                style={{ width: `${riskMetrics.complianceRate}%` }}
              ></div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all risk assessments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Open Issues</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{riskMetrics.openIssues}</div>
            <p className="text-xs text-muted-foreground">
              Avg. resolution time: {riskMetrics.avgResolutionTime}
            </p>
            <div className="mt-2 flex items-center text-xs text-amber-500">
              <AlertCircle className="h-3 w-3 mr-1" />
              <span>{riskMetrics.criticalRisks + riskMetrics.highRisks} high priority</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Assessments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{riskMetrics.totalAssessments}</div>
            <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-blue-500 mr-1"></div>
                <span>This week: 42</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-gray-500 mr-1"></div>
                <span>Last week: 38</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Client List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg">Clients by Risk</CardTitle>
              <div className="w-full max-w-sm flex items-center space-x-2 pb-0 pt-2">
                <Input 
                  placeholder="Search clients..." 
                  className="h-8 text-sm"
                />
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {clients.map((client) => (
                  <div 
                    key={client.id} 
                    className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedClient && selectedClient.id === client.id ? 'bg-gray-50 border-l-4 border-indigo-500' : ''
                    }`}
                    onClick={() => setSelectedClient(client)}
                  >
                    <div className="flex justify-between">
                      <h3 className="font-medium flex items-center">
                        {client.name}
                        {client.flagged && <AlertCircle className="h-4 w-4 text-red-500 ml-2" />}
                      </h3>
                      <Badge className={getRiskLevelColor(client.riskLevel)}>
                        {client.riskLevel}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{client.service} • {client.country}</p>
                    <div className="flex justify-between items-center mt-2">
                      <span className={`text-xs ${getRiskScoreColor(client.riskScore)}`}>
                        Risk Score: {client.riskScore}/100
                      </span>
                      <span className="text-xs text-gray-500">
                        {client.criticalFactors > 0 ? `${client.criticalFactors} critical factors` : 'No critical factors'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Risk Management Tools */}
        <div className="lg:col-span-2">
          <Tabs 
            defaultValue="overview" 
            className="w-full"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <div className="flex justify-between items-center mb-4">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="factors">Risk Factors</TabsTrigger>
                <TabsTrigger value="compliance">Compliance</TabsTrigger>
              </TabsList>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                New Assessment
              </Button>
            </div>

            <TabsContent value="overview" className="m-0">
              {selectedClient ? (
                <Card>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{selectedClient.name} - Risk Assessment</CardTitle>
                        <p className="text-sm text-gray-500 mt-1">{selectedClient.service} • {selectedClient.country}</p>
                      </div>
                      <Badge className={getRiskLevelColor(selectedClient.riskLevel)}>
                        {selectedClient.riskLevel} Risk
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Risk Score Gauge */}
                    <div className="flex items-center gap-4">
                      <div className="relative w-32 h-32">
                        <svg className="w-full h-full" viewBox="0 0 100 100">
                          <circle 
                            cx="50" 
                            cy="50" 
                            r="45" 
                            fill="none" 
                            stroke="#e5e7eb" 
                            strokeWidth="10" 
                          />
                          <circle 
                            cx="50" 
                            cy="50" 
                            r="45" 
                            fill="none" 
                            stroke={
                              selectedClient.riskScore >= 80 ? "#ef4444" : 
                              selectedClient.riskScore >= 60 ? "#f97316" :
                              selectedClient.riskScore >= 40 ? "#f59e0b" : "#10b981"
                            }
                            strokeWidth="10"
                            strokeDasharray="282.7"
                            strokeDashoffset={282.7 - (282.7 * selectedClient.riskScore / 100)}
                            strokeLinecap="round"
                            transform="rotate(-90 50 50)"
                          />
                          <text 
                            x="50" 
                            y="55" 
                            fontSize="24" 
                            fontWeight="bold" 
                            textAnchor="middle"
                            fill={
                              selectedClient.riskScore >= 80 ? "#ef4444" : 
                              selectedClient.riskScore >= 60 ? "#f97316" :
                              selectedClient.riskScore >= 40 ? "#f59e0b" : "#10b981"
                            }
                          >
                            {selectedClient.riskScore}
                          </text>
                          <text 
                            x="50" 
                            y="70" 
                            fontSize="12" 
                            textAnchor="middle" 
                            fill="#6b7280"
                          >
                            Risk Score
                          </text>
                        </svg>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium mb-2">Risk Summary</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Documentation Risks:</span>
                            <span className={
                              selectedClient.riskLevel === "Critical" || selectedClient.riskLevel === "High" 
                                ? "text-red-500" 
                                : "text-amber-500"
                            }>
                              {selectedClient.riskLevel === "Critical" ? "Severe" : 
                               selectedClient.riskLevel === "High" ? "Significant" : 
                               selectedClient.riskLevel === "Medium" ? "Moderate" : "Minor"}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Background Check:</span>
                            <span className={
                              selectedClient.flagged ? "text-red-500" : "text-green-500"
                            }>
                              {selectedClient.flagged ? "Issues Found" : "Passed"}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Financial Assessment:</span>
                            <span className={
                              selectedClient.riskLevel === "Critical" || selectedClient.riskLevel === "High" 
                                ? "text-red-500" 
                                : "text-green-500"
                            }>
                              {selectedClient.riskLevel === "Critical" || selectedClient.riskLevel === "High" 
                                ? "Insufficient" 
                                : "Adequate"}
                            </span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Last Assessment:</span>
                            <span className="text-gray-500">{selectedClient.lastAssessment}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Critical Factors */}
                    {selectedClient.criticalFactors > 0 && (
                      <div>
                        <h4 className="font-medium mb-2 flex items-center">
                          <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
                          Critical Risk Factors
                        </h4>
                        <div className="bg-red-50 p-3 rounded-md border border-red-200">
                          <ul className="text-sm space-y-2">
                            {selectedClient.riskLevel === "Critical" && (
                              <>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Inconsistent educational records that don't match verification</span>
                                </li>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Multiple previous visa rejections not disclosed</span>
                                </li>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Suspicious financial transactions identified</span>
                                </li>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Identity verification discrepancies</span>
                                </li>
                              </>
                            )}
                            {selectedClient.riskLevel === "High" && (
                              <>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Incomplete financial documentation</span>
                                </li>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Previous visa rejection</span>
                                </li>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Weak ties to home country</span>
                                </li>
                              </>
                            )}
                            {selectedClient.riskLevel === "Medium" && selectedClient.criticalFactors > 0 && (
                              <>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Unclear purpose of travel</span>
                                </li>
                                <li className="flex items-start">
                                  <XCircle className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                                  <span>Financial documentation requires verification</span>
                                </li>
                              </>
                            )}
                          </ul>
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-2">
                      <Button variant="outline">
                        <FileText className="h-4 w-4 mr-2" />
                        Full Report
                      </Button>
                      <Button variant="outline">
                        <Clock className="h-4 w-4 mr-2" />
                        History
                      </Button>
                      <Button variant="default">
                        <Shield className="h-4 w-4 mr-2" />
                        Mitigation Plan
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center h-[400px]">
                    <ShieldCheck className="h-16 w-16 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-700">Select a client</h3>
                    <p className="text-sm text-gray-500 mt-2 text-center max-w-sm">
                      Select a client from the list to view their risk assessment details and create mitigation plans
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="factors" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Risk Factors Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {riskFactors.map((category) => (
                      <div key={category.id}>
                        <h3 className="font-medium mb-3">{category.category}</h3>
                        <div className="grid grid-cols-1 gap-3 mb-6">
                          {category.factors.map((factor) => (
                            <div key={factor.id} className="flex justify-between items-center p-3 border rounded-md hover:bg-gray-50">
                              <div className="flex items-start gap-3">
                                <Badge className={
                                  factor.severity === "Critical" ? "bg-red-100 text-red-800" : 
                                  factor.severity === "High" ? "bg-orange-100 text-orange-800" : 
                                  factor.severity === "Medium" ? "bg-amber-100 text-amber-800" : 
                                  "bg-green-100 text-green-800"
                                }>
                                  {factor.severity}
                                </Badge>
                                <div>
                                  <h4 className="font-medium">{factor.name}</h4>
                                  <div className="flex items-center mt-1">
                                    <span className="text-xs text-gray-500 mr-2">Impact Score:</span>
                                    <div className="w-24 bg-gray-200 rounded-full h-1.5">
                                      <div
                                        className={
                                          factor.impact >= 80 ? "bg-red-500 h-1.5 rounded-full" : 
                                          factor.impact >= 60 ? "bg-orange-500 h-1.5 rounded-full" : 
                                          factor.impact >= 40 ? "bg-amber-500 h-1.5 rounded-full" : 
                                          "bg-green-500 h-1.5 rounded-full"
                                        }
                                        style={{ width: `${factor.impact}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-xs ml-2">{factor.impact}%</span>
                                  </div>
                                </div>
                              </div>
                              <Button variant="ghost" size="sm">
                                Details
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="compliance" className="m-0">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Compliance Alerts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {complianceAlerts.map((alert) => (
                      <div key={alert.id} className="p-4 border rounded-md hover:bg-gray-50">
                        <div className="flex justify-between">
                          <div className="flex items-start gap-3">
                            <div className={
                              alert.alertType === "Critical" ? "rounded-full p-1.5 bg-red-100" : 
                              alert.alertType === "High" ? "rounded-full p-1.5 bg-orange-100" : 
                              "rounded-full p-1.5 bg-amber-100"
                            }>
                              <AlertTriangle className={
                                alert.alertType === "Critical" ? "h-4 w-4 text-red-600" : 
                                alert.alertType === "High" ? "h-4 w-4 text-orange-600" : 
                                "h-4 w-4 text-amber-600"
                              } />
                            </div>
                            <div>
                              <div className="flex items-center">
                                <h4 className="font-medium">{alert.clientName}</h4>
                                <Badge className={`ml-2 ${
                                  alert.alertType === "Critical" ? "bg-red-100 text-red-800" : 
                                  alert.alertType === "High" ? "bg-orange-100 text-orange-800" : 
                                  "bg-amber-100 text-amber-800"
                                }`}>
                                  {alert.alertType}
                                </Badge>
                              </div>
                              <p className="text-sm mt-1">{alert.description}</p>
                              <div className="flex items-center mt-2 text-xs text-gray-500">
                                <Calendar className="h-3 w-3 mr-1" />
                                <span>{alert.date}</span>
                                <span className="mx-2">•</span>
                                <User className="h-3 w-3 mr-1" />
                                <span>{alert.assignedTo}</span>
                              </div>
                            </div>
                          </div>
                          <Badge className={getAlertStatusColor(alert.status)}>
                            {alert.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Risk Management Tools */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Risk Management Tools</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <ShieldCheck className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Risk Assessment</span>
              <span className="text-xs text-gray-500 mt-1">Create new analysis</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <AlertTriangle className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Compliance Check</span>
              <span className="text-xs text-gray-500 mt-1">Verify requirements</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <FileText className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Documentation Review</span>
              <span className="text-xs text-gray-500 mt-1">Audit client files</span>
            </button>
            <button className="flex flex-col items-center justify-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
              <Shield className="h-8 w-8 mb-2 text-indigo-600" />
              <span className="text-sm font-medium">Mitigation Plans</span>
              <span className="text-xs text-gray-500 mt-1">Resolve risk factors</span>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 