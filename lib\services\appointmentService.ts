import { prisma } from '@/lib/prisma'
import { Appointment, AppointmentStatus } from '@prisma/client'

export interface CreateAppointmentData {
  userId: string
  title: string
  description?: string
  date: Date
  time: string
  duration?: number
  counselorId?: string
  counselorName?: string
  meetingLink?: string
  notes?: string
}

export interface UpdateAppointmentData {
  title?: string
  description?: string
  date?: Date
  time?: string
  duration?: number
  status?: AppointmentStatus
  counselorId?: string
  counselorName?: string
  meetingLink?: string
  notes?: string
  reminderSent?: boolean
}

export class AppointmentService {
  // Create new appointment
  static async createAppointment(data: CreateAppointmentData): Promise<Appointment> {
    try {
      return await prisma.appointment.create({
        data,
      })
    } catch (error) {
      console.error('Error creating appointment:', error)
      throw new Error('Failed to create appointment')
    }
  }

  // Get appointments for user
  static async getUserAppointments(userId: string, includeCompleted = false): Promise<Appointment[]> {
    try {
      const whereClause: any = { userId }
      
      if (!includeCompleted) {
        whereClause.status = {
          not: 'COMPLETED'
        }
        whereClause.date = {
          gte: new Date()
        }
      }

      return await prisma.appointment.findMany({
        where: whereClause,
        orderBy: { date: 'asc' },
      })
    } catch (error) {
      console.error('Error fetching appointments:', error)
      return []
    }
  }

  // Get upcoming appointments for user
  static async getUpcomingAppointments(userId: string, limit = 5): Promise<Appointment[]> {
    try {
      return await prisma.appointment.findMany({
        where: { 
          userId,
          date: {
            gte: new Date()
          },
          status: {
            in: ['SCHEDULED', 'CONFIRMED']
          }
        },
        orderBy: { date: 'asc' },
        take: limit,
      })
    } catch (error) {
      console.error('Error fetching upcoming appointments:', error)
      return []
    }
  }

  // Get appointment by ID
  static async getAppointmentById(id: string, userId: string): Promise<Appointment | null> {
    try {
      return await prisma.appointment.findFirst({
        where: { 
          id,
          userId
        },
      })
    } catch (error) {
      console.error('Error fetching appointment:', error)
      return null
    }
  }

  // Update appointment
  static async updateAppointment(id: string, userId: string, data: UpdateAppointmentData): Promise<Appointment | null> {
    try {
      return await prisma.appointment.update({
        where: { id },
        data,
      })
    } catch (error) {
      console.error('Error updating appointment:', error)
      throw new Error('Failed to update appointment')
    }
  }

  // Cancel appointment
  static async cancelAppointment(id: string, userId: string): Promise<boolean> {
    try {
      await prisma.appointment.update({
        where: { 
          id,
          userId
        },
        data: {
          status: 'CANCELLED'
        }
      })
      return true
    } catch (error) {
      console.error('Error cancelling appointment:', error)
      return false
    }
  }

  // Reschedule appointment
  static async rescheduleAppointment(id: string, userId: string, newDate: Date, newTime: string): Promise<Appointment | null> {
    try {
      return await prisma.appointment.update({
        where: { 
          id,
          userId
        },
        data: {
          date: newDate,
          time: newTime,
          status: 'RESCHEDULED'
        }
      })
    } catch (error) {
      console.error('Error rescheduling appointment:', error)
      return null
    }
  }

  // Mark appointment as completed
  static async completeAppointment(id: string, userId: string, notes?: string): Promise<Appointment | null> {
    try {
      return await prisma.appointment.update({
        where: { 
          id,
          userId
        },
        data: {
          status: 'COMPLETED',
          notes
        }
      })
    } catch (error) {
      console.error('Error completing appointment:', error)
      return null
    }
  }

  // Get appointment statistics
  static async getAppointmentStats(userId: string) {
    try {
      const [
        total,
        upcoming,
        completed,
        cancelled
      ] = await Promise.all([
        prisma.appointment.count({
          where: { userId }
        }),
        prisma.appointment.count({
          where: { 
            userId,
            date: {
              gte: new Date()
            },
            status: {
              in: ['SCHEDULED', 'CONFIRMED']
            }
          }
        }),
        prisma.appointment.count({
          where: { 
            userId,
            status: 'COMPLETED'
          }
        }),
        prisma.appointment.count({
          where: { 
            userId,
            status: 'CANCELLED'
          }
        })
      ])

      return {
        total,
        upcoming,
        completed,
        cancelled
      }
    } catch (error) {
      console.error('Error fetching appointment stats:', error)
      return {
        total: 0,
        upcoming: 0,
        completed: 0,
        cancelled: 0
      }
    }
  }
}
