import Link from "next/link";
import Image from "next/image";
import { Calendar, Clock, Play, ArrowRight } from "lucide-react";

interface WebinarCardProps {
  title: string;
  description: string;
  thumbnail: string;
  date: string;
  duration: string;
  href: string;
  isUpcoming?: boolean;
}

function WebinarCard({ title, description, thumbnail, date, duration, href, isUpcoming = false }: WebinarCardProps) {
  return (
    <div className="rounded-xl overflow-hidden shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl"
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}>
      {/* Thumbnail */}
      <div className="relative h-48">
        <Image
          src={thumbnail}
          alt={title}
          fill
          className="object-cover"
        />
        {!isUpcoming && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/30 opacity-0 hover:opacity-100 transition-opacity">
            <div className="w-14 h-14 rounded-full bg-white/90 flex items-center justify-center">
              <Play className="h-6 w-6 text-indigo-700 ml-1" />
            </div>
          </div>
        )}
        {isUpcoming && (
          <div className="absolute top-3 right-3 bg-indigo-600 text-white px-3 py-1 rounded-full text-xs font-medium">
            Upcoming
          </div>
        )}
      </div>
      
      {/* Content */}
      <div className="p-5">
        <div className="flex items-center text-xs text-gray-500 mb-3">
          <Calendar className="h-3 w-3 mr-1" />
          <span>{date}</span>
          <span className="mx-2">•</span>
          <Clock className="h-3 w-3 mr-1" />
          <span>{duration}</span>
        </div>
        
        <h3 className="text-lg font-semibold text-indigo-800 mb-2">{title}</h3>
        <p className="text-sm text-gray-600 mb-4">
          {description}
        </p>
        <Link href={href as any} className="text-indigo-600 font-medium flex items-center">
          {isUpcoming ? "Register Now" : "Watch Recording"}
          <ArrowRight size={16} className="ml-1" />
        </Link>
      </div>
    </div>
  );
}

export default function WebinarsPage() {
  const pastWebinars = [
    {
      title: "Navigating US Student Visa Options",
      description: "Learn about F-1, J-1, and M-1 visas, application timelines, and maintaining visa status while studying in the US.",
      thumbnail: "/placeholder.svg?height=400&width=600",
      date: "Oct 15, 2023",
      duration: "45 minutes",
      href: "#student-visa-webinar",
    },
    {
      title: "UK Points-Based Immigration System",
      description: "A detailed breakdown of the UK's points-based system for skilled workers, students, and entrepreneurs.",
      thumbnail: "/placeholder.svg?height=400&width=600",
      date: "Sep 28, 2023",
      duration: "60 minutes",
      href: "#uk-points-webinar",
    },
    {
      title: "Canadian Express Entry Masterclass",
      description: "Expert tips on maximizing your CRS score and navigating the Express Entry system for Canadian permanent residency.",
      thumbnail: "/placeholder.svg?height=400&width=600",
      date: "Aug 12, 2023",
      duration: "75 minutes",
      href: "#canada-express-webinar",
    },
  ];

  const upcomingWebinars = [
    {
      title: "Australia's New Skilled Migration Program",
      description: "Updates on recent changes to Australia's skilled migration pathways and how they might affect your application.",
      thumbnail: "/placeholder.svg?height=400&width=600",
      date: "Nov 10, 2023",
      duration: "60 minutes",
      href: "#australia-skilled-webinar",
      isUpcoming: true,
    },
    {
      title: "Digital Nomad Visas Around the World",
      description: "Explore countries offering digital nomad visas, requirements, benefits, and application processes.",
      thumbnail: "/placeholder.svg?height=400&width=600",
      date: "Nov 25, 2023",
      duration: "45 minutes",
      href: "#digital-nomad-webinar",
      isUpcoming: true,
    }
  ];

  return (
    <div>
      <h2 className="text-xl font-semibold text-indigo-800 mb-6">Immigration Webinars</h2>
      
      <p className="text-gray-600 mb-8">
        Expand your knowledge with our expert-led webinars covering various visa types,
        immigration pathways, and strategies for successful applications. Register for upcoming
        sessions or watch recordings of past webinars.
      </p>
      
      {/* Upcoming Webinars */}
      <div className="mb-12">
        <h3 className="text-lg font-semibold text-indigo-800 mb-4">Upcoming Webinars</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {upcomingWebinars.map((webinar, index) => (
            <WebinarCard 
              key={index}
              title={webinar.title}
              description={webinar.description}
              thumbnail={webinar.thumbnail}
              date={webinar.date}
              duration={webinar.duration}
              href={webinar.href}
              isUpcoming={webinar.isUpcoming}
            />
          ))}
        </div>
      </div>
      
      {/* Past Webinars */}
      <div>
        <h3 className="text-lg font-semibold text-indigo-800 mb-4">Past Webinars</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {pastWebinars.map((webinar, index) => (
            <WebinarCard 
              key={index}
              title={webinar.title}
              description={webinar.description}
              thumbnail={webinar.thumbnail}
              date={webinar.date}
              duration={webinar.duration}
              href={webinar.href}
            />
          ))}
        </div>
      </div>
      
      {/* Subscribe section */}
      <div className="mt-12 p-6 rounded-xl bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-100">
        <h3 className="text-lg font-semibold text-indigo-800 mb-3">Never Miss a Webinar</h3>
        <p className="text-gray-600 mb-4">
          Subscribe to our newsletter to receive notifications about upcoming webinars
          and get access to exclusive immigration resources.
        </p>
        <div className="flex flex-col sm:flex-row gap-3">
          <input 
            type="email" 
            placeholder="Enter your email" 
            className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
          <button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md">
            Subscribe
          </button>
        </div>
      </div>
    </div>
  );
} 