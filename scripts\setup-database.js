const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up visa-mentor database...\n');

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env.local file not found!');
  console.log('Please create .env.local with DATABASE_URL configuration.');
  process.exit(1);
}

// Read and check DATABASE_URL
const envContent = fs.readFileSync(envPath, 'utf8');
const dbUrlMatch = envContent.match(/DATABASE_URL="([^"]+)"/);

if (!dbUrlMatch) {
  console.error('❌ DATABASE_URL not found in .env.local!');
  console.log('Please add DATABASE_URL="mysql://root:@localhost:3306/visa_mentor" to .env.local');
  process.exit(1);
}

console.log('✅ Environment configuration found');
console.log('📊 Database URL:', dbUrlMatch[1].replace(/:[^:@]*@/, ':***@'));

try {
  console.log('\n📦 Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  
  console.log('\n🔄 Running database migrations...');
  execSync('npx prisma migrate deploy', { stdio: 'inherit' });
  
  console.log('\n🌱 Seeding database with sample data...');
  execSync('npx tsx scripts/seed-database.ts', { stdio: 'inherit' });
  
  console.log('\n🎉 Database setup completed successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Make sure XAMPP MySQL is running on port 3306');
  console.log('2. Start the development server: npm run dev');
  console.log('3. Visit http://localhost:3000 and sign in');
  console.log('4. Your dashboard will now show real data from the database!');
  
} catch (error) {
  console.error('\n❌ Database setup failed:', error.message);
  console.log('\n🔧 Troubleshooting:');
  console.log('1. Make sure XAMPP is running and MySQL is started');
  console.log('2. Verify the database "visa_mentor" exists');
  console.log('3. Check that the DATABASE_URL in .env.local is correct');
  console.log('4. Try running: npx prisma migrate reset --force');
  process.exit(1);
}
