import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { AppointmentService } from '@/lib/services/appointmentService';
import { UserService } from '@/lib/services/userService';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get appointments for user
    const appointments = await AppointmentService.getUserAppointments(user.id);

    return NextResponse.json({ appointments });
  } catch (error) {
    console.error('API: Error fetching appointments:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const data = await request.json();

    if (!data.title || !data.date || !data.time) {
      return NextResponse.json({ 
        error: 'Title, date, and time are required' 
      }, { status: 400 });
    }

    // Create new appointment
    const appointment = await AppointmentService.createAppointment({
      userId: user.id,
      ...data,
      date: new Date(data.date),
    });

    return NextResponse.json({ appointment }, { status: 201 });
  } catch (error) {
    console.error('API: Error creating appointment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
