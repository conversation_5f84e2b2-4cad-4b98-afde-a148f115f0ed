"use client";

// @ts-ignore - Temporarily disable TypeScript checking for this file
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowRight, Calendar, FileText, MessageSquare, Clock } from "lucide-react"
import GlassCard from "@/components/GlassCard";
import type { Route } from 'next';
import RoleBasedAccess from "@/components/auth/RoleBasedAccess";

export default function DashboardPage() {
  const applications = [
    {
      id: 1,
      type: "Student Visa - USA",
      status: "In Progress",
      progress: 65,
      lastUpdated: "2 days ago",
    },
    {
      id: 2,
      type: "University Application - Stanford",
      status: "Under Review",
      progress: 80,
      lastUpdated: "1 week ago",
    },
  ]

  const appointments = [
    {
      id: 1,
      title: "Visa Interview Preparation",
      date: "May 15, 2025",
      time: "10:00 AM",
      counselor: "<PERSON><PERSON>",
    },
    {
      id: 2,
      title: "Document Review Session",
      date: "May 20, 2025",
      time: "2:30 PM",
      counselor: "<PERSON><PERSON>",
    },
  ]

  const tasks = [
    {
      id: 1,
      title: "Submit financial documents",
      dueDate: "May 14, 2025",
      priority: "High",
    },
    {
      id: 2,
      title: "Complete SOP draft",
      dueDate: "May 18, 2025",
      priority: "Medium",
    },
    {
      id: 3,
      title: "Prepare for mock interview",
      dueDate: "May 22, 2025",
      priority: "High",
    },
  ]

  return (
    <RoleBasedAccess allowedRoles={['admin', 'sales-manager', 'sales-executive', 'crm', 'hr', 'user']}>
      <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Welcome back! Here's an overview of your applications and upcoming tasks.
          </p>
        </div>
        <Button 
          className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
          asChild
        >
          <Link href={"/dashboard/applications/new" as Route}>Start New Application</Link>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <GlassCard className="transform transition hover:translate-y-[-4px]">
          <div className="p-6">
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium text-indigo-800">Active Applications</h3>
              <div className="bg-indigo-50/50 p-2 rounded-full">
                <FileText className="h-4 w-4 text-indigo-600" />
              </div>
            </div>
            <div className="text-2xl font-bold text-indigo-800">{applications.length}</div>
            <p className="text-xs text-gray-600">+0 from last month</p>
          </div>
        </GlassCard>
        
        <GlassCard className="transform transition hover:translate-y-[-4px]">
          <div className="p-6">
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium text-indigo-800">Upcoming Appointments</h3>
              <div className="bg-indigo-50/50 p-2 rounded-full">
                <Calendar className="h-4 w-4 text-indigo-600" />
              </div>
            </div>
            <div className="text-2xl font-bold text-indigo-800">{appointments.length}</div>
            <p className="text-xs text-gray-600">Next: {appointments[0].date}</p>
          </div>
        </GlassCard>
        
        <GlassCard className="transform transition hover:translate-y-[-4px]">
          <div className="p-6">
            <div className="flex flex-row items-center justify-between pb-2">
              <h3 className="text-sm font-medium text-indigo-800">Pending Tasks</h3>
              <div className="bg-indigo-50/50 p-2 rounded-full">
                <Clock className="h-4 w-4 text-indigo-600" />
              </div>
            </div>
            <div className="text-2xl font-bold text-indigo-800">{tasks.length}</div>
            <p className="text-xs text-gray-600">
              {tasks.filter((t) => t.priority === "High").length} high priority
            </p>
          </div>
        </GlassCard>
      </div>

      <div>
        <Tabs defaultValue="applications" className="space-y-4">
          <TabsList className="bg-white/70 backdrop-blur-sm border border-indigo-100/30 p-1">
            <TabsTrigger 
              value="applications" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
            >
              Applications
            </TabsTrigger>
            <TabsTrigger 
              value="appointments" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
            >
              Appointments
            </TabsTrigger>
            <TabsTrigger 
              value="tasks" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
            >
              Tasks
            </TabsTrigger>
          </TabsList>

          <TabsContent value="applications" className="space-y-4">
            <div className="grid gap-4">
              {applications.map((app) => (
                <GlassCard key={app.id} className="transform transition hover:translate-y-[-2px]">
                  <div className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-semibold text-indigo-800">{app.type}</h3>
                        <p className="text-sm text-gray-600">Status: {app.status}</p>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-600 mr-4">{app.lastUpdated}</div>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50/70"
                          asChild
                        >
                          <Link href={`/dashboard/applications/${app.id}`}>
                            View <ArrowRight className="ml-1 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                    <div className="flex flex-col gap-2 mt-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Progress</span>
                        <span className="text-indigo-800 font-medium">{app.progress}%</span>
                      </div>
                      <div className="h-2 bg-indigo-100/40 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-indigo-600 to-blue-400 rounded-full"
                          style={{ width: `${app.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </GlassCard>
              ))}
            </div>
            <div className="flex justify-center">
              <Button 
                variant="outline"
                className="border border-indigo-300 rounded-lg bg-white/80 backdrop-blur-sm text-indigo-600 hover:bg-indigo-50/70"
                asChild
              >
                <Link href={"/dashboard/applications" as Route}>View All Applications</Link>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="appointments" className="space-y-4">
            <div className="grid gap-4">
              {appointments.map((appointment) => (
                <GlassCard key={appointment.id} className="transform transition hover:translate-y-[-2px]">
                  <div className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-semibold text-indigo-800">{appointment.title}</h3>
                        <p className="text-sm text-gray-600">With {appointment.counselor}</p>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-indigo-600 font-medium mr-4">{appointment.time}</div>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50/70"
                          asChild
                        >
                          <Link href={`/dashboard/appointments/${appointment.id}`}>
                            View <ArrowRight className="ml-1 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 mt-4 text-sm text-gray-600">
                      <div className="bg-indigo-50/70 p-1.5 rounded-full">
                        <Calendar className="h-4 w-4 text-indigo-600" />
                      </div>
                      <span>{appointment.date}</span>
                    </div>
                  </div>
                </GlassCard>
              ))}
            </div>
            <div className="flex justify-center">
              <Button 
                variant="outline"
                className="border border-indigo-300 rounded-lg bg-white/80 backdrop-blur-sm text-indigo-600 hover:bg-indigo-50/70"
                asChild
              >
                <Link href={"/dashboard/appointments" as Route}>View All Appointments</Link>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="tasks" className="space-y-4">
            <div className="grid gap-4">
              {tasks.map((task) => (
                <GlassCard key={task.id} className="transform transition hover:translate-y-[-2px]">
                  <div className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-semibold text-indigo-800">{task.title}</h3>
                        <div className="flex items-center mt-1">
                          <Clock className="h-4 w-4 text-gray-600 mr-1" />
                          <p className="text-sm text-gray-600">Due: {task.dueDate}</p>
                        </div>
                      </div>
                      <div
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          task.priority === "High"
                            ? "bg-red-100/70 backdrop-blur-sm text-red-800"
                            : task.priority === "Medium"
                              ? "bg-amber-100/70 backdrop-blur-sm text-amber-800"
                              : "bg-green-100/70 backdrop-blur-sm text-green-800"
                        }`}
                      >
                        {task.priority}
                      </div>
                    </div>
                    <div className="flex justify-end mt-4">
                      <Button 
                        size="sm" 
                        className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg py-1 px-3 text-sm font-medium hover:from-indigo-700 hover:to-blue-700 transition-colors shadow-sm"
                      >
                        Mark as Complete
                      </Button>
                    </div>
                  </div>
                </GlassCard>
              ))}
            </div>
            <div className="flex justify-center">
              <Button 
                variant="outline"
                className="border border-indigo-300 rounded-lg bg-white/80 backdrop-blur-sm text-indigo-600 hover:bg-indigo-50/70"
                asChild
              >
                <Link href={"/dashboard/tasks" as Route}>View All Tasks</Link>
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <GlassCard className="transform transition hover:translate-y-[-2px]">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-indigo-800 mb-2">Recent Messages</h2>
          <p className="text-sm text-gray-600 mb-4">Stay in touch with your counselors and support team</p>
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="bg-gradient-to-br from-indigo-100 to-blue-50 p-3 rounded-full flex-shrink-0">
                <MessageSquare className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold text-indigo-800">Rajiv Sharma</h4>
                  <span className="text-xs text-gray-600">2 hours ago</span>
                </div>
                <p className="text-sm text-gray-600">
                  I've reviewed your documents and everything looks good. Let's discuss the next steps in our
                  appointment.
                </p>
              </div>
            </div>
            <div className="flex items-start gap-4">
              <div className="bg-gradient-to-br from-indigo-100 to-blue-50 p-3 rounded-full flex-shrink-0">
                <MessageSquare className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold text-indigo-800">Support Team</h4>
                  <span className="text-xs text-gray-600">1 day ago</span>
                </div>
                <p className="text-sm text-gray-600">
                  Your appointment has been confirmed for May 15th at 10:00 AM. Please make sure to prepare all required
                  documents.
                </p>
              </div>
            </div>
          </div>
          <div className="mt-6 flex justify-center">
            <Button 
              variant="outline"
              className="border border-indigo-300 rounded-lg bg-white/80 backdrop-blur-sm text-indigo-600 hover:bg-indigo-50/70"
              asChild
            >
              <Link href={"/dashboard/messages" as Route}>View All Messages</Link>
            </Button>
          </div>
        </div>
      </GlassCard>
      </div>
    </RoleBasedAccess>
  )
}
