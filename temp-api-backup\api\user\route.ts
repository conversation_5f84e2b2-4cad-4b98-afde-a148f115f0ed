import { NextRequest, NextResponse } from "next/server"

// In a real app, this would be stored in a database
let userData = {
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "+****************",
  location: "New York, USA",
  memberSince: "May 2024",
  membershipTier: "Premium",
  profilePicture: "", // No picture in the mock data
}

export async function GET() {
  // Simulate a slight delay like a real API might have
  await new Promise(resolve => setTimeout(resolve, 300))

  return NextResponse.json(userData)
}

export async function PUT(request: NextRequest) {
  try {
    const updates = await request.json()
    
    // Validate the updates (basic validation)
    if (updates.name && typeof updates.name !== 'string') {
      return NextResponse.json({ error: 'Invalid name format' }, { status: 400 })
    }
    if (updates.email && typeof updates.email !== 'string') {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 })
    }
    
    // Update the user data (in a real app, this would update a database)
    userData = { ...userData, ...updates }
    
    // Simulate a delay like a real API might have
    await new Promise(resolve => setTimeout(resolve, 300))
    
    return NextResponse.json(userData)
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Failed to update user data' }, { status: 500 })
  }
} 