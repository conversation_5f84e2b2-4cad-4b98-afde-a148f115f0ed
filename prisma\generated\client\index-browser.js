
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.1
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.1",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  password: 'password',
  image: 'image',
  phone: 'phone',
  country: 'country',
  status: 'status',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastLogin: 'lastLogin'
};

exports.Prisma.ProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  address: 'address',
  city: 'city',
  state: 'state',
  zipCode: 'zipCode',
  dateOfBirth: 'dateOfBirth',
  passportNumber: 'passportNumber',
  passportExpiry: 'passportExpiry',
  nationality: 'nationality',
  occupation: 'occupation',
  travelHistory: 'travelHistory',
  bio: 'bio',
  preferences: 'preferences',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VisaApplicationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  applicationType: 'applicationType',
  destinationCountry: 'destinationCountry',
  purpose: 'purpose',
  plannedArrival: 'plannedArrival',
  plannedDeparture: 'plannedDeparture',
  status: 'status',
  stage: 'stage',
  submissionDate: 'submissionDate',
  decisionDate: 'decisionDate',
  rejectionReason: 'rejectionReason',
  processingTime: 'processingTime',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  applicationId: 'applicationId',
  filename: 'filename',
  originalName: 'originalName',
  path: 'path',
  size: 'size',
  mimetype: 'mimetype',
  documentType: 'documentType',
  status: 'status',
  reviewNotes: 'reviewNotes',
  reviewedBy: 'reviewedBy',
  reviewedAt: 'reviewedAt',
  uploadedAt: 'uploadedAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BookingScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  counselorId: 'counselorId',
  name: 'name',
  email: 'email',
  phone: 'phone',
  country: 'country',
  visaType: 'visaType',
  date: 'date',
  time: 'time',
  notes: 'notes',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AdminActionScalarFieldEnum = {
  id: 'id',
  adminId: 'adminId',
  userId: 'userId',
  action: 'action',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.TravelRecommendationScalarFieldEnum = {
  id: 'id',
  destinationCountry: 'destinationCountry',
  attractionName: 'attractionName',
  attractionType: 'attractionType',
  image: 'image',
  address: 'address',
  description: 'description',
  historicalSignificance: 'historicalSignificance',
  bestTimeToVisit: 'bestTimeToVisit',
  entranceFee: 'entranceFee',
  openingHours: 'openingHours',
  website: 'website',
  localTips: 'localTips',
  weatherInfo: 'weatherInfo',
  safetyTips: 'safetyTips',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SettingScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  updatedBy: 'updatedBy',
  updatedAt: 'updatedAt'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  content: 'content',
  userId: 'userId',
  sender: 'sender',
  read: 'read',
  attachments: 'attachments',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  senderId: 'senderId',
  receiverId: 'receiverId'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  image: 'image',
  phone: 'phone',
  country: 'country',
  status: 'status',
  role: 'role'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.ProfileOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  address: 'address',
  city: 'city',
  state: 'state',
  zipCode: 'zipCode',
  passportNumber: 'passportNumber',
  nationality: 'nationality',
  occupation: 'occupation',
  bio: 'bio'
};

exports.Prisma.VisaApplicationOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  applicationType: 'applicationType',
  destinationCountry: 'destinationCountry',
  purpose: 'purpose',
  status: 'status',
  rejectionReason: 'rejectionReason',
  notes: 'notes'
};

exports.Prisma.DocumentOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  applicationId: 'applicationId',
  filename: 'filename',
  originalName: 'originalName',
  path: 'path',
  mimetype: 'mimetype',
  documentType: 'documentType',
  status: 'status',
  reviewNotes: 'reviewNotes',
  reviewedBy: 'reviewedBy'
};

exports.Prisma.BookingOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  counselorId: 'counselorId',
  name: 'name',
  email: 'email',
  phone: 'phone',
  country: 'country',
  visaType: 'visaType',
  time: 'time',
  notes: 'notes',
  status: 'status'
};

exports.Prisma.AdminActionOrderByRelevanceFieldEnum = {
  adminId: 'adminId',
  userId: 'userId',
  action: 'action'
};

exports.Prisma.TravelRecommendationOrderByRelevanceFieldEnum = {
  id: 'id',
  destinationCountry: 'destinationCountry',
  attractionName: 'attractionName',
  attractionType: 'attractionType',
  image: 'image',
  address: 'address',
  description: 'description',
  historicalSignificance: 'historicalSignificance',
  bestTimeToVisit: 'bestTimeToVisit',
  entranceFee: 'entranceFee',
  openingHours: 'openingHours',
  website: 'website',
  localTips: 'localTips',
  weatherInfo: 'weatherInfo',
  safetyTips: 'safetyTips'
};

exports.Prisma.SettingOrderByRelevanceFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  updatedBy: 'updatedBy'
};

exports.Prisma.MessageOrderByRelevanceFieldEnum = {
  id: 'id',
  content: 'content',
  userId: 'userId',
  sender: 'sender',
  attachments: 'attachments',
  senderId: 'senderId',
  receiverId: 'receiverId'
};


exports.Prisma.ModelName = {
  User: 'User',
  Profile: 'Profile',
  VisaApplication: 'VisaApplication',
  Document: 'Document',
  Booking: 'Booking',
  AdminAction: 'AdminAction',
  TravelRecommendation: 'TravelRecommendation',
  Setting: 'Setting',
  Message: 'Message'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
