/**
 * App routes type definitions for Next.js typed routes
 */

interface StaticRoutes {
  "/": never;
  "/login": never;
  "/about": never;
  "/contact": never;
  "/services": never;
  "/resources": never;
  "/resources/guides": never;
  "/resources/webinars": never;
  "/resources/faqs": never;
  "/unauthorized": never;
  
  // Admin routes
  "/admin": never;
  "/admin/dashboard": never;
  "/admin/users": never;
  "/admin/settings": never;
  
  // Sales executive routes
  "/sales-executive": never;
  "/sales-executive/dashboard": never;
  "/sales-executive/leads": never;
  "/sales-executive/documents": never;
  "/sales-executive/communications": never;
  "/sales-executive/calendar": never;
  "/sales-executive/analytics": never;
  "/sales-executive/settings": never;
  
  // Sales manager routes
  "/sales-manager": never;
  "/sales-manager/dashboard": never;
  "/sales-manager/team": never;
  "/sales-manager/performance": never;
  "/sales-manager/reports": never;
  "/sales-manager/settings": never;
  
  // CRM routes
  "/crm": never;
  "/crm/overview": never;
  "/crm/clients": never;
  "/crm/lifecycle": never;
  "/crm/engagement": never;
  "/crm/risk": never;
  "/crm/referrals": never;
  "/crm/analytics": never;
  "/crm/settings": never;
  
  // HR routes
  "/hr": never;
  "/hr/dashboard": never;
  "/hr/talent-hub": never;
  "/hr/team-analytics": never;
  "/hr/regulatory-center": never;
  "/hr/skill-development": never;
  "/hr/settings": never;
  
  // User routes
  "/user": never;
  "/user/dashboard": never;
  "/user/applications": never;
  "/user/documents": never;
  "/user/messages": never;
  "/user/appointments": never;
  "/user/profile": never;
}

/**
 * Dynamic route patterns for the application
 */
type DynamicRoutes<T extends string> = 
  `/user/applications/${T}` |
  `/crm/clients/${T}` |
  `/crm/clients/${T}/edit` |
  `/sales-executive/leads/${T}`;

/**
 * Combined route types for the entire application
 */
export type AppRoutes = keyof StaticRoutes | DynamicRoutes<string>; 