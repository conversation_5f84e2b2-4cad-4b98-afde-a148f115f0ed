import React, { useState } from 'react';
import Image from 'next/image';
import { MapPin, Calendar, Bookmark, BookmarkCheck, ChevronDown, ChevronUp, ExternalLink, Clock, Info, History } from 'lucide-react';

export interface TravelRecommendation {
  id: string;
  destinationCountry: string;
  attractionName: string;
  attractionType: string;
  image: string;
  address: string;
  description: string;
  historicalSignificance: string;
  bestTimeToVisit: string;
  entranceFee: string;
  openingHours: string;
  website?: string | null;
  localTips?: string | null;
  weatherInfo?: string | null;
  safetyTips?: string | null;
  isBookmarked: boolean;
}

interface TravelRecommendationCardProps {
  recommendation: TravelRecommendation;
  onToggleBookmark: (id: string, value: boolean) => void;
}

const TravelRecommendationCard: React.FC<TravelRecommendationCardProps> = ({ 
  recommendation, 
  onToggleBookmark 
}) => {
  const [expanded, setExpanded] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  const {
    id,
    destinationCountry,
    attractionName,
    attractionType,
    image,
    address,
    description,
    historicalSignificance,
    bestTimeToVisit,
    entranceFee,
    openingHours,
    website,
    localTips,
    weatherInfo,
    safetyTips,
    isBookmarked
  } = recommendation;

  // Get country name from country code
  const getCountryName = (code: string) => {
    const countries: Record<string, string> = {
      'US': 'United States',
      'GB': 'United Kingdom',
      'IN': 'India',
      'CA': 'Canada',
      'AU': 'Australia'
    };
    
    return countries[code] || code;
  };

  return (
    <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col w-full border border-indigo-50 group">
      <div className="relative">
        {image && !imageError ? (
          <div className="h-40 w-full relative overflow-hidden">
            <Image 
              src={image} 
              alt={attractionName}
              width={400}
              height={200}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              style={{ objectPosition: "center" }}
              priority={true}
              onError={() => setImageError(true)}
              unoptimized // For external URLs
            />
            <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/70 to-transparent"></div>
          </div>
        ) : (
          <div className="h-40 bg-gradient-to-r from-indigo-500 to-purple-600 flex justify-center items-center">
            <MapPin className="w-10 h-10 text-white" />
          </div>
        )}
        
        <div className="absolute top-3 right-3">
          <button 
            onClick={() => onToggleBookmark(id, !isBookmarked)}
            className="p-1.5 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors transform hover:scale-105"
            aria-label={isBookmarked ? "Remove bookmark" : "Add bookmark"}
          >
            {isBookmarked ? 
              <BookmarkCheck className="w-4 h-4 text-indigo-600" /> : 
              <Bookmark className="w-4 h-4 text-gray-500" />
            }
          </button>
        </div>
        
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
          <div className="text-white font-medium text-xs px-2 py-1 bg-indigo-600/90 backdrop-blur-sm rounded-full inline-block">
            {getCountryName(destinationCountry)}
          </div>
        </div>
      </div>
      
      <div className="p-3.5 flex flex-col flex-grow">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-bold text-indigo-900 text-sm">{attractionName}</h3>
            <p className="text-xs text-indigo-600 mt-0.5">{attractionType}</p>
          </div>
          <div className="text-right">
            <p className="font-semibold text-indigo-700 bg-indigo-100 px-2 py-1 rounded-full text-xs">{entranceFee}</p>
          </div>
        </div>
        
        <div className="mt-2 pt-2 border-t border-indigo-100">
          <div className="flex items-center text-xs text-gray-700 mb-1.5 bg-indigo-50/60 p-1.5 px-2 rounded-lg">
            <MapPin className="w-3.5 h-3.5 mr-1.5 text-indigo-600 flex-shrink-0" />
            <span className="truncate text-xs">{address}</span>
          </div>
          
          <div className="flex items-center text-xs text-gray-700 mb-1.5 bg-indigo-50/60 p-1.5 px-2 rounded-lg">
            <Clock className="w-3.5 h-3.5 mr-1.5 text-indigo-600 flex-shrink-0" />
            <span className="truncate text-xs">{openingHours}</span>
          </div>
          
          <div className="flex items-center text-xs text-gray-700 bg-indigo-50/60 p-1.5 px-2 rounded-lg">
            <Calendar className="w-3.5 h-3.5 mr-1.5 text-indigo-600 flex-shrink-0" />
            <span className="truncate text-xs">Best time: {bestTimeToVisit}</span>
          </div>
        </div>
        
        <div 
          className={`overflow-hidden transition-all duration-300 ${expanded ? 'max-h-[800px] opacity-100 mt-3' : 'max-h-0 opacity-0'}`}
        >
          {expanded && (
            <div className="pt-2 border-t border-indigo-100 text-xs">
              <div className="mb-3 bg-white p-2.5 rounded-lg shadow-sm">
                <h4 className="font-semibold text-indigo-800 mb-1 text-xs">Description</h4>
                <p className="text-gray-700 text-xs">{description}</p>
              </div>
              
              <div className="mb-3 bg-white p-2.5 rounded-lg shadow-sm">
                <h4 className="font-semibold text-indigo-800 mb-1 flex items-center text-xs">
                  <History className="w-3 h-3 mr-1" /> Historical Significance
                </h4>
                <p className="text-gray-700 text-xs">{historicalSignificance}</p>
              </div>
              
              {localTips && (
                <div className="mb-3 bg-white p-2.5 rounded-lg shadow-sm">
                  <h4 className="font-semibold text-indigo-800 mb-1 text-xs">Local Tips</h4>
                  <p className="text-gray-700 text-xs">{localTips}</p>
                </div>
              )}
              
              {weatherInfo && (
                <div className="mb-3 bg-white p-2.5 rounded-lg shadow-sm">
                  <h4 className="font-semibold text-indigo-800 mb-1 text-xs">Weather</h4>
                  <p className="text-gray-700 text-xs">{weatherInfo}</p>
                </div>
              )}
              
              {safetyTips && (
                <div className="mb-3 bg-white p-2.5 rounded-lg shadow-sm">
                  <h4 className="font-semibold text-indigo-800 mb-1 text-xs">Safety Tips</h4>
                  <p className="text-gray-700 text-xs">{safetyTips}</p>
                </div>
              )}
              
              {website && (
                <a 
                  href={website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-indigo-600 hover:text-indigo-800 mt-1 font-medium text-xs bg-indigo-50 px-2.5 py-1.5 rounded-full transition-colors"
                >
                  Visit official website <ExternalLink className="w-3 h-3 ml-1" />
                </a>
              )}
            </div>
          )}
        </div>
        
        <div className="mt-auto pt-2">
          <button 
            onClick={() => setExpanded(!expanded)}
            className="w-full pt-1.5 flex items-center justify-center text-xs text-indigo-600 hover:text-indigo-800 border-t border-indigo-100 bg-indigo-50/50 rounded-b-lg p-1.5 font-medium transition-colors"
          >
            {expanded ? (
              <>Show less <ChevronUp className="w-3.5 h-3.5 ml-1" /></>
            ) : (
              <>Show more <ChevronDown className="w-3.5 h-3.5 ml-1" /></>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TravelRecommendationCard; 