"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

const routeLabels: Record<string, string> = {
  // Admin routes
  "/admin": "Admin Dashboard",
  "/admin/users": "User Management",
  "/admin/applications": "Visa Applications", 
  "/admin/documents": "Document Verification",
  "/admin/communications": "Communications",
  "/admin/analytics": "Analytics",
  "/admin/compliance": "Compliance",
  "/admin/settings": "Settings",
  
  // CRM routes
  "/crm": "CRM Dashboard",
  "/crm/clients": "Clients",
  "/crm/lifecycle": "Lifecycle",
  "/crm/engagement": "Engagement",
  "/crm/risk": "Risk Management",
  "/crm/referrals": "Referrals",
  "/crm/analytics": "Analytics",
  "/crm/settings": "Settings",
  
  // Sales Manager routes
  "/sales-manager": "Sales Manager Dashboard",
  "/sales-manager/team": "Team Overview",
  "/sales-manager/analytics": "Performance Analytics",
  "/sales-manager/leads": "Lead Distribution",
  "/sales-manager/compliance": "Compliance Review",
  "/sales-manager/calendar": "Calendar",
  "/sales-manager/settings": "Settings",
  
  // Sales Executive routes
  "/sales-executive": "Sales Executive Dashboard",
  "/sales-executive/leads": "Leads",
  "/sales-executive/documents": "Documents",
  "/sales-executive/communications": "Communications",
  "/sales-executive/calendar": "Calendar",
  "/sales-executive/analytics": "Analytics",
  "/sales-executive/settings": "Settings",
  
  // HR routes
  "/hr": "HR Dashboard",
  "/hr/talent": "Talent Hub",
  "/hr/performance": "Team Analytics",
  "/hr/compliance": "Regulatory Center",
  "/hr/learning": "Skill Development",
  "/hr/settings": "Settings",
  

  
  // User routes
  "/user": "User Portal",
  "/user/dashboard": "Dashboard",
  "/user/documents": "Documents",
  "/user/chat": "Chat",
  "/user/profile-setup": "Profile Setup",
  "/user/schedule": "Schedule",
  
  // Public routes
  "/": "Home",
  "/about": "About Us",
  "/contact": "Contact",
  "/services": "Services",
  "/resources": "Resources",
  "/resources/guides": "Guides",
  "/resources/webinars": "Webinars",
  "/resources/faqs": "FAQs",
}

function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = []
  
  // Always start with home for dashboard routes
  if (pathname !== '/') {
    breadcrumbs.push({
      label: "Home",
      href: "/",
    })
  }
  
  // Build breadcrumbs from path segments
  let currentPath = ''
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const isLast = index === segments.length - 1
    
    const label = routeLabels[currentPath] || segment.charAt(0).toUpperCase() + segment.slice(1)
    
    breadcrumbs.push({
      label,
      href: isLast ? undefined : currentPath,
      current: isLast
    })
  })
  
  return breadcrumbs
}

interface BreadcrumbProps {
  className?: string
  showHome?: boolean
}

export default function Breadcrumb({ className, showHome = true }: BreadcrumbProps) {
  const pathname = usePathname()
  
  // Don't show breadcrumbs on home page
  if (pathname === '/') {
    return null
  }
  
  const breadcrumbs = generateBreadcrumbs(pathname)
  
  // Filter out home if showHome is false
  const filteredBreadcrumbs = showHome ? breadcrumbs : breadcrumbs.slice(1)
  
  if (filteredBreadcrumbs.length <= 1) {
    return null
  }
  
  return (
    <nav className={cn("flex items-center space-x-1 text-sm text-gray-600", className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {filteredBreadcrumbs.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />
            )}
            {item.current ? (
              <span className="font-medium text-gray-900" aria-current="page">
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href!}
                className="hover:text-gray-900 transition-colors"
              >
                {index === 0 && showHome ? (
                  <Home className="h-4 w-4" />
                ) : (
                  item.label
                )}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

// Compact version for smaller spaces
export function BreadcrumbCompact({ className }: { className?: string }) {
  const pathname = usePathname()
  
  if (pathname === '/') {
    return null
  }
  
  const breadcrumbs = generateBreadcrumbs(pathname)
  const current = breadcrumbs[breadcrumbs.length - 1]
  const parent = breadcrumbs[breadcrumbs.length - 2]
  
  if (!parent) {
    return (
      <div className={cn("text-sm font-medium text-gray-900", className)}>
        {current.label}
      </div>
    )
  }
  
  return (
    <nav className={cn("flex items-center space-x-1 text-sm text-gray-600", className)}>
      <Link href={parent.href!} className="hover:text-gray-900 transition-colors">
        {parent.label}
      </Link>
      <ChevronRight className="h-4 w-4 text-gray-400" />
      <span className="font-medium text-gray-900">{current.label}</span>
    </nav>
  )
}
