"use client";

import { useState } from "react";
import {
  Save,
  User,
  Mail,
  Phone,
  Shield,
  Bell,
  Globe,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  Camera,
  ClipboardList,
  Lock,
  Smartphone,
  Eye,
  EyeOff,
  ArrowRight,
  RefreshCw,
  Moon,
  Sun,
  BellOff
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock user data
const userData = {
  name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "+91 98765 43210",
  role: "Senior Visa Counselor",
  avatar: "/avatars/priya.jpg",
  joinDate: "May 15, 2022",
  twoFactorEnabled: true,
  notificationsEnabled: {
    email: true,
    push: true,
    sms: false
  },
  language: "English",
  timezone: "Asia/Kolkata",
  dateFormat: "DD/MM/YYYY",
  timeFormat: "12h"
};

// Security activity logs
const securityLogs = [
  {
    action: "Login successful",
    device: "Windows PC - Chrome Browser",
    location: "Bangalore, India",
    time: "Today at 9:15 AM",
    status: "success"
  },
  {
    action: "Password changed",
    device: "Windows PC - Chrome Browser",
    location: "Bangalore, India",
    time: "May 15, 2023",
    status: "success"
  },
  {
    action: "Login attempt",
    device: "Unknown device - Firefox Browser",
    location: "Mumbai, India",
    time: "May 10, 2023",
    status: "warning"
  },
  {
    action: "Two-factor authentication enabled",
    device: "Windows PC - Chrome Browser",
    location: "Bangalore, India",
    time: "April 28, 2023",
    status: "success"
  }
];

// Notification preferences
const notificationCategories = [
  {
    name: "Lead updates",
    description: "Get notified when your leads change status or update documents",
    emailEnabled: true,
    pushEnabled: true,
    smsEnabled: false
  },
  {
    name: "Client messages",
    description: "Receive alerts for new messages from clients",
    emailEnabled: true,
    pushEnabled: true,
    smsEnabled: true
  },
  {
    name: "Meeting reminders",
    description: "Reminders for upcoming meetings and appointments",
    emailEnabled: true,
    pushEnabled: true,
    smsEnabled: true
  },
  {
    name: "System notifications",
    description: "Updates about the platform and new features",
    emailEnabled: true,
    pushEnabled: false,
    smsEnabled: false
  },
  {
    name: "Performance reports",
    description: "Weekly and monthly performance summaries",
    emailEnabled: true,
    pushEnabled: false,
    smsEnabled: false
  }
];

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("profile");
  const [theme, setTheme] = useState("light");
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [language, setLanguage] = useState("en");

  // Profile state
  const [profileData, setProfileData] = useState({
    name: userData.name,
    email: userData.email,
    phone: userData.phone
  });
  
  // Settings states
  const [selectedLanguage, setSelectedLanguage] = useState(userData.language);
  const [selectedTimezone, setSelectedTimezone] = useState(userData.timezone);
  const [selectedDateFormat, setSelectedDateFormat] = useState(userData.dateFormat);
  const [selectedTimeFormat, setSelectedTimeFormat] = useState(userData.timeFormat);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(userData.twoFactorEnabled);
  
  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState([
    {
      type: "New Lead Assigned",
      emailEnabled: true,
      pushEnabled: true,
      smsEnabled: false
    },
    {
      type: "Lead Status Update",
      emailEnabled: true,
      pushEnabled: false,
      smsEnabled: false
    },
    {
      type: "Document Upload",
      emailEnabled: true,
      pushEnabled: true,
      smsEnabled: true
    }
  ]);
  
  // Handle profile form submission
  const handleProfileUpdate = (e: React.FormEvent) => {
    e.preventDefault();
    // Here would be API call to update profile
    console.log("Profile update:", profileData);
    showToast("Profile updated successfully");
  };
  
  // Toggle notification preference
  const toggleNotification = (index: number, type: 'email' | 'push' | 'sms') => {
    setNotificationSettings(prev => {
      const newSettings = [...prev];
      if (type === 'email') {
        newSettings[index].emailEnabled = !newSettings[index].emailEnabled;
      } else if (type === 'push') {
        newSettings[index].pushEnabled = !newSettings[index].pushEnabled;
      } else {
        newSettings[index].smsEnabled = !newSettings[index].smsEnabled;
      }
      return newSettings;
    });
  };
  
  // Show toast notification
  const showToast = (message: string) => {
    // In a real app, this would show a toast notification
    console.log(message);
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 mb-2">Settings</h1>
        <p className="text-gray-600">Manage your account preferences and settings</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        {/* Settings Navigation */}
        <div className="md:col-span-1">
          <div className="rounded-xl overflow-hidden bg-white/80 backdrop-blur-lg border border-indigo-100/30 shadow-md">
            <button
              onClick={() => setActiveTab("profile")}
              className={`w-full text-left px-4 py-3 flex items-center space-x-3 ${
                activeTab === "profile"
                  ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-indigo-600"
                  : "hover:bg-gray-50"
              }`}
            >
              <User className={`h-5 w-5 ${activeTab === "profile" ? "text-indigo-600" : "text-gray-500"}`} />
              <span className={activeTab === "profile" ? "font-medium text-indigo-700" : "text-gray-600"}>Profile</span>
            </button>
            
            <button
              onClick={() => setActiveTab("security")}
              className={`w-full text-left px-4 py-3 flex items-center space-x-3 ${
                activeTab === "security"
                  ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-indigo-600"
                  : "hover:bg-gray-50"
              }`}
            >
              <Lock className={`h-5 w-5 ${activeTab === "security" ? "text-indigo-600" : "text-gray-500"}`} />
              <span className={activeTab === "security" ? "font-medium text-indigo-700" : "text-gray-600"}>Security</span>
            </button>
            
            <button
              onClick={() => setActiveTab("notifications")}
              className={`w-full text-left px-4 py-3 flex items-center space-x-3 ${
                activeTab === "notifications"
                  ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-indigo-600"
                  : "hover:bg-gray-50"
              }`}
            >
              <Bell className={`h-5 w-5 ${activeTab === "notifications" ? "text-indigo-600" : "text-gray-500"}`} />
              <span className={activeTab === "notifications" ? "font-medium text-indigo-700" : "text-gray-600"}>Notifications</span>
            </button>
            
            <button
              onClick={() => setActiveTab("appearance")}
              className={`w-full text-left px-4 py-3 flex items-center space-x-3 ${
                activeTab === "appearance"
                  ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-indigo-600"
                  : "hover:bg-gray-50"
              }`}
            >
              <Sun className={`h-5 w-5 ${activeTab === "appearance" ? "text-indigo-600" : "text-gray-500"}`} />
              <span className={activeTab === "appearance" ? "font-medium text-indigo-700" : "text-gray-600"}>Appearance</span>
            </button>
            
            <button
              onClick={() => setActiveTab("language")}
              className={`w-full text-left px-4 py-3 flex items-center space-x-3 ${
                activeTab === "language"
                  ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-indigo-600"
                  : "hover:bg-gray-50"
              }`}
            >
              <Globe className={`h-5 w-5 ${activeTab === "language" ? "text-indigo-600" : "text-gray-500"}`} />
              <span className={activeTab === "language" ? "font-medium text-indigo-700" : "text-gray-600"}>Language</span>
            </button>
          </div>
        </div>

        {/* Settings Content */}
        <div className="md:col-span-4">
          <div className="rounded-xl bg-white/80 backdrop-blur-lg border border-indigo-100/30 shadow-md p-6">
            {activeTab === "profile" && (
              <div>
                <h2 className="text-xl font-semibold text-indigo-800 mb-6">Profile Settings</h2>
                
                <div className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                    <input
                      type="text"
                      id="name"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      value={profileData.name}
                      onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <input
                      type="email"
                      id="email"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      value={profileData.email}
                      onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                    <input
                      type="text"
                      id="phone"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      value={profileData.phone}
                      onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      onClick={handleProfileUpdate}
                      className="flex items-center bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === "notifications" && (
              <div>
                <h2 className="text-xl font-semibold text-indigo-800 mb-6">Notification Preferences</h2>
                
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-800">Email Notifications</h3>
                      <p className="text-sm text-gray-600">Receive email updates about your account activity</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input 
                        type="checkbox" 
                        checked={emailNotifications}
                        onChange={(e) => setEmailNotifications(e.target.checked)}
                        className="sr-only peer" 
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-800">Push Notifications</h3>
                      <p className="text-sm text-gray-600">Receive push notifications for important updates</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input 
                        type="checkbox" 
                        checked={pushNotifications}
                        onChange={(e) => setPushNotifications(e.target.checked)}
                        className="sr-only peer" 
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>
                  
                  <div className="pt-4 border-t border-gray-200">
                    <h3 className="font-medium text-gray-800 mb-3">Notification Types</h3>
                    
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="leadUpdates"
                          defaultChecked
                          className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <label htmlFor="leadUpdates" className="ml-3 text-sm text-gray-700">
                          Lead updates and status changes
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="meetings"
                          defaultChecked
                          className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <label htmlFor="meetings" className="ml-3 text-sm text-gray-700">
                          Meeting and appointment reminders
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="tasks"
                          defaultChecked
                          className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <label htmlFor="tasks" className="ml-3 text-sm text-gray-700">
                          Task assignments and due dates
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="marketing"
                          className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                        />
                        <label htmlFor="marketing" className="ml-3 text-sm text-gray-700">
                          Marketing and promotional messages
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      onClick={() => {
                        // Implement notification settings save logic
                        showToast("Notification preferences saved");
                      }}
                      className="flex items-center bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Preferences
                    </button>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === "appearance" && (
              <div>
                <h2 className="text-xl font-semibold text-indigo-800 mb-6">Appearance Settings</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium text-gray-800 mb-3">Theme</h3>
                    <div className="grid grid-cols-3 gap-3">
                      <button
                        className={`p-4 rounded-lg border flex flex-col items-center ${
                          theme === "light" 
                            ? "border-indigo-600 bg-indigo-50 text-indigo-600" 
                            : "border-gray-200 hover:bg-gray-50"
                        }`}
                        onClick={() => setTheme("light")}
                      >
                        <Sun className="h-6 w-6 mb-2" />
                        <span className="text-sm">Light</span>
                      </button>
                      
                      <button
                        className={`p-4 rounded-lg border flex flex-col items-center ${
                          theme === "dark" 
                            ? "border-indigo-600 bg-indigo-50 text-indigo-600" 
                            : "border-gray-200 hover:bg-gray-50"
                        }`}
                        onClick={() => setTheme("dark")}
                      >
                        <Moon className="h-6 w-6 mb-2" />
                        <span className="text-sm">Dark</span>
                      </button>
                      
                      <button
                        className={`p-4 rounded-lg border flex flex-col items-center ${
                          theme === "system" 
                            ? "border-indigo-600 bg-indigo-50 text-indigo-600" 
                            : "border-gray-200 hover:bg-gray-50"
                        }`}
                        onClick={() => setTheme("system")}
                      >
                        <div className="h-6 w-6 mb-2 flex">
                          <Sun className="h-5 w-5" />
                          <Moon className="h-5 w-5" />
                        </div>
                        <span className="text-sm">System</span>
                      </button>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t border-gray-200">
                    <h3 className="font-medium text-gray-800 mb-3">Density</h3>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="comfortable"
                          name="density"
                          defaultChecked
                          className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                        />
                        <label htmlFor="comfortable" className="ml-3 text-sm text-gray-700">
                          Comfortable - Standard spacing
                        </label>
                      </div>
                      
                      <div className="flex items-center">
                        <input
                          type="radio"
                          id="compact"
                          name="density"
                          className="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                        />
                        <label htmlFor="compact" className="ml-3 text-sm text-gray-700">
                          Compact - Reduced spacing
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      onClick={() => {
                        // Implement appearance settings save logic
                        showToast("Appearance settings saved");
                      }}
                      className="flex items-center bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </button>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === "language" && (
              <div>
                <h2 className="text-xl font-semibold text-indigo-800 mb-6">Language Settings</h2>
                
                <div className="space-y-6">
                  <div>
                    <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">Interface Language</label>
                    <select
                      id="language"
                      value={language}
                      onChange={(e) => setLanguage(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    >
                      <option value="en">English (US)</option>
                      <option value="en-gb">English (UK)</option>
                      <option value="es">Español (Spanish)</option>
                      <option value="fr">Français (French)</option>
                      <option value="de">Deutsch (German)</option>
                      <option value="zh">中文 (Chinese)</option>
                      <option value="ja">日本語 (Japanese)</option>
                      <option value="hi">हिन्दी (Hindi)</option>
                    </select>
                    <p className="text-sm text-gray-500 mt-2">
                      This sets the language for the user interface
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-gray-800 mb-3">Region and Format</h3>
                    
                    <div className="space-y-3">
                      <div>
                        <label htmlFor="format" className="block text-sm font-medium text-gray-700 mb-1">Date and Time Format</label>
                        <select
                          id="format"
                          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                          defaultValue="us"
                        >
                          <option value="us">MM/DD/YYYY, 12-hour time</option>
                          <option value="uk">DD/MM/YYYY, 24-hour time</option>
                          <option value="iso">YYYY-MM-DD, 24-hour time</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      onClick={() => {
                        // Implement language settings save logic
                        showToast("Language settings saved");
                      }}
                      className="flex items-center bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </button>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === "security" && (
              <div>
                <h2 className="text-xl font-semibold text-indigo-800 mb-6">Security Settings</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium text-gray-800 mb-3">Two-Factor Authentication</h3>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">
                          Add an extra layer of security to your account
                        </p>
                      </div>
                      <button className="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                        Enable
                      </button>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t border-gray-200">
                    <h3 className="font-medium text-gray-800 mb-3">Session Management</h3>
                    
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="text-sm font-medium text-gray-800">Current Session</h4>
                          <p className="text-xs text-gray-500">Windows • Chrome • Mumbai, India</p>
                        </div>
                        <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                          Active
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="text-sm font-medium text-gray-800">Previous Session</h4>
                          <p className="text-xs text-gray-500">Android • Mobile App • Mumbai, India</p>
                        </div>
                        <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">
                          5 days ago
                        </span>
                      </div>
                    </div>
                    
                    <button className="mt-4 text-sm font-medium text-red-600 hover:text-red-800">
                      Log out from all other devices
                    </button>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      onClick={() => {
                        // Implement security settings save logic
                        showToast("Security settings updated");
                      }}
                      className="flex items-center bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Update Security
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 