"use client"

import Link from "next/link"
import { useUserRole } from "@/components/auth/RoleBasedAccess"
import {
  LayoutDashboard,
  Users,
  BarChart3,
  UserCheck,
  Building,
  Home,
  Settings,
  ArrowRight
} from "lucide-react"

interface DashboardCard {
  name: string
  href: string
  icon: React.ReactNode
  description: string
  roles: string[]
  color: string
}

const dashboardCards: DashboardCard[] = [
  {
    name: "Admin Dashboard",
    href: "/admin",
    icon: <Settings className="h-6 w-6" />,
    description: "System administration, user management, and global settings",
    roles: ["admin"],
    color: "from-red-500 to-pink-600"
  },
  {
    name: "CRM Dashboard", 
    href: "/crm",
    icon: <Users className="h-6 w-6" />,
    description: "Customer relationship management and lead scoring",
    roles: ["admin", "crm", "sales-manager"],
    color: "from-blue-500 to-indigo-600"
  },
  {
    name: "Sales Manager",
    href: "/sales-manager", 
    icon: <BarChart3 className="h-6 w-6" />,
    description: "Team performance, lead distribution, and analytics",
    roles: ["admin", "sales-manager"],
    color: "from-green-500 to-emerald-600"
  },
  {
    name: "Sales Executive",
    href: "/sales-executive",
    icon: <UserCheck className="h-6 w-6" />,
    description: "Lead management, client communications, and targets", 
    roles: ["admin", "sales-manager", "sales-executive"],
    color: "from-purple-500 to-violet-600"
  },
  {
    name: "HR Dashboard",
    href: "/hr",
    icon: <Building className="h-6 w-6" />,
    description: "Human resources, compliance, and training management",
    roles: ["admin", "hr"],
    color: "from-orange-500 to-amber-600"
  },
  {
    name: "General Dashboard",
    href: "/dashboard", 
    icon: <LayoutDashboard className="h-6 w-6" />,
    description: "Applications, appointments, and task management",
    roles: ["admin", "sales-manager", "sales-executive", "crm", "hr", "user"],
    color: "from-cyan-500 to-blue-600"
  },
  {
    name: "User Portal",
    href: "/user",
    icon: <Home className="h-6 w-6" />,
    description: "Personal visa journey, bookings, and progress tracking",
    roles: ["admin", "sales-manager", "sales-executive", "crm", "hr", "user"],
    color: "from-teal-500 to-green-600"
  }
]

interface DashboardGridProps {
  title?: string
  subtitle?: string
  className?: string
}

export default function DashboardGrid({ 
  title = "Available Dashboards", 
  subtitle = "Choose a dashboard to access based on your role",
  className = ""
}: DashboardGridProps) {
  const { role, isLoading } = useUserRole()

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="text-center">
          <div className="h-8 w-64 bg-gray-200 rounded mx-auto mb-2 animate-pulse"></div>
          <div className="h-4 w-96 bg-gray-200 rounded mx-auto animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    )
  }

  if (!role) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className="text-gray-600">Please sign in to access dashboards</p>
      </div>
    )
  }

  // Filter dashboards based on user role
  const availableDashboards = dashboardCards.filter(dashboard => 
    dashboard.roles.includes(role)
  )

  if (availableDashboards.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <p className="text-gray-600">No dashboards available for your role</p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600">{subtitle}</p>
        <div className="mt-2">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            Your Role: {role.charAt(0).toUpperCase() + role.slice(1).replace('-', ' ')}
          </span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableDashboards.map((dashboard) => (
          <Link
            key={dashboard.href}
            href={dashboard.href}
            className="group relative overflow-hidden rounded-xl bg-white border border-gray-200 hover:border-gray-300 transition-all duration-200 hover:shadow-lg hover:-translate-y-1"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-lg bg-gradient-to-r ${dashboard.color} text-white shadow-lg`}>
                  {dashboard.icon}
                </div>
                <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                {dashboard.name}
              </h3>
              
              <p className="text-sm text-gray-600 leading-relaxed">
                {dashboard.description}
              </p>
            </div>
            
            {/* Hover effect overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none"></div>
          </Link>
        ))}
      </div>
      
      {availableDashboards.length > 0 && (
        <div className="text-center text-sm text-gray-500">
          You have access to {availableDashboards.length} dashboard{availableDashboards.length !== 1 ? 's' : ''}
        </div>
      )}
    </div>
  )
}

// Compact version for smaller spaces
export function DashboardGridCompact({ className = "" }: { className?: string }) {
  const { role, isLoading } = useUserRole()

  if (isLoading || !role) {
    return null
  }

  const availableDashboards = dashboardCards.filter(dashboard => 
    dashboard.roles.includes(role)
  ).slice(0, 4) // Show only first 4

  return (
    <div className={`grid grid-cols-2 gap-3 ${className}`}>
      {availableDashboards.map((dashboard) => (
        <Link
          key={dashboard.href}
          href={dashboard.href}
          className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all"
        >
          <div className={`p-2 rounded bg-gradient-to-r ${dashboard.color} text-white`}>
            {dashboard.icon}
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-gray-900 truncate">
              {dashboard.name}
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}
