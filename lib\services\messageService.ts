import { prisma } from '@/lib/prisma'
import { Message, MessageType } from '@prisma/client'

export interface CreateMessageData {
  senderId: string
  receiverId: string
  type?: MessageType
  subject?: string
  content: string
  attachments?: any
  metadata?: any
}

export interface MessageWithUsers extends Message {
  sender: {
    id: string
    name: string | null
    email: string
    image: string | null
  }
  receiver: {
    id: string
    name: string | null
    email: string
    image: string | null
  }
}

export class MessageService {
  // Send message
  static async sendMessage(data: CreateMessageData): Promise<Message> {
    try {
      return await prisma.message.create({
        data,
      })
    } catch (error) {
      console.error('Error sending message:', error)
      throw new Error('Failed to send message')
    }
  }

  // Get messages for user (both sent and received)
  static async getUserMessages(userId: string, limit = 50): Promise<MessageWithUsers[]> {
    try {
      return await prisma.message.findMany({
        where: {
          OR: [
            { senderId: userId },
            { receiverId: userId }
          ]
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          receiver: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      }) as MessageWithUsers[]
    } catch (error) {
      console.error('Error fetching messages:', error)
      return []
    }
  }

  // Get received messages for user
  static async getReceivedMessages(userId: string, unreadOnly = false): Promise<MessageWithUsers[]> {
    try {
      const whereClause: any = { receiverId: userId }
      
      if (unreadOnly) {
        whereClause.isRead = false
      }

      return await prisma.message.findMany({
        where: whereClause,
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          receiver: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
      }) as MessageWithUsers[]
    } catch (error) {
      console.error('Error fetching received messages:', error)
      return []
    }
  }

  // Get sent messages for user
  static async getSentMessages(userId: string): Promise<MessageWithUsers[]> {
    try {
      return await prisma.message.findMany({
        where: { senderId: userId },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          receiver: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
      }) as MessageWithUsers[]
    } catch (error) {
      console.error('Error fetching sent messages:', error)
      return []
    }
  }

  // Get message by ID
  static async getMessageById(id: string, userId: string): Promise<MessageWithUsers | null> {
    try {
      return await prisma.message.findFirst({
        where: { 
          id,
          OR: [
            { senderId: userId },
            { receiverId: userId }
          ]
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          receiver: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        },
      }) as MessageWithUsers | null
    } catch (error) {
      console.error('Error fetching message:', error)
      return null
    }
  }

  // Mark message as read
  static async markAsRead(id: string, userId: string): Promise<Message | null> {
    try {
      return await prisma.message.update({
        where: { 
          id,
          receiverId: userId // Only receiver can mark as read
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      })
    } catch (error) {
      console.error('Error marking message as read:', error)
      return null
    }
  }

  // Mark all messages as read for user
  static async markAllAsRead(userId: string): Promise<number> {
    try {
      const result = await prisma.message.updateMany({
        where: { 
          receiverId: userId,
          isRead: false
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      })
      return result.count
    } catch (error) {
      console.error('Error marking all messages as read:', error)
      return 0
    }
  }

  // Delete message
  static async deleteMessage(id: string, userId: string): Promise<boolean> {
    try {
      await prisma.message.delete({
        where: { 
          id,
          OR: [
            { senderId: userId },
            { receiverId: userId }
          ]
        }
      })
      return true
    } catch (error) {
      console.error('Error deleting message:', error)
      return false
    }
  }

  // Get unread message count
  static async getUnreadCount(userId: string): Promise<number> {
    try {
      return await prisma.message.count({
        where: { 
          receiverId: userId,
          isRead: false
        }
      })
    } catch (error) {
      console.error('Error fetching unread count:', error)
      return 0
    }
  }

  // Get conversation between two users
  static async getConversation(userId1: string, userId2: string, limit = 50): Promise<MessageWithUsers[]> {
    try {
      return await prisma.message.findMany({
        where: {
          OR: [
            { senderId: userId1, receiverId: userId2 },
            { senderId: userId2, receiverId: userId1 }
          ]
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          receiver: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        },
        orderBy: { createdAt: 'asc' },
        take: limit,
      }) as MessageWithUsers[]
    } catch (error) {
      console.error('Error fetching conversation:', error)
      return []
    }
  }
}
