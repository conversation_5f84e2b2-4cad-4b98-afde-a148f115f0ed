'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

interface LeadFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  leadId?: string | null; // If provided, we're editing, otherwise creating
}

interface LeadFormData {
  id?: string;
  clientName: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  service: string;
  status: string;
  priority: string;
  value: number;
  notes: string;
  assignedTo: string | null;
}

// Define status and service options for consistency
const STATUS_OPTIONS = [
  { value: 'new', label: 'New' },
  { value: 'contacted', label: 'Contacted' },
  { value: 'qualified', label: 'Qualified' },
  { value: 'proposal', label: 'Proposal' },
  { value: 'negotiation', label: 'Negotiation' },
  { value: 'closed', label: 'Closed' },
  { value: 'lost', label: 'Lost' },
];

const SERVICE_OPTIONS = [
  { value: 'EB-1', label: 'EB-1 Visa' },
  { value: 'O-1', label: 'O-1 Visa' },
  { value: 'H-1B', label: 'H-1B Visa' },
  { value: 'Student Visa', label: 'Student Visa' },
  { value: 'EB-5', label: 'EB-5 Visa' },
  { value: 'J-1', label: 'J-1 Visa' },
  { value: 'Tourist Visa', label: 'Tourist Visa' },
  { value: 'Business Visa', label: 'Business Visa' },
];

const PRIORITY_OPTIONS = [
  { value: 'high', label: 'High' },
  { value: 'medium', label: 'Medium' },
  { value: 'low', label: 'Low' },
];

// Mock executives data
const EXECUTIVES = [
  { id: 'exec1', name: 'Priya Sharma' },
  { id: 'exec2', name: 'Rahul Singh' },
  { id: 'exec3', name: 'Amit Patel' },
  { id: 'exec4', name: 'Neha Kumar' },
];

export default function LeadFormModal({ isOpen, onClose, onSuccess, leadId }: LeadFormModalProps) {
  const [formData, setFormData] = useState<LeadFormData>({
    clientName: '',
    contactName: '',
    contactEmail: '',
    contactPhone: '',
    service: 'EB-1',
    status: 'new',
    priority: 'medium',
    value: 100000,
    notes: '',
    assignedTo: null,
  });
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fetchError, setFetchError] = useState<string | null>(null);
  
  const isEditMode = !!leadId;
  
  // Fetch lead data if in edit mode
  useEffect(() => {
    if (isOpen && isEditMode && leadId) {
      fetchLeadData(leadId);
    }
  }, [isOpen, isEditMode, leadId]);
  
  const fetchLeadData = async (id: string) => {
    try {
      setFetchLoading(true);
      setFetchError(null);
      
      const response = await fetch(`/api/leads?id=${id}`);
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch lead data');
      }
      
      setFormData({
        id: data.lead.id,
        clientName: data.lead.clientName,
        contactName: data.lead.contactName,
        contactEmail: data.lead.contactEmail,
        contactPhone: data.lead.contactPhone,
        service: data.lead.service,
        status: data.lead.status,
        priority: data.lead.priority,
        value: data.lead.value,
        notes: data.lead.notes || '',
        assignedTo: data.lead.assignedTo,
      });
    } catch (err: any) {
      setFetchError(err.message || 'An error occurred');
      console.error('Error fetching lead data:', err);
    } finally {
      setFetchLoading(false);
    }
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: name === 'value' ? Number(value) : value
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);
      
      const endpoint = isEditMode ? '/api/leads' : '/api/leads';
      const method = isEditMode ? 'PUT' : 'POST';
      
      // Prepare the request payload
      const payload = isEditMode ? formData : {
        name: formData.contactName,
        email: formData.contactEmail,
        mobile: formData.contactPhone,
        service: formData.service,
        message: formData.notes,
        priority: formData.priority,
        source: 'Sales Manager Dashboard'
      };
      
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || `Failed to ${isEditMode ? 'update' : 'create'} lead`);
      }
      
      // Success - close the modal and refresh the leads list
      onSuccess();
      onClose();
      
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} lead:`, err);
    } finally {
      setLoading(false);
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-lg max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-indigo-600 to-blue-600 p-4 text-white flex justify-between items-center">
          <h2 className="text-xl font-bold">
            {isEditMode ? 'Edit Lead' : 'Add New Lead'}
          </h2>
          <button 
            onClick={onClose}
            className="text-white/80 hover:text-white transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Form Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-64px)]">
          {fetchLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-600"></div>
            </div>
          ) : fetchError ? (
            <div className="text-center py-8">
              <div className="text-red-500 mb-3">Failed to load lead data</div>
              <p className="text-gray-600 mb-4">{fetchError}</p>
              <button 
                onClick={() => leadId && fetchLeadData(leadId)}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
              >
                Try Again
              </button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-5">
              {error && (
                <div className="p-3 bg-red-50 border-l-4 border-red-500 text-red-700 text-sm">
                  {error}
                </div>
              )}
              
              {/* Organization Details */}
              <div>
                <label htmlFor="clientName" className="block text-sm font-medium text-gray-700 mb-1">
                  Organization/Client Name*
                </label>
                <input
                  type="text"
                  id="clientName"
                  name="clientName"
                  value={formData.clientName}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              
              {/* Contact Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="contactName" className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Person*
                  </label>
                  <input
                    type="text"
                    id="contactName"
                    name="contactName"
                    value={formData.contactName}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address*
                  </label>
                  <input
                    type="email"
                    id="contactEmail"
                    name="contactEmail"
                    value={formData.contactEmail}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number*
                  </label>
                  <input
                    type="tel"
                    id="contactPhone"
                    name="contactPhone"
                    value={formData.contactPhone}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>
              
              {/* Lead Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-1">
                    Service*
                  </label>
                  <select
                    id="service"
                    name="service"
                    value={formData.service}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    {SERVICE_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label htmlFor="value" className="block text-sm font-medium text-gray-700 mb-1">
                    Potential Value (₹)*
                  </label>
                  <input
                    type="number"
                    id="value"
                    name="value"
                    value={formData.value}
                    onChange={handleChange}
                    required
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                    Status*
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    {STATUS_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                    Priority*
                  </label>
                  <select
                    id="priority"
                    name="priority"
                    value={formData.priority}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    {PRIORITY_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700 mb-1">
                    Assign To
                  </label>
                  <select
                    id="assignedTo"
                    name="assignedTo"
                    value={formData.assignedTo || ''}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="">Unassigned</option>
                    {EXECUTIVES.map(exec => (
                      <option key={exec.id} value={exec.id}>
                        {exec.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              {/* Notes */}
              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                ></textarea>
              </div>
              
              {/* Form Actions */}
              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-70"
                  disabled={loading}
                >
                  {loading ? 'Saving...' : isEditMode ? 'Update Lead' : 'Add Lead'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
} 