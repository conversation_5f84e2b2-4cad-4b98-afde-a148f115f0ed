"use client";

import { useState } from "react";
import Link from "next/link";
import { 
  ChevronLeft, 
  Save, 
  Bell, 
  Mail, 
  Smartphone, 
  MessageCircle,
  Calendar,
  Users,
  FileText,
  Clock,
  AlertCircle,
  Plus,
  Trash2,
  Edit
} from "lucide-react";

// Form Field Component
interface FormFieldProps {
  label: string;
  children: React.ReactNode;
  helper?: string;
}

const FormField = ({ label, children, helper }: FormFieldProps) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start py-4 border-b border-gray-100 last:border-0">
    <div>
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      {helper && <p className="mt-1 text-xs text-gray-500">{helper}</p>}
    </div>
    <div className="md:col-span-2">
      {children}
    </div>
  </div>
);

// Toggle Switch Component
interface ToggleSwitchProps {
  enabled: boolean;
  onChange: () => void;
}

const ToggleSwitch = ({ enabled, onChange }: ToggleSwitchProps) => {
  return (
    <button
      type="button"
      className={`${
        enabled ? 'bg-indigo-600' : 'bg-gray-200'
      } relative inline-flex flex-shrink-0 w-11 h-6 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none`}
      role="switch"
      aria-checked={enabled}
      onClick={onChange}
    >
      <span
        className={`${
          enabled ? 'translate-x-5' : 'translate-x-0'
        } pointer-events-none relative inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200`}
      />
    </button>
  );
};

// Email Template Card Component
interface EmailTemplateProps {
  id: string;
  name: string;
  subject: string;
  lastModified: string;
  isActive: boolean;
  onToggle: (id: string) => void;
  onEdit: (id: string) => void;
}

const EmailTemplateCard = ({ id, name, subject, lastModified, isActive, onToggle, onEdit }: EmailTemplateProps) => (
  <div className="bg-white p-4 rounded-lg border border-gray-200 hover:border-indigo-200 transition-colors">
    <div className="flex justify-between items-start">
      <div>
        <h3 className="font-medium text-gray-900">{name}</h3>
        <p className="text-sm text-gray-500 mt-1">Subject: {subject}</p>
        <p className="text-xs text-gray-400 mt-2">Last edited: {lastModified}</p>
      </div>
      <div className="flex items-center gap-2">
        <ToggleSwitch enabled={isActive} onChange={() => onToggle(id)} />
        <button 
          onClick={() => onEdit(id)}
          className="p-1 text-gray-400 hover:text-indigo-600 transition-colors"
        >
          <Edit className="h-4 w-4" />
        </button>
      </div>
    </div>
  </div>
);

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  variables: string[];
  lastModified: string;
  isActive: boolean;
}

export default function NotificationSettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  
  // Notification settings state
  const [settings, setSettings] = useState({
    // Email notifications
    enableEmailNotifications: true,
    emailDigestFrequency: "daily",
    customEmailFooter: "© 2025 Visa Mentor Inc. All rights reserved.",
    senderName: "Visa Mentor Support",
    senderEmail: "<EMAIL>",
    
    // Push notifications
    enablePushNotifications: true,
    allowWeekendNotifications: false,
    quietHoursStart: "22:00",
    quietHoursEnd: "07:00",
    enableQuietHours: false,
    
    // In-app notifications
    enableInAppNotifications: true,
    maxNotificationsStored: 50,
    markReadAfterViewed: true,
    showNotificationPreview: true,
    
    // SMS notifications
    enableSMSNotifications: false,
    defaultCountryCode: "+1",
  });
  
  // Email templates
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>([
    {
      id: "welcome",
      name: "Welcome Email",
      subject: "Welcome to Visa Mentor",
      content: "Thank you for joining Visa Mentor...",
      variables: ["name", "email"],
      lastModified: "2024-05-15",
      isActive: true,
    },
    {
      id: "lead-assigned",
      name: "Lead Assignment",
      subject: "New Lead Assigned",
      content: "A new lead has been assigned to you...",
      variables: ["counselor_name", "lead_name"],
      lastModified: "2024-05-14",
      isActive: true,
    },
    {
      id: "document-upload",
      name: "Document Upload",
      subject: "Document Upload Confirmation",
      content: "Your document has been successfully uploaded...",
      variables: ["document_name", "upload_date"],
      lastModified: "2024-05-13",
      isActive: true,
    },
  ]);
  
  // Notification events
  const [notificationEvents, setNotificationEvents] = useState([
    {
      id: "doc-upload",
      name: "Document Upload",
      description: "When a user uploads a new document",
      email: true,
      push: true,
      inApp: true,
      sms: false
    },
    {
      id: "status-change",
      name: "Application Status Change",
      description: "When an application's status changes",
      email: true,
      push: true,
      inApp: true,
      sms: true
    },
    {
      id: "appointment",
      name: "Appointment Reminders",
      description: "Reminders for upcoming appointments",
      email: true,
      push: true,
      inApp: true,
      sms: true
    },
    {
      id: "message",
      name: "New Message",
      description: "When a user receives a new message",
      email: true,
      push: true,
      inApp: true,
      sms: false
    },
    {
      id: "payment",
      name: "Payment Confirmation",
      description: "When a payment is processed",
      email: true,
      push: false,
      inApp: true,
      sms: false
    }
  ]);
  
  // Handle toggle changes
  const toggleSetting = (field: string) => {
    setSettings({
      ...settings,
      [field]: !settings[field as keyof typeof settings]
    });
  };
  
  // Handle input changes
  const handleChange = (field: string, value: string) => {
    setSettings({
      ...settings,
      [field]: value
    });
  };
  
  // Toggle email template active status
  const toggleEmailTemplate = (id: string) => {
    setEmailTemplates(templates => 
      templates.map(template => 
        template.id === id 
          ? { ...template, isActive: !template.isActive } 
          : template
      )
    );
  };
  
  // Edit template
  const editTemplate = (id: string) => {
    setSelectedTemplate(id);
    // In a real app, this would open a template editor
  };
  
  // Toggle notification event channel
  const toggleNotificationEventChannel = (eventId: string, channel: 'email' | 'push' | 'inApp' | 'sms') => {
    setNotificationEvents(events => 
      events.map(event => 
        event.id === eventId 
          ? { ...event, [channel]: !event[channel] } 
          : event
      )
    );
  };
  
  // Save settings
  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Link href="/admin/settings" className="text-gray-500 hover:text-gray-700 mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-gray-800">Notification Settings</h1>
        </div>
        <p className="text-gray-600">Configure notifications, email templates, and messaging preferences</p>
      </div>
      
      {/* Success Message */}
      {showSuccess && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-800 rounded-lg p-4 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          <span>Notification settings have been successfully updated.</span>
        </div>
      )}
      
      {/* Settings Form */}
      <div className="grid grid-cols-1 gap-6 mb-6">
        {/* Email Notification Settings */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6">
            <div className="flex items-center mb-4">
              <Mail className="h-5 w-5 text-indigo-600 mr-2" />
              <h2 className="text-lg font-semibold">Email Notifications</h2>
            </div>
            
            <div className="space-y-2">
              <FormField 
                label="Enable Email Notifications" 
                helper="Send notifications to users via email"
              >
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.enableEmailNotifications} 
                    onChange={() => toggleSetting('enableEmailNotifications')}
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {settings.enableEmailNotifications ? "Enabled" : "Disabled"}
                  </span>
                </div>
              </FormField>
              
              {settings.enableEmailNotifications && (
                <>
                  <FormField 
                    label="Digest Frequency" 
                    helper="How often to send email digests for non-urgent notifications"
                  >
                    <select
                      className="w-full md:w-auto p-2 border border-gray-300 rounded-md"
                      value={settings.emailDigestFrequency}
                      onChange={(e) => handleChange('emailDigestFrequency', e.target.value)}
                    >
                      <option value="instant">Instant (Send immediately)</option>
                      <option value="hourly">Hourly Digest</option>
                      <option value="daily">Daily Digest</option>
                      <option value="weekly">Weekly Digest</option>
                    </select>
                  </FormField>
                  
                  <FormField 
                    label="Sender Name" 
                    helper="The name that appears in the 'from' field of emails"
                  >
                    <input
                      type="text"
                      className="w-full md:w-auto p-2 border border-gray-300 rounded-md"
                      value={settings.senderName}
                      onChange={(e) => handleChange('senderName', e.target.value)}
                    />
                  </FormField>
                  
                  <FormField 
                    label="Sender Email" 
                    helper="The email address that notifications are sent from"
                  >
                    <input
                      type="email"
                      className="w-full md:w-auto p-2 border border-gray-300 rounded-md"
                      value={settings.senderEmail}
                      onChange={(e) => handleChange('senderEmail', e.target.value)}
                    />
                  </FormField>
                  
                  <FormField 
                    label="Custom Email Footer" 
                    helper="Text to include at the bottom of all emails"
                  >
                    <textarea
                      className="w-full p-2 border border-gray-300 rounded-md"
                      rows={2}
                      value={settings.customEmailFooter}
                      onChange={(e) => handleChange('customEmailFooter', e.target.value)}
                    />
                  </FormField>
                </>
              )}
            </div>
          </div>
        </div>
        
        {/* Push Notification Settings */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6">
            <div className="flex items-center mb-4">
              <Smartphone className="h-5 w-5 text-indigo-600 mr-2" />
              <h2 className="text-lg font-semibold">Push Notifications</h2>
            </div>
            
            <div className="space-y-2">
              <FormField 
                label="Enable Push Notifications" 
                helper="Send notifications to mobile devices and browsers"
              >
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.enablePushNotifications} 
                    onChange={() => toggleSetting('enablePushNotifications')}
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {settings.enablePushNotifications ? "Enabled" : "Disabled"}
                  </span>
                </div>
              </FormField>
              
              {settings.enablePushNotifications && (
                <>
                  <FormField 
                    label="Quiet Hours" 
                    helper="Don't send push notifications during these hours"
                  >
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <ToggleSwitch 
                          enabled={settings.enableQuietHours} 
                          onChange={() => toggleSetting('enableQuietHours')}
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          {settings.enableQuietHours ? "Enabled" : "Disabled"}
                        </span>
                      </div>
                      
                      {settings.enableQuietHours && (
                        <div className="flex items-center gap-2">
                          <div className="flex items-center">
                            <span className="text-sm text-gray-700 mr-2">From</span>
                            <input
                              type="time"
                              className="p-2 border border-gray-300 rounded-md"
                              value={settings.quietHoursStart}
                              onChange={(e) => handleChange('quietHoursStart', e.target.value)}
                            />
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm text-gray-700 mr-2">to</span>
                            <input
                              type="time"
                              className="p-2 border border-gray-300 rounded-md"
                              value={settings.quietHoursEnd}
                              onChange={(e) => handleChange('quietHoursEnd', e.target.value)}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </FormField>
                  
                  <FormField 
                    label="Weekend Notifications" 
                    helper="Allow push notifications on weekends"
                  >
                    <div className="flex items-center">
                      <ToggleSwitch 
                        enabled={settings.allowWeekendNotifications} 
                        onChange={() => toggleSetting('allowWeekendNotifications')}
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {settings.allowWeekendNotifications ? "Allowed" : "Disabled"}
                      </span>
                    </div>
                  </FormField>
                </>
              )}
            </div>
          </div>
        </div>
        
        {/* In-app Notification Settings */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6">
            <div className="flex items-center mb-4">
              <Bell className="h-5 w-5 text-indigo-600 mr-2" />
              <h2 className="text-lg font-semibold">In-App Notifications</h2>
            </div>
            
            <div className="space-y-2">
              <FormField 
                label="Enable In-App Notifications" 
                helper="Show notifications within the application UI"
              >
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.enableInAppNotifications} 
                    onChange={() => toggleSetting('enableInAppNotifications')}
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {settings.enableInAppNotifications ? "Enabled" : "Disabled"}
                  </span>
                </div>
              </FormField>
              
              {settings.enableInAppNotifications && (
                <>
                  <FormField 
                    label="Maximum Notifications" 
                    helper="Maximum number of notifications to store per user"
                  >
                    <div className="flex items-center">
                      <input
                        type="number"
                        min="10"
                        max="200"
                        className="w-20 p-2 border border-gray-300 rounded-md"
                        value={settings.maxNotificationsStored}
                        onChange={(e) => handleChange('maxNotificationsStored', e.target.value)}
                      />
                      <span className="ml-2 text-gray-500">notifications</span>
                    </div>
                  </FormField>
                  
                  <FormField 
                    label="Mark as Read" 
                    helper="Automatically mark notifications as read when viewed"
                  >
                    <div className="flex items-center">
                      <ToggleSwitch 
                        enabled={settings.markReadAfterViewed} 
                        onChange={() => toggleSetting('markReadAfterViewed')}
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {settings.markReadAfterViewed ? "Enabled" : "Disabled"}
                      </span>
                    </div>
                  </FormField>
                  
                  <FormField 
                    label="Notification Preview" 
                    helper="Show preview of notification content in notification center"
                  >
                    <div className="flex items-center">
                      <ToggleSwitch 
                        enabled={settings.showNotificationPreview} 
                        onChange={() => toggleSetting('showNotificationPreview')}
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {settings.showNotificationPreview ? "Enabled" : "Disabled"}
                      </span>
                    </div>
                  </FormField>
                </>
              )}
            </div>
          </div>
        </div>
        
        {/* SMS Notification Settings */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6">
            <div className="flex items-center mb-4">
              <MessageCircle className="h-5 w-5 text-indigo-600 mr-2" />
              <h2 className="text-lg font-semibold">SMS Notifications</h2>
            </div>
            
            <div className="space-y-2">
              <FormField 
                label="Enable SMS Notifications" 
                helper="Send notifications via text message (additional charges may apply)"
              >
                <div className="flex items-center">
                  <ToggleSwitch 
                    enabled={settings.enableSMSNotifications} 
                    onChange={() => toggleSetting('enableSMSNotifications')}
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {settings.enableSMSNotifications ? "Enabled" : "Disabled"}
                  </span>
                </div>
              </FormField>
              
              {settings.enableSMSNotifications && (
                <FormField 
                  label="Default Country Code" 
                  helper="Default international dialing code for phone numbers"
                >
                  <input
                    type="text"
                    className="w-20 p-2 border border-gray-300 rounded-md"
                    value={settings.defaultCountryCode}
                    onChange={(e) => handleChange('defaultCountryCode', e.target.value)}
                  />
                </FormField>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Notification Events Configuration */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <Calendar className="h-5 w-5 text-indigo-600 mr-2" />
            <h2 className="text-lg font-semibold">Notification Events</h2>
          </div>
          
          <p className="text-sm text-gray-500 mb-4">
            Configure which channels are used for different notification events
          </p>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Event
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center justify-center">
                      <Mail className="h-4 w-4 mr-1" /> Email
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center justify-center">
                      <Bell className="h-4 w-4 mr-1" /> In-App
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center justify-center">
                      <Smartphone className="h-4 w-4 mr-1" /> Push
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center justify-center">
                      <MessageCircle className="h-4 w-4 mr-1" /> SMS
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {notificationEvents.map((event) => (
                  <tr key={event.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <div className="text-sm font-medium text-gray-900">{event.name}</div>
                        <div className="text-sm text-gray-500">{event.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <ToggleSwitch 
                        enabled={event.email} 
                        onChange={() => toggleNotificationEventChannel(event.id, 'email')}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <ToggleSwitch 
                        enabled={event.inApp} 
                        onChange={() => toggleNotificationEventChannel(event.id, 'inApp')}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <ToggleSwitch 
                        enabled={event.push} 
                        onChange={() => toggleNotificationEventChannel(event.id, 'push')}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <ToggleSwitch 
                        enabled={event.sms} 
                        onChange={() => toggleNotificationEventChannel(event.id, 'sms')}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            <div className="flex justify-end mt-4">
              <button className="flex items-center gap-1 px-3 py-1.5 text-sm text-indigo-600 bg-indigo-50 rounded hover:bg-indigo-100 transition-colors">
                <Plus className="h-4 w-4" />
                <span>Add Event</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Email Templates */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FileText className="h-5 w-5 text-indigo-600 mr-2" />
              <h2 className="text-lg font-semibold">Email Templates</h2>
            </div>
            <button className="flex items-center gap-1 px-3 py-1.5 text-sm text-indigo-600 bg-indigo-50 rounded hover:bg-indigo-100 transition-colors">
              <Plus className="h-4 w-4" />
              <span>New Template</span>
            </button>
          </div>
          
          <p className="text-sm text-gray-500 mb-4">
            Manage email templates used for different notification events
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {emailTemplates.map((template) => (
              <EmailTemplateCard
                key={template.id}
                id={template.id}
                name={template.name}
                subject={template.subject}
                lastModified={template.lastModified}
                isActive={template.isActive}
                onToggle={toggleEmailTemplate}
                onEdit={editTemplate}
              />
            ))}
          </div>
        </div>
      </div>
      
      {/* Save Button */}
      <div className="flex justify-end">
        <div className="flex gap-3">
          <Link 
            href="/admin/settings"
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
          >
            Cancel
          </Link>
          <button 
            onClick={saveSettings}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              isSaving 
                ? "bg-gray-200 text-gray-600 cursor-not-allowed" 
                : "bg-indigo-600 text-white hover:bg-indigo-700"
            }`}
            disabled={isSaving}
          >
            <Save className="h-4 w-4" />
            {isSaving ? "Saving..." : "Save Changes"}
          </button>
        </div>
      </div>
    </div>
  );
} 