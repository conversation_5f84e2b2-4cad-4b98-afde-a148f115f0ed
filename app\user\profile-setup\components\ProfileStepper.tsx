"use client";

import { PROFILE_STEPS, useProfileCompletion } from '@/contexts/ProfileCompletionContext';
import { CheckCircle2, Circle, UserRound, Phone, FileText, BookText, Settings } from 'lucide-react';

export default function ProfileStepper() {
  const { currentStep, completedSteps, goToStep } = useProfileCompletion();

  const steps = [
    {
      id: PROFILE_STEPS.PERSONAL,
      title: 'Personal Information',
      icon: UserRound,
    },
    {
      id: PROFILE_STEPS.CONTACT,
      title: 'Contact Details',
      icon: Phone,
    },
    {
      id: PROFILE_STEPS.VISA_DETAILS,
      title: 'Visa Details',
      icon: FileText,
    },
    {
      id: PROFILE_STEPS.DOCUMENTS,
      title: 'Documents',
      icon: BookText,
    },
    {
      id: PROFILE_STEPS.PREFERENCES,
      title: 'Preferences',
      icon: Settings,
    },
  ];

  return (
    <div className="flex flex-col space-y-1">
      {steps.map((step, index) => {
        const isActive = currentStep === step.id;
        const isCompleted = completedSteps[step.id];
        const Icon = step.icon;
        
        return (
          <button
            key={step.id}
            onClick={() => goToStep(step.id)}
            className={`flex items-center p-3 rounded-md transition-all ${
              isActive 
                ? 'bg-primary/10 text-primary font-medium' 
                : isCompleted 
                  ? 'hover:bg-primary/5 text-primary' 
                  : 'hover:bg-muted text-muted-foreground'
            }`}
          >
            <div className="mr-3">
              {isCompleted ? (
                <CheckCircle2 className="h-5 w-5 text-primary" />
              ) : (
                isActive ? (
                  <Circle className="h-5 w-5 text-primary fill-primary/20" />
                ) : (
                  <Circle className="h-5 w-5" />
                )
              )}
            </div>
            <span>{step.title}</span>
          </button>
        );
      })}
    </div>
  );
} 