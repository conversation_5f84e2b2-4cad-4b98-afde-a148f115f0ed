import { EventEmitter } from 'events';
import { prisma } from '@/lib/prisma';
import { calculateLeadScore, categorizeLead } from '@/lib/models/lead-scoring';

// A global Event Emitter for handling sync events
export const syncHandler = new EventEmitter();

// Define event types
export enum SyncEvent {
  LEAD_CREATED = 'leadCreated',
  LEAD_UPDATED = 'leadUpdated',
  LEAD_ASSIGNED = 'leadAssigned',
  LEAD_SCORE_UPDATED = 'leadScoreUpdated',
  LEAD_STATUS_CHANGED = 'leadStatusChanged',
}

// Initialize event listeners
export function initSyncSystem() {
  // When a lead is updated, recalculate the score and update related systems
  syncHandler.on(SyncEvent.LEAD_UPDATED, async (leadId: string) => {
    try {
      await updateCRMScore(leadId);
      await updateSalesPriority(leadId);
      await triggerAdminAudit(leadId, 'lead_updated');
    } catch (error) {
      console.error('Error handling lead update sync:', error);
    }
  });

  // When a lead is assigned, update the CRM and sales dashboards
  syncHandler.on(SyncEvent.LEAD_ASSIGNED, async (leadId: string, assigneeId: string) => {
    try {
      // Update sales manager's team metrics
      await updateTeamMetrics(assigneeId);
      // Log the assignment
      await triggerAdminAudit(leadId, 'lead_assigned', { assigneeId });
    } catch (error) {
      console.error('Error handling lead assignment sync:', error);
    }
  });

  // When a lead score is updated, update the CRM and sales dashboards
  syncHandler.on(SyncEvent.LEAD_SCORE_UPDATED, async (leadId: string, score: number) => {
    try {
      // Update sales executive's lead priority
      await updateSalesPriority(leadId);
      // Log the score change
      await triggerAdminAudit(leadId, 'score_updated', { score });
    } catch (error) {
      console.error('Error handling lead score update sync:', error);
    }
  });
}

// Helper functions for sync operations

/**
 * Updates the lead score in the CRM
 */
export async function updateCRMScore(leadId: string) {
  try {
    // Fetch the lead from the database
    const lead = await prisma.enhancedLead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // Calculate the new score
    const score = calculateLeadScore(lead);
    const category = categorizeLead(score);
    
    // Update the lead score in the database
    await prisma.enhancedLead.update({
      where: { id: leadId },
      data: { score },
    });

    // Emit an event for the score update
    syncHandler.emit(SyncEvent.LEAD_SCORE_UPDATED, leadId, score);
    
    return { score, category };
  } catch (error) {
    console.error('Error updating CRM score:', error);
    throw error;
  }
}

/**
 * Updates the sales priority for a lead
 */
export async function updateSalesPriority(leadId: string) {
  try {
    // Fetch the lead with the score
    const lead = await prisma.enhancedLead.findUnique({
      where: { id: leadId },
      select: {
        id: true,
        name: true,
        score: true,
        visaType: true,
        documentCompleteness: true,
        status: true,
        assigneeId: true,
      },
    });

    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // In a real implementation, this would update the sales dashboard priority queue
    console.log(`Updated sales priority for lead ${lead.name} with score ${lead.score}`);
    
    return lead;
  } catch (error) {
    console.error('Error updating sales priority:', error);
    throw error;
  }
}

/**
 * Triggers an audit log for admin dashboard
 */
export async function triggerAdminAudit(leadId: string, action: string, metadata?: any) {
  try {
    const lead = await prisma.enhancedLead.findUnique({
      where: { id: leadId },
      select: {
        id: true,
        name: true,
      },
    });

    if (!lead) {
      throw new Error(`Lead with ID ${leadId} not found`);
    }

    // In a real implementation, this would create an audit log in the database
    console.log(`Audit: ${action} for lead ${lead.name}`, metadata);
    
    return { leadId, action, timestamp: new Date(), metadata };
  } catch (error) {
    console.error('Error creating admin audit:', error);
    throw error;
  }
}

/**
 * Updates team metrics when leads are assigned
 */
export async function updateTeamMetrics(assigneeId: string) {
  try {
    // Get all leads assigned to this user
    const assignedLeads = await prisma.enhancedLead.findMany({
      where: { assigneeId },
    });

    // Calculate team metrics
    const metrics = {
      totalLeads: assignedLeads.length,
      highPriorityLeads: assignedLeads.filter((lead) => lead.score >= 0.7).length,
      mediumPriorityLeads: assignedLeads.filter((lead) => lead.score >= 0.4 && lead.score < 0.7).length,
      lowPriorityLeads: assignedLeads.filter((lead) => lead.score < 0.4).length,
      avgScore: assignedLeads.reduce((sum: number, lead) => sum + lead.score, 0) / assignedLeads.length || 0,
    };

    // In a real implementation, this would update the team metrics in the database
    console.log(`Updated team metrics for assignee ${assigneeId}:`, metrics);
    
    return metrics;
  } catch (error) {
    console.error('Error updating team metrics:', error);
    throw error;
  }
} 