import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create a demo user (this will be linked to Clerk users later)
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo User',
      role: 'USER',
      status: 'ACTIVE',
      country: 'India',
      phone: '+91 9876543210',
      location: 'Mumbai, India',
      visaType: 'Student Visa',
      visaGoals: 'Pursuing Masters in Computer Science in USA',
      profileCompleted: true,
      profileProgress: 85,
      membershipTier: 'Premium',
      memberSince: 'January 2024',
      communicationPreference: 'EMAIL',
      receiveUpdates: true,
      receivePromotions: false,
    },
  })

  console.log('✅ Created demo user:', demoUser.email)

  // Create sample applications
  const applications = await Promise.all([
    prisma.application.create({
      data: {
        userId: demoUser.id,
        type: 'Student Visa - USA',
        status: 'IN_REVIEW',
        progress: 65,
        currentStage: 'BIOMETRICS',
        notes: 'All documents submitted, waiting for biometrics appointment',
        counselorId: 'counselor-1',
        estimatedCompletion: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
    }),
    prisma.application.create({
      data: {
        userId: demoUser.id,
        type: 'University Application - Stanford',
        status: 'SUBMITTED',
        progress: 80,
        currentStage: 'DECISION_PENDING',
        notes: 'Application submitted, awaiting university decision',
        counselorId: 'counselor-2',
        estimatedCompletion: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
      },
    }),
  ])

  console.log('✅ Created applications:', applications.length)

  // Create sample appointments
  const appointments = await Promise.all([
    prisma.appointment.create({
      data: {
        userId: demoUser.id,
        title: 'Visa Interview Preparation',
        description: 'Mock interview session to prepare for visa interview',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        time: '10:00 AM',
        duration: 60,
        status: 'SCHEDULED',
        counselorId: 'counselor-1',
        counselorName: 'Rajiv Sharma',
        meetingLink: 'https://meet.google.com/abc-defg-hij',
        notes: 'Bring all required documents for review',
      },
    }),
    prisma.appointment.create({
      data: {
        userId: demoUser.id,
        title: 'Document Review Session',
        description: 'Review and verify all application documents',
        date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        time: '2:30 PM',
        duration: 45,
        status: 'CONFIRMED',
        counselorId: 'counselor-2',
        counselorName: 'Priya Patel',
        meetingLink: 'https://zoom.us/j/*********',
        notes: 'Follow-up on previous consultation',
      },
    }),
  ])

  console.log('✅ Created appointments:', appointments.length)

  // Create sample tasks
  const tasks = await Promise.all([
    prisma.task.create({
      data: {
        userId: demoUser.id,
        title: 'Submit financial documents',
        description: 'Upload bank statements and financial proof documents',
        status: 'PENDING',
        priority: 'HIGH',
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        assignedBy: 'counselor-1',
        category: 'Documents',
        applicationId: applications[0].id,
      },
    }),
    prisma.task.create({
      data: {
        userId: demoUser.id,
        title: 'Complete SOP draft',
        description: 'Write and submit Statement of Purpose draft for review',
        status: 'IN_PROGRESS',
        priority: 'NORMAL',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        assignedBy: 'counselor-2',
        category: 'Application',
        applicationId: applications[1].id,
      },
    }),
    prisma.task.create({
      data: {
        userId: demoUser.id,
        title: 'Prepare for mock interview',
        description: 'Review common interview questions and practice responses',
        status: 'PENDING',
        priority: 'HIGH',
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
        assignedBy: 'counselor-1',
        category: 'Interview Prep',
      },
    }),
  ])

  console.log('✅ Created tasks:', tasks.length)

  // Create sample bookings
  const bookings = await Promise.all([
    prisma.booking.create({
      data: {
        userId: demoUser.id,
        serviceType: 'Consultation',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        time: '10:00 AM',
        duration: 60,
        status: 'confirmed',
        counselorName: 'Rajiv Sharma',
        meetingType: 'video',
        notes: 'Initial consultation for visa application',
        price: 150.00,
        paymentStatus: 'paid',
      },
    }),
    prisma.booking.create({
      data: {
        userId: demoUser.id,
        serviceType: 'Document Review',
        date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
        time: '2:30 PM',
        duration: 45,
        status: 'confirmed',
        counselorName: 'Priya Patel',
        meetingType: 'video',
        notes: 'Review application documents',
        price: 100.00,
        paymentStatus: 'paid',
      },
    }),
  ])

  console.log('✅ Created bookings:', bookings.length)

  // Create sample travel recommendations
  const recommendations = await Promise.all([
    prisma.travelRecommendation.create({
      data: {
        userId: demoUser.id,
        title: 'USA Student Visa Requirements',
        description: 'Complete guide to F-1 visa requirements and application process',
        destination: 'United States',
        category: 'Visa Requirements',
        priority: 'HIGH',
        isPersonalized: true,
        validUntil: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        imageUrl: '/images/usa-flag.jpg',
        actionUrl: '/resources/guides/usa-student-visa',
        isActive: true,
      },
    }),
    prisma.travelRecommendation.create({
      data: {
        userId: demoUser.id,
        title: 'Pre-departure Checklist',
        description: 'Essential items to prepare before traveling to the USA for studies',
        destination: 'United States',
        category: 'Travel Tips',
        priority: 'NORMAL',
        isPersonalized: true,
        validUntil: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        imageUrl: '/images/checklist.jpg',
        actionUrl: '/resources/guides/pre-departure',
        isActive: true,
      },
    }),
  ])

  console.log('✅ Created travel recommendations:', recommendations.length)

  console.log('🎉 Database seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
