"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  CheckCircle2, 
  Clock, 
  AlertTriangle, 
  ChevronRight, 
  Calendar, 
  File, 
  MessageSquare,
  Briefcase,
  Map,
  Building,
  Plane,
  Home
} from "lucide-react";

// Visa stages in order
const visaStages = [
  { id: 'inquiry', label: 'Initial Inquiry', icon: <MessageSquare className="h-5 w-5" /> },
  { id: 'consultation', label: 'Consultation', icon: <Briefcase className="h-5 w-5" /> },
  { id: 'documentation', label: 'Documentation', icon: <File className="h-5 w-5" /> },
  { id: 'submission', label: 'Application Submission', icon: <File className="h-5 w-5" /> },
  { id: 'processing', label: 'Application Processing', icon: <Clock className="h-5 w-5" /> },
  { id: 'interview', label: 'Visa Interview', icon: <MessageSquare className="h-5 w-5" /> },
  { id: 'decision', label: 'Visa Decision', icon: <CheckCircle2 className="h-5 w-5" /> },
  { id: 'travel', label: 'Travel Preparation', icon: <Plane className="h-5 w-5" /> },
  { id: 'arrival', label: 'Arrival & Settlement', icon: <Map className="h-5 w-5" /> },
  { id: 'extension', label: 'Visa Extension/Change', icon: <Calendar className="h-5 w-5" /> },
  { id: 'completion', label: 'Service Completion', icon: <Home className="h-5 w-5" /> },
];

// Mock client visa applications with their current stages
const clientApplications = [
  { 
    id: 1, 
    clientName: "Priya Sharma", 
    service: "Student Visa",
    country: "Australia",
    currentStage: "processing", 
    stageProgress: 45,
    nextAction: "Embassy Processing",
    nextActionDeadline: "Expected in 3 weeks",
    status: "On Track",
    healthScore: 85,
    assignedTo: "Rahul Gupta",
    lastUpdated: "2 days ago",
  },
  { 
    id: 2, 
    clientName: "Raj Patel", 
    service: "Work Visa",
    country: "Canada",
    currentStage: "interview", 
    stageProgress: 75,
    nextAction: "Interview Preparation",
    nextActionDeadline: "In 5 days",
    status: "On Track",
    healthScore: 72,
    assignedTo: "Anjali Singh",
    lastUpdated: "1 day ago",
  },
  { 
    id: 3, 
    clientName: "Ananya Desai", 
    service: "Tourist Visa",
    country: "United States",
    currentStage: "documentation", 
    stageProgress: 30,
    nextAction: "Pending Financial Documents",
    nextActionDeadline: "Overdue by 1 week",
    status: "At Risk",
    healthScore: 45,
    assignedTo: "Rahul Gupta",
    lastUpdated: "10 days ago",
  },
  { 
    id: 4, 
    clientName: "Vikram Singh", 
    service: "Business Visa",
    country: "United Kingdom",
    currentStage: "consultation", 
    stageProgress: 90,
    nextAction: "Document List Preparation",
    nextActionDeadline: "Tomorrow",
    status: "On Track",
    healthScore: 60,
    assignedTo: "Meera Kapoor",
    lastUpdated: "3 days ago",
  },
  { 
    id: 5, 
    clientName: "Meera Kapoor", 
    service: "Family Visa",
    country: "New Zealand",
    currentStage: "decision", 
    stageProgress: 100,
    nextAction: "Visa Approved - Travel Planning",
    nextActionDeadline: "Complete within 2 weeks",
    status: "Complete",
    healthScore: 90,
    assignedTo: "Vikram Singh",
    lastUpdated: "Yesterday",
  },
];

// Recommended next actions based on client's stage
const getRecommendedActions = (currentStage: string) => {
  switch (currentStage) {
    case 'inquiry':
      return [
        { action: 'Schedule initial consultation', priority: 'High' },
        { action: 'Send information package', priority: 'Medium' },
      ];
    case 'consultation':
      return [
        { action: 'Create document checklist', priority: 'High' },
        { action: 'Explain visa process timeline', priority: 'Medium' },
        { action: 'Collect basic information', priority: 'High' },
      ];
    case 'documentation':
      return [
        { action: 'Review submitted documents', priority: 'High' },
        { action: 'Request missing documents', priority: 'High' },
        { action: 'Verify document authenticity', priority: 'Medium' },
      ];
    case 'submission':
      return [
        { action: 'Submit application to embassy', priority: 'High' },
        { action: 'Pay application fees', priority: 'High' },
        { action: 'Send confirmation to client', priority: 'Medium' },
      ];
    case 'processing':
      return [
        { action: 'Check application status', priority: 'Medium' },
        { action: 'Prepare for potential RFEs', priority: 'Low' },
        { action: 'Update client on timeline', priority: 'Medium' },
      ];
    case 'interview':
      return [
        { action: 'Conduct interview preparation', priority: 'High' },
        { action: 'Review common questions', priority: 'Medium' },
        { action: 'Schedule mock interview', priority: 'Medium' },
      ];
    case 'decision':
      return [
        { action: 'Check visa decision', priority: 'High' },
        { action: 'Notify client of outcome', priority: 'High' },
        { action: 'Plan next steps based on outcome', priority: 'Medium' },
      ];
    default:
      return [
        { action: 'Schedule follow-up call', priority: 'Medium' },
        { action: 'Check for document updates', priority: 'Medium' },
      ];
  }
};

export default function LifecyclePage() {
  const [selectedApplication, setSelectedApplication] = useState<any>(clientApplications[0]);
  
  // Find the index of current stage in the lifecycle
  const currentStageIndex = visaStages.findIndex(stage => stage.id === selectedApplication.currentStage);
  
  // Get appropriate status colors
  const getStatusColor = (status: string) => {
    switch (status) {
      case "On Track":
        return "bg-green-100 text-green-800";
      case "At Risk":
        return "bg-amber-100 text-amber-800";
      case "Delayed":
        return "bg-red-100 text-red-800";
      case "Complete":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return "text-green-500";
    if (score >= 60) return "text-amber-500";
    return "text-red-500";
  };

  // Get recommended actions
  const recommendedActions = getRecommendedActions(selectedApplication.currentStage);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Client Applications List */}
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Active Applications</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {clientApplications.map((application) => (
                  <div 
                    key={application.id}
                    className={`p-4 cursor-pointer hover:bg-gray-50 ${
                      selectedApplication.id === application.id ? 'bg-gray-50 border-l-4 border-indigo-500' : ''
                    }`}
                    onClick={() => setSelectedApplication(application)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{application.clientName}</h4>
                        <p className="text-sm text-gray-600">{application.service} • {application.country}</p>
                      </div>
                      <Badge className={getStatusColor(application.status)}>
                        {application.status}
                      </Badge>
                    </div>
                    <div className="mt-2">
                      <div className="text-xs text-gray-500">Current Stage: {
                        visaStages.find(stage => stage.id === application.currentStage)?.label
                      }</div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                        <div 
                          className="bg-indigo-600 h-1.5 rounded-full" 
                          style={{ width: `${application.stageProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Lifecycle View */}
        <div className="md:col-span-2">
          <Card className="mb-6">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-lg">{selectedApplication.clientName}</CardTitle>
                  <p className="text-sm text-gray-600">
                    {selectedApplication.service} • {selectedApplication.country}
                  </p>
                </div>
                <Badge className={getStatusColor(selectedApplication.status)}>
                  {selectedApplication.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium">Application Progress</span>
                  <span className="text-sm font-medium text-indigo-600">
                    {selectedApplication.stageProgress}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-indigo-600 h-2 rounded-full" 
                    style={{ width: `${selectedApplication.stageProgress}%` }}
                  ></div>
                </div>
              </div>

              {/* Visa Lifecycle Timeline */}
              <div className="space-y-4">
                <h3 className="font-medium">Visa Application Lifecycle</h3>
                <div className="relative">
                  {visaStages.map((stage, index) => {
                    // Determine if stage is completed, current, or upcoming
                    const isCompleted = index < currentStageIndex;
                    const isCurrent = index === currentStageIndex;
                    const isUpcoming = index > currentStageIndex;

                    return (
                      <div key={stage.id} className="flex items-start mb-4">
                        <div 
                          className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                            isCompleted 
                              ? 'bg-green-100 text-green-600' 
                              : isCurrent 
                                ? 'bg-indigo-100 text-indigo-600 ring-2 ring-indigo-600 ring-offset-2' 
                                : 'bg-gray-100 text-gray-400'
                          }`}
                        >
                          {stage.icon}
                        </div>
                        <div className="ml-4 flex-grow">
                          <div className="flex justify-between">
                            <h4 className={`font-medium ${
                              isCompleted 
                                ? 'text-green-600' 
                                : isCurrent 
                                  ? 'text-indigo-600' 
                                  : 'text-gray-500'
                            }`}>{stage.label}</h4>
                            {isCompleted && <CheckCircle2 className="h-4 w-4 text-green-600" />}
                            {isCurrent && <Clock className="h-4 w-4 text-indigo-600" />}
                          </div>
                          {isCurrent && (
                            <div className="mt-1 text-sm bg-indigo-50 p-2 rounded-md">
                              <p className="font-medium text-indigo-700">Next Action: {selectedApplication.nextAction}</p>
                              <p className="text-indigo-600">{selectedApplication.nextActionDeadline}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI Recommendations and Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recommended Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recommendedActions.map((action, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className={`rounded-full p-1 mt-0.5 ${
                        action.priority === 'High'
                          ? 'bg-red-100'
                          : action.priority === 'Medium'
                            ? 'bg-amber-100'
                            : 'bg-blue-100'
                      }`}>
                        <AlertTriangle className={`h-4 w-4 ${
                          action.priority === 'High'
                            ? 'text-red-600'
                            : action.priority === 'Medium'
                              ? 'text-amber-600'
                              : 'text-blue-600'
                        }`} />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{action.action}</span>
                          <Badge className={`text-xs ${
                            action.priority === 'High'
                              ? 'bg-red-100 text-red-800'
                              : action.priority === 'Medium'
                                ? 'bg-amber-100 text-amber-800'
                                : 'bg-blue-100 text-blue-800'
                          }`}>
                            {action.priority}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <Button className="w-full mt-4">Execute Selected Actions</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Application Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-sm text-gray-500">Health Score</p>
                    <p className={`font-medium ${getHealthScoreColor(selectedApplication.healthScore)}`}>
                      {selectedApplication.healthScore}%
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Assigned To</p>
                    <p className="font-medium">{selectedApplication.assignedTo}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Last Updated</p>
                    <p className="font-medium">{selectedApplication.lastUpdated}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Current Stage</p>
                    <p className="font-medium">
                      {visaStages.find(stage => stage.id === selectedApplication.currentStage)?.label}
                    </p>
                  </div>
                </div>

                <div className="pt-2 flex flex-col gap-2">
                  <Button variant="outline" size="sm">
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule Follow-up
                  </Button>
                  <Button variant="outline" size="sm">
                    <File className="h-4 w-4 mr-2" />
                    Manage Documents
                  </Button>
                  <Button variant="default" size="sm">
                    <ChevronRight className="h-4 w-4 mr-2" />
                    Advance to Next Stage
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
} 