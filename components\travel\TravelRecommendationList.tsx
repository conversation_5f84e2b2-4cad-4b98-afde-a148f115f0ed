"use client"

import React, { useState, useEffect } from 'react';
import TravelRecommendationCard, { TravelRecommendation } from './TravelRecommendationCard';
import { Globe, Search, Filter, MapPin, ArrowRight } from 'lucide-react';

interface TravelRecommendationListProps {
  userId: string;
}

const TravelRecommendationList: React.FC<TravelRecommendationListProps> = ({ userId }) => {
  const [recommendations, setRecommendations] = useState<TravelRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  
  const fetchRecommendations = async (country?: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const url = new URL('/api/travel-recommendations', window.location.origin);
      url.searchParams.append('userId', userId);
      if (country) {
        url.searchParams.append('country', country);
      }
      
      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new Error('Failed to fetch travel recommendations');
      }
      
      const data = await response.json();
      setRecommendations(data);
    } catch (err) {
      console.error('Error fetching recommendations:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchRecommendations(selectedCountry || undefined);
  }, [userId, selectedCountry]);
  
  const handleToggleBookmark = async (id: string, value: boolean) => {
    try {
      const response = await fetch('/api/travel-recommendations', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, isBookmarked: value }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update bookmark status');
      }
      
      // Update local state
      setRecommendations(prevRecs => 
        prevRecs.map(rec => rec.id === id ? { ...rec, isBookmarked: value } : rec)
      );
    } catch (err) {
      console.error('Error toggling bookmark:', err);
    }
  };
  
  // Handle country filter changes
  const handleCountryChange = (country: string) => {
    setSelectedCountry(country === 'all' ? null : country);
  };
  
  // Sample countries for the filter dropdown
  const countries = [
    { code: 'all', name: 'All Destinations' },
    { code: 'US', name: 'United States' },
    { code: 'GB', name: 'United Kingdom' },
    { code: 'IN', name: 'India' },
    { code: 'CA', name: 'Canada' },
    { code: 'AU', name: 'Australia' },
  ];
  
  // Render loading skeleton
  if (loading) {
    return (
      <div>
        <div className="flex justify-between items-center mb-4">
          <div className="h-5 w-32 bg-indigo-200 animate-pulse rounded-full"></div>
          <div className="w-40 h-8 bg-indigo-200 animate-pulse rounded-lg"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="bg-white rounded-xl shadow-md overflow-hidden h-64 animate-pulse">
              <div className="h-36 bg-indigo-200"></div>
              <div className="p-3">
                <div className="h-5 bg-indigo-200 rounded-full mb-2 w-3/4"></div>
                <div className="h-4 bg-indigo-200 rounded-full mb-2 w-1/2"></div>
                <div className="h-3 bg-indigo-200 rounded-full w-full mb-1"></div>
                <div className="h-3 bg-indigo-200 rounded-full w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  
  // Render error message
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl flex items-start">
        <div className="bg-red-100 p-2 rounded-full mr-3 flex-shrink-0">
          <svg className="w-5 h-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div>
          <h2 className="text-sm font-semibold mb-1 text-red-800">Unable to load recommendations</h2>
          <p className="text-xs text-red-700 mb-2">{error}</p>
          <button 
            onClick={() => fetchRecommendations(selectedCountry || undefined)}
            className="mt-1 text-white bg-red-600 hover:bg-red-700 px-3 py-1.5 rounded-lg text-xs font-medium transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div>
      <div className="flex flex-wrap justify-between items-center mb-4">
        <div className="flex items-center gap-2 hidden">
          <span className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white text-xs font-medium px-3 py-1 rounded-full">Suggested for you</span>
        </div>
        <div className="flex items-center gap-3 ml-auto">
          <span className="text-sm text-indigo-700 hidden sm:inline-block font-medium">Filter by:</span>
          <div className="relative">
            <select 
              value={selectedCountry || 'all'} 
              onChange={(e) => handleCountryChange(e.target.value)}
              className="appearance-none border border-indigo-300 rounded-lg text-sm py-2 pl-3 pr-10 bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500 shadow-sm text-indigo-800 font-medium cursor-pointer"
            >
              {countries.map(country => (
                <option key={country.code} value={country.code}>{country.name}</option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <Filter className="h-4 w-4 text-indigo-500" />
            </div>
          </div>
        </div>
      </div>
      
      {recommendations.length === 0 ? (
        <div className="text-center p-6 bg-indigo-50 rounded-xl border border-indigo-100 shadow-inner">
          <div className="bg-white w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-3 shadow-sm">
            <Globe className="w-8 h-8 text-indigo-400" />
          </div>
          <p className="text-indigo-800 font-semibold text-sm mb-1">No travel recommendations found</p>
          <p className="text-xs text-indigo-600 mb-3">Try selecting a different country or check back later</p>
          <button 
            onClick={() => setSelectedCountry(null)}
            className="bg-indigo-100 hover:bg-indigo-200 text-indigo-700 text-xs py-1.5 px-3 rounded-lg inline-flex items-center transition-colors font-medium"
          >
            <MapPin className="w-3.5 h-3.5 mr-1.5" />
            View all destinations
          </button>
        </div>
      ) : (
        <div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {recommendations.map(recommendation => (
              <div key={recommendation.id} className="transform transition-all duration-300 hover:scale-[1.02] hover:-translate-y-1">
                <TravelRecommendationCard
                  recommendation={recommendation}
                  onToggleBookmark={handleToggleBookmark}
                />
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-5">
            <button className="bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-indigo-700 hover:to-purple-800 transition-all shadow-md flex items-center group">
              <span>View more recommendations</span>
              <ArrowRight className="ml-2 w-4 h-4 transform transition-transform duration-300 group-hover:translate-x-1" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TravelRecommendationList; 