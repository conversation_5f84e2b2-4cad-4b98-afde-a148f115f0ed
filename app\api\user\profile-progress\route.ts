import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Authentication removed - return mock progress data
    const progressData = {
      userId: "mock-user-id",
      profileProgress: 30,
      profileCompleted: false,
      completedSteps: [
        'basic_info'
      ],
      nextStep: 'contact_details'
    };

    return NextResponse.json(progressData);
  } catch (error) {
    console.error('API: Error fetching profile progress:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}