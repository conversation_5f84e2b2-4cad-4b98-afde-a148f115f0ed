"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import GlassCard from "@/components/GlassCard";
import { 
  Users, 
  UserCheck, 
  Clock, 
  FileText, 
  Calendar, 
  ArrowUpRight, 
  BookOpen, 
  Shield, 
  BarChart, 
  User, 
  Briefcase,
  TrendingUp,
  AlertCircle,
  CheckCircle2,
  ChevronRight
} from "lucide-react";
import RoleBasedAccess from "@/components/auth/RoleBasedAccess";

// Mock data for the dashboard
const hrMetrics = {
  totalEmployees: 247,
  openPositions: 18,
  timeToHire: 23,
  upcomingReviews: 15,
  complianceScore: 97,
  attritionRate: 8.5,
  trainingCompletion: 89,
  employeeEngagement: 4.2
};

const recentHires = [
  { id: 1, name: "<PERSON><PERSON> <PERSON>", role: "Immigration Specialist", department: "Visa Processing", date: "2 days ago" },
  { id: 2, name: "<PERSON>", role: "Client Relations Manager", department: "Customer Service", date: "1 week ago" },
  { id: 3, name: "<PERSON>", role: "Travel Consultant", department: "Bookings", date: "2 weeks ago" }
];

const upcomingTrainings = [
  { id: 1, title: "Advanced Immigration Law Course", date: "May 20, 2025", enrolledCount: 15, status: "Upcoming" },
  { id: 2, title: "Conflict Resolution Workshop", date: "May 25, 2025", enrolledCount: 22, status: "Upcoming" },
  { id: 3, title: "Visa Documentation Best Practices", date: "June 5, 2025", enrolledCount: 18, status: "Registration Open" }
];

const hrAlerts = [
  { id: 1, title: "GDPR Compliance Update Required", type: "regulatory", priority: "high" },
  { id: 2, title: "5 Employee Reviews Due This Week", type: "performance", priority: "medium" },
  { id: 3, title: "Potential Attrition Risk: Sales Team", type: "retention", priority: "high" }
];

export default function HrDashboard() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <RoleBasedAccess allowedRoles={['admin', 'hr']}>
      <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
          HR Dashboard
        </h1>
        <div className="flex items-center gap-3">
          <button className="px-4 py-2 border border-indigo-300 rounded-lg text-sm bg-white text-indigo-800 font-medium hover:bg-indigo-50/70 transition-colors shadow-sm">
            Export Report
          </button>
          <button className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Review
          </button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6" onValueChange={setActiveTab}>
        <div className="bg-white/50 backdrop-blur-sm p-1 rounded-lg inline-flex">
          <TabsList className="bg-transparent">
            <TabsTrigger 
              value="overview"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger 
              value="talent"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              Talent
            </TabsTrigger>
            <TabsTrigger 
              value="compliance"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              Compliance
            </TabsTrigger>
            <TabsTrigger 
              value="development"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-700 data-[state=active]:text-white"
            >
              Development
            </TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="overview" className="space-y-6">
          {/* Key metrics */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Total Employees</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-400 to-indigo-300 flex items-center justify-center text-white shadow-sm">
                  <Users size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">{hrMetrics.totalEmployees}</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>+4 from last month</span>
                </p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Open Positions</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-indigo-400 to-purple-300 flex items-center justify-center text-white shadow-sm">
                  <Briefcase size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">{hrMetrics.openPositions}</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>+2 new this week</span>
                </p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Avg. Time to Hire</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-amber-400 to-orange-300 flex items-center justify-center text-white shadow-sm">
                  <Clock size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">{hrMetrics.timeToHire} days</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>-2 days improvement</span>
                </p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Compliance Score</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-green-400 to-emerald-300 flex items-center justify-center text-white shadow-sm">
                  <Shield size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">{hrMetrics.complianceScore}%</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>+3% from previous audit</span>
                </p>
              </div>
            </GlassCard>
          </div>
          
          {/* Secondary metrics */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Attrition Rate</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-300 flex items-center justify-center text-white shadow-sm">
                  <TrendingUp size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">{hrMetrics.attritionRate}%</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>-1.2% from last quarter</span>
                </p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Upcoming Reviews</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-400 to-cyan-300 flex items-center justify-center text-white shadow-sm">
                  <FileText size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">{hrMetrics.upcomingReviews}</div>
                <p className="text-xs text-indigo-600 mt-1">Next due in 3 days</p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Training Completion</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-indigo-400 to-blue-300 flex items-center justify-center text-white shadow-sm">
                  <BookOpen size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">{hrMetrics.trainingCompletion}%</div>
                <p className="text-xs text-indigo-600 flex items-center gap-1 mt-1">
                  <TrendingUp size={12} className="text-green-500" />
                  <span>+5% from last month</span>
                </p>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-indigo-800">Employee Engagement</h3>
                <div className="h-8 w-8 rounded-full bg-gradient-to-br from-amber-400 to-yellow-300 flex items-center justify-center text-white shadow-sm">
                  <BarChart size={16} />
                </div>
              </div>
              <div className="pt-2">
                <div className="text-2xl font-bold text-indigo-900">{hrMetrics.employeeEngagement}/5</div>
                <p className="text-xs text-indigo-600 mt-1">Based on latest survey</p>
              </div>
            </GlassCard>
          </div>
          
          {/* Alert Cards */}
          <div className="grid gap-6 md:grid-cols-2">
            <GlassCard className="p-6">
              <div className="flex flex-col h-full">
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">HR Alerts</h3>
                <p className="text-sm text-gray-600 mb-4">Critical action items requiring attention</p>
                
                <div className="space-y-3 flex-1">
                  {hrAlerts.map(alert => (
                    <div 
                      key={alert.id} 
                      className="bg-white/50 backdrop-blur-sm border border-indigo-100/30 rounded-lg p-3"
                    >
                      <div className="flex items-start">
                        <div 
                          className={`h-8 w-8 rounded-full flex items-center justify-center shadow-sm mr-3 ${
                            alert.priority === 'high' 
                              ? 'bg-gradient-to-br from-red-400 to-rose-300 text-white' 
                              : alert.priority === 'medium' 
                                ? 'bg-gradient-to-br from-amber-400 to-orange-300 text-white' 
                                : 'bg-gradient-to-br from-blue-400 to-indigo-300 text-white'
                          }`}
                        >
                          <AlertCircle size={16} />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-indigo-800">{alert.title}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full bg-indigo-100 text-indigo-700">{alert.type}</span>
                            <span 
                              className={`inline-flex items-center text-xs font-medium px-2 py-0.5 rounded-full ${
                                alert.priority === 'high'
                                  ? 'bg-red-100 text-red-700' 
                                  : alert.priority === 'medium' 
                                    ? 'bg-amber-100 text-amber-700' 
                                    : 'bg-blue-100 text-blue-700'
                              }`}
                            >
                              {alert.priority}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <button className="mt-4 self-end text-xs text-indigo-600 hover:text-indigo-800 transition-colors flex items-center">
                  View All Alerts
                  <ChevronRight size={14} className="ml-1" />
                </button>
              </div>
            </GlassCard>
            
            <GlassCard className="p-6">
              <div className="flex flex-col h-full">
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">Recent Hires</h3>
                <p className="text-sm text-gray-600 mb-4">New team members joining the organization</p>
                
                <div className="space-y-3 flex-1">
                  {recentHires.map(hire => (
                    <div 
                      key={hire.id} 
                      className="bg-white/50 backdrop-blur-sm border border-indigo-100/30 rounded-lg p-3"
                    >
                      <div className="flex items-center">
                        <div className="h-9 w-9 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 flex items-center justify-center text-white font-medium shadow-sm mr-3">
                          {hire.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-indigo-800">{hire.name}</p>
                          <p className="text-xs text-indigo-600">{hire.role} • {hire.department}</p>
                        </div>
                        <div className="text-xs text-gray-500">{hire.date}</div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <button className="mt-4 px-4 py-2 bg-indigo-50/70 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-100/70 transition-colors flex items-center justify-center w-full">
                  <ArrowUpRight className="mr-2 h-4 w-4" />
                  View All Hires
                </button>
              </div>
            </GlassCard>
          </div>
          
          <GlassCard className="p-6">
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold text-indigo-800 mb-2">Upcoming Training Programs</h3>
              <p className="text-sm text-gray-600 mb-4">Professional development opportunities</p>
              
              <div className="overflow-hidden">
                <div className="grid gap-6 md:grid-cols-3">
                  {upcomingTrainings.map((training) => (
                    <div 
                      key={training.id}
                      className="bg-white/50 backdrop-blur-sm border border-indigo-100/30 rounded-lg p-4 transition-all hover:translate-y-[-4px]"
                    >
                      <div className="flex flex-col h-full">
                        <div className="flex justify-between items-start mb-2">
                          <div className="px-2.5 py-1 rounded-full bg-indigo-50/70 text-xs font-medium text-indigo-700">
                            {training.status}
                          </div>
                          <div className="text-xs text-gray-500">{training.date}</div>
                        </div>
                        <h4 className="text-sm font-medium text-indigo-800 mb-2 flex-1">{training.title}</h4>
                        <div className="flex items-center">
                          <div className="flex -space-x-2">
                            {[...Array(3)].map((_, i) => (
                              <div key={i} className="h-6 w-6 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 flex items-center justify-center text-white text-xs font-medium shadow-sm border border-white">
                                {String.fromCharCode(65 + i)}
                              </div>
                            ))}
                          </div>
                          <div className="text-xs text-indigo-600 ml-2">
                            {training.enrolledCount} enrolled
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <button className="mt-6 self-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-700 text-white rounded-lg text-sm font-medium hover:from-indigo-700 hover:to-purple-800 transition-all shadow-md flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                View Training Calendar
              </button>
            </div>
          </GlassCard>
        </TabsContent>
        
        <TabsContent value="talent" className="space-y-6">
          <GlassCard className="p-6">
            <h3 className="text-lg font-semibold text-indigo-800 mb-2">Talent Management</h3>
            <p className="text-sm text-gray-600 mb-4">View and manage employee profiles, skills, and career paths</p>
            
            <div className="bg-indigo-50/50 rounded-lg p-4 text-center h-64 flex items-center justify-center">
              <div className="text-sm text-indigo-600">Talent Management Content Goes Here</div>
            </div>
          </GlassCard>
        </TabsContent>
        
        <TabsContent value="compliance" className="space-y-6">
          <GlassCard className="p-6">
            <h3 className="text-lg font-semibold text-indigo-800 mb-2">Regulatory Compliance</h3>
            <p className="text-sm text-gray-600 mb-4">Ensure adherence to labor laws and organizational policies</p>
            
            <div className="bg-indigo-50/50 rounded-lg p-4 text-center h-64 flex items-center justify-center">
              <div className="text-sm text-indigo-600">Compliance Dashboard Content Goes Here</div>
            </div>
          </GlassCard>
        </TabsContent>
        
        <TabsContent value="development" className="space-y-6">
          <GlassCard className="p-6">
            <h3 className="text-lg font-semibold text-indigo-800 mb-2">Skill Development</h3>
            <p className="text-sm text-gray-600 mb-4">Training programs and professional development tracking</p>
            
            <div className="bg-indigo-50/50 rounded-lg p-4 text-center h-64 flex items-center justify-center">
              <div className="text-sm text-indigo-600">Development Programs Content Goes Here</div>
            </div>
          </GlassCard>
        </TabsContent>
      </Tabs>
      </div>
    </RoleBasedAccess>
  );
}