import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Return user data from Clerk
    const userData = {
      id: userId,
      email: "<EMAIL>", // You can get this from Clerk user object
      name: "Demo User", // You can get this from Clerk user object
      image: null,
      role: 'USER',
      status: 'ACTIVE',
      profileCompleted: false,
      profileProgress: 30,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
    }

    return NextResponse.json({ user: userData })
  } catch (error) {
    console.error('API: Error fetching user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Update user data
export async function PUT(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json()

    if (!data) {
      return NextResponse.json({ error: 'No data provided' }, { status: 400 })
    }

    // Return updated user data with Clerk user ID
    const updatedUserData = {
      id: userId,
      email: "<EMAIL>",
      name: data.name || "Demo User",
      image: null,
      role: 'USER',
      status: 'ACTIVE',
      profileCompleted: false,
      profileProgress: 30,
      phone: data.phone || '',
      country: data.country || '',
      updatedAt: new Date().toISOString()
    }

    return NextResponse.json({ user: updatedUserData })

  } catch (error) {
    console.error('API: Error updating user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}