"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { BookOpen, FileText, Video, HelpCircle } from "lucide-react";

interface SidebarLinkProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
}

function SidebarLink({ href, icon, label, active }: SidebarLinkProps) {
  return (
    <Link href={href as any}>
      <div
        className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${
          active
            ? "bg-gradient-to-r from-indigo-600/20 to-purple-700/20 text-indigo-800 font-medium"
            : "text-gray-600 hover:bg-indigo-50/70 hover:text-indigo-700"
        }`}
      >
        <div className={`text-lg ${active ? 'text-indigo-600' : 'text-indigo-400'}`}>{icon}</div>
        <span>{label}</span>
      </div>
    </Link>
  );
}

function ResourcesSidebar() {
  const pathname = usePathname();
  
  const isActive = (path: string): boolean => {
    return pathname === path || (pathname?.startsWith(`${path}/`) ?? false);
  };

  return (
    <div className="p-4 bg-white/80 backdrop-blur-lg rounded-xl shadow-lg border border-indigo-100/30">
      <h2 className="text-lg font-semibold mb-4 text-indigo-800">Resources</h2>
      <nav className="space-y-1">
        <SidebarLink 
          href="/resources" 
          icon={<BookOpen size={18} />} 
          label="Overview" 
          active={pathname === "/resources"}
        />
        <SidebarLink 
          href="/resources/guides" 
          icon={<FileText size={18} />} 
          label="Guides" 
          active={isActive("/resources/guides")}
        />
        <SidebarLink 
          href="/resources/webinars" 
          icon={<Video size={18} />} 
          label="Webinars" 
          active={isActive("/resources/webinars")}
        />
        <SidebarLink 
          href="/resources/faqs" 
          icon={<HelpCircle size={18} />} 
          label="FAQs" 
          active={isActive("/resources/faqs")}
        />
      </nav>
    </div>
  );
}

export default function ResourcesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-700 mb-6">Resource Center</h1>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div className="md:col-span-1">
          <ResourcesSidebar />
        </div>
        <div className="md:col-span-3">
          <div className="rounded-xl p-6 bg-white/80 backdrop-blur-lg shadow-lg border border-indigo-100/30">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
} 