"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { 
  Users, 
  BarChart, 
  Shield, 
  BookOpen, 
  Home, 
  Settings, 
  Bell,
  User,
  Menu,
  X,
  LogOut
} from "lucide-react";

interface SidebarLinkProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
}

function SidebarLink({ href, icon, label, active }: SidebarLinkProps) {
  return (
    <Link href={href as any}>
      <div
        className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${
          active
            ? "bg-gradient-to-r from-indigo-600/20 to-purple-700/20 text-indigo-800 font-medium"
            : "text-gray-600 hover:bg-indigo-50/70 hover:text-indigo-700"
        }`}
      >
        <div className={`text-lg ${active ? 'text-indigo-600' : 'text-indigo-400'}`}>{icon}</div>
        <span>{label}</span>
      </div>
    </Link>
  );
}

const navItems = [
  { 
    id: 'dashboard', 
    label: 'Dashboard', 
    icon: <Home className="w-5 h-5" />, 
    path: '/hr' 
  },
  { 
    id: 'talent', 
    label: 'Talent Hub', 
    icon: <Users className="w-5 h-5" />, 
    path: '/hr/talent' 
  },
  { 
    id: 'performance', 
    label: 'Team Analytics', 
    icon: <BarChart className="w-5 h-5" />, 
    path: '/hr/performance' 
  },
  { 
    id: 'compliance', 
    label: 'Regulatory Center', 
    icon: <Shield className="w-5 h-5" />, 
    path: '/hr/compliance' 
  },
  { 
    id: 'learning', 
    label: 'Skill Development', 
    icon: <BookOpen className="w-5 h-5" />, 
    path: '/hr/learning' 
  },
  { 
    id: 'settings', 
    label: 'Settings', 
    icon: <Settings className="w-5 h-5" />, 
    path: '/hr/settings' 
  }
];

export default function HrLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
      if (window.innerWidth < 1024) {
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const isActive = (path: string): boolean => {
    return pathname === path || (pathname?.startsWith(`${path}/`) ?? false);
  };

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Sidebar */}
      <aside
        className={`bg-white/80 backdrop-blur-lg fixed lg:static inset-y-0 left-0 z-50 transform ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        } lg:translate-x-0 transition-transform duration-300 ease-in-out w-64 border-r border-indigo-100/30 shadow-lg`}
      >
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between h-16 px-4 border-b border-indigo-100/30">
            <Link href="/hr" className="flex items-center">
              <div className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700">Visa Mentor HR</div>
            </Link>
            {isMobile && (
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden p-1 rounded-md text-indigo-500 hover:text-indigo-700 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            )}
          </div>
          
          <div className="p-4 flex-1 overflow-auto">
            <div className="mb-6">
              <div className="text-xs uppercase tracking-wider text-indigo-600 font-medium px-4 mb-2">Main</div>
              
              <nav className="space-y-1">
                {navItems.map((item) => (
                  <SidebarLink 
                    key={item.id}
                    href={item.path} 
                    icon={item.icon} 
                    label={item.label} 
                    active={isActive(item.path)}
                  />
                ))}
              </nav>
            </div>
          </div>
          
          <div className="p-4 border-t border-indigo-100/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="h-9 w-9 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 flex items-center justify-center text-white font-medium shadow-sm mr-3">
                  AK
                </div>
                <div>
                  <div className="text-sm font-medium text-indigo-800">Anjali Kumar</div>
                  <div className="text-xs text-indigo-600">HR Director</div>
                </div>
              </div>
              <Link href="/">
                <button className="text-indigo-500 hover:text-indigo-700 p-1.5 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                  <LogOut size={18} />
                </button>
              </Link>
            </div>
          </div>
        </div>
      </aside>
      
      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {/* Top navigation */}
        <header className="bg-white/80 backdrop-blur-lg border-b border-indigo-100/30 shadow-sm">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex">
                {isMobile && (
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="lg:hidden p-1 rounded-md -ml-1 text-indigo-500 hover:text-indigo-700 transition-colors"
                  >
                    <Menu className="h-6 w-6" />
                  </button>
                )}
                <h1 className="ml-3 text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-700">
                  {navItems.find(item => isActive(item.path))?.label || 'HR Dashboard'}
                </h1>
              </div>
              <div className="flex items-center gap-4">
                <div className="relative">
                  <button className="p-1.5 rounded-full bg-indigo-50/70 backdrop-blur-sm text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                    <Bell className="h-5 w-5" />
                  </button>
                  <span className="absolute top-0 right-0 h-2.5 w-2.5 rounded-full bg-gradient-to-r from-red-500 to-rose-600 border-2 border-white"></span>
                </div>
                <div className="relative">
                  <button className="p-1.5 rounded-full bg-indigo-50/70 backdrop-blur-sm text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                    <User className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
} 