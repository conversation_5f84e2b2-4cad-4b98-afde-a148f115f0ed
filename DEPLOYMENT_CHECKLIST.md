# Visa Mentor Deployment Checklist

## Pre-Deployment Tasks

1. **Environment Configuration**
   - [ ] Copy `env.production.template` to `.env.production`
   - [ ] Update database credentials in `.env.production`
   - [ ] Set proper JWT_SECRET and NEXTAUTH_SECRET values
   - [ ] Configure NEXTAUTH_URL to match your production domain

2. **Database Setup**
   - [ ] Set up MySQL database server
   - [ ] Create database named `visa_mentor`
   - [ ] Run database migrations: `npx prisma migrate deploy`
   - [ ] Verify database schema is correctly applied

3. **Build Application**
   - [ ] Run the deployment script: `node deploy-production.js`
   - [ ] Verify build completes successfully
   - [ ] Check for any warnings that need addressing

## Deployment Options

### Option 1: Vercel Deployment (Recommended)
- [ ] Install Vercel CLI: `npm i -g vercel`
- [ ] Run `vercel login`
- [ ] Deploy with `vercel --prod`
- [ ] Configure environment variables in Vercel dashboard
- [ ] Set up database connection

### Option 2: Traditional Hosting
- [ ] Upload the `.next`, `public`, `prisma` directories and package files
- [ ] Install dependencies on server: `npm ci --production`
- [ ] Set up process manager (PM2): `pm2 start npm --name "visa-mentor" -- start`
- [ ] Configure reverse proxy (Nginx/Apache) to point to application port

### Option 3: Docker Deployment
- [ ] Build Docker image: `docker build -t visa-mentor .`
- [ ] Run container: `docker run -p 3000:3000 -d visa-mentor`
- [ ] Configure database connection

## Post-Deployment Tasks

1. **Verify Application**
   - [ ] Test authentication flows
   - [ ] Verify all roles work correctly (admin, sales, user)
   - [ ] Check database connections and queries
   - [ ] Test lead routing system

2. **Security Checks**
   - [ ] Ensure all secrets are properly set
   - [ ] Verify HTTPS is configured
   - [ ] Check for exposed environment variables
   - [ ] Review authentication mechanisms

3. **Performance Optimization**
   - [ ] Enable caching where appropriate
   - [ ] Verify API response times
   - [ ] Check for any slow database queries

4. **Monitoring Setup**
   - [ ] Set up application monitoring
   - [ ] Configure error logging
   - [ ] Set up database monitoring
   - [ ] Create alerts for critical failures

## Troubleshooting Common Issues

- **Database Connection Errors**: Verify database credentials and network access
- **Authentication Issues**: Check JWT_SECRET and NEXTAUTH_SECRET values
- **Build Failures**: Use `SKIP_TYPE_CHECK=true` for temporary workarounds
- **API Errors**: Check server logs for detailed error messages 