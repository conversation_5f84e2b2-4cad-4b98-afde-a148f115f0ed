"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON>, 
  BarChart, 
  Award, 
  TrendingUp,
  ArrowUpRight, 
  Calendar, 
  Download,
  FileText,
  Target,
  Star
} from "lucide-react";

// Mock data for performance metrics
const competencyMatrix = {
  skills: ["Visa Law", "Client Empathy", "Document Accuracy", "Case Management", "Communication"],
  ratings: {
    "John Doe": [4.2, 4.8, 4.5, 4.1, 4.7],
    "<PERSON><PERSON>": [4.7, 4.9, 4.6, 4.8, 4.5],
    "<PERSON>": [3.9, 4.3, 4.8, 4.0, 4.2],
    "<PERSON>": [4.5, 4.6, 4.7, 4.3, 4.9]
  }
};

// Team metrics data
const teamMetrics = {
  totalTeams: 8,
  avgTeamSize: 6.2,
  highPerformingTeams: 3,
  avgTeamRating: 4.3
};

// Department performance data
const departmentPerformance = [
  { name: "Visa Processing", rating: 4.6, change: "+0.3", reviewCount: 156 },
  { name: "Client Relations", rating: 4.7, change: "+0.2", reviewCount: 203 },
  { name: "Documentation", rating: 4.5, change: "+0.4", reviewCount: 127 },
  { name: "Advisory", rating: 4.8, change: "+0.5", reviewCount: 142 },
  { name: "Legal Review", rating: 4.4, change: "+0.1", reviewCount: 98 }
];

// Performance reviews data
const upcomingReviews = [
  { id: 1, employeeName: "David Chen", position: "Visa Consultant", department: "Advisory", date: "May 15, 2025", reviewer: "Sophia Williams" },
  { id: 2, employeeName: "Aisha Patel", position: "Client Success Representative", department: "Client Relations", date: "May 18, 2025", reviewer: "James Rodriguez" },
  { id: 3, employeeName: "Li Wei", position: "Documentation Specialist", department: "Documentation", date: "May 22, 2025", reviewer: "Sarah Johnson" }
];

export default function TeamPerformance() {
  const [activeTab, setActiveTab] = useState("overview");

  // Helper function to render rating stars
  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        <span className="mr-2 font-medium">{rating.toFixed(1)}</span>
        <div className="flex items-center">
          {[1, 2, 3, 4, 5].map((star) => (
            <Star 
              key={star} 
              className={`h-4 w-4 ${star <= Math.round(rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`} 
            />
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Team Analytics</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button>
            <Calendar className="mr-2 h-4 w-4" />
            Schedule Review
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="skills">Skills Matrix</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          {/* Team Performance Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Teams</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{teamMetrics.totalTeams}</div>
                <p className="text-xs text-muted-foreground">
                  Across 4 departments
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Team Size</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{teamMetrics.avgTeamSize}</div>
                <p className="text-xs text-muted-foreground">
                  -0.3 from last quarter
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">High Performing Teams</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{teamMetrics.highPerformingTeams}</div>
                <p className="text-xs text-muted-foreground">
                  +1 from last quarter
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Team Rating</CardTitle>
                <BarChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{teamMetrics.avgTeamRating}/5</div>
                <p className="text-xs text-muted-foreground">
                  +0.2 from last quarter
                </p>
              </CardContent>
            </Card>
          </div>
          
          {/* Department Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Department Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {departmentPerformance.map((dept) => (
                  <div key={dept.name} className="flex flex-col space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{dept.name}</p>
                        <p className="text-xs text-muted-foreground">{dept.reviewCount} reviews</p>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <Badge variant="outline" className={`${
                            parseFloat(dept.change) > 0.3 
                              ? "text-green-600 border-green-600" 
                              : "text-blue-600 border-blue-600"
                          }`}>
                            <TrendingUp className="h-3 w-3 mr-1" />
                            {dept.change}
                          </Badge>
                        </div>
                        {renderStars(dept.rating)}
                      </div>
                    </div>
                    <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-primary rounded-full" 
                        style={{ width: `${(dept.rating / 5) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle>Top Performers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(competencyMatrix.ratings)
                  .map(([name, ratings]) => ({
                    name,
                    avgRating: ratings.reduce((a, b) => a + b, 0) / ratings.length
                  }))
                  .sort((a, b) => b.avgRating - a.avgRating)
                  .slice(0, 4)
                  .map((performer, index) => (
                    <div key={performer.name} className="flex items-start justify-between border-b pb-3 last:border-0 last:pb-0">
                      <div className="flex items-center">
                        <div className={`flex h-8 w-8 shrink-0 items-center justify-center rounded-full ${
                          index === 0 ? "bg-yellow-100" : index === 1 ? "bg-gray-100" : index === 2 ? "bg-amber-100" : "bg-blue-100"
                        }`}>
                          {index < 3 ? (
                            <Award className={`h-4 w-4 ${
                              index === 0 ? "text-yellow-600" : index === 1 ? "text-gray-600" : "text-amber-600"
                            }`} />
                          ) : (
                            <Target className="h-4 w-4 text-blue-600" />
                          )}
                        </div>
                        <div className="ml-4">
                          <p className="text-sm font-medium leading-none">{performer.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {index === 0 ? "Star Performer" : index === 1 ? "Key Talent" : index === 2 ? "Rising Star" : "High Potential"}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {renderStars(performer.avgRating)}
                      </div>
                    </div>
                  ))
                }
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="skills" className="space-y-4">
          {/* Competency Matrix */}
          <Card>
            <CardHeader>
              <CardTitle>Competency Matrix</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="border-b">
                    <tr className="text-muted-foreground">
                      <th className="h-10 px-4 text-left font-medium">Team Member</th>
                      {competencyMatrix.skills.map((skill) => (
                        <th key={skill} className="h-10 px-4 text-left font-medium">{skill}</th>
                      ))}
                      <th className="h-10 px-4 text-left font-medium">Average</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(competencyMatrix.ratings).map(([name, ratings]) => (
                      <tr key={name} className="border-b transition-colors hover:bg-muted/50">
                        <td className="p-4 font-medium">{name}</td>
                        {ratings.map((rating, index) => (
                          <td key={index} className="p-4">
                            <div className="flex items-center">
                              <div 
                                className={`h-2 w-2 rounded-full mr-2 ${
                                  rating >= 4.5 ? "bg-green-500" : 
                                  rating >= 4.0 ? "bg-blue-500" : 
                                  rating >= 3.5 ? "bg-amber-500" : 
                                  "bg-red-500"
                                }`}
                              ></div>
                              {rating.toFixed(1)}
                            </div>
                          </td>
                        ))}
                        <td className="p-4 font-medium">
                          {(ratings.reduce((a, b) => a + b, 0) / ratings.length).toFixed(1)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <div className="mt-6 space-y-4">
                <h4 className="text-sm font-medium">Skill Rating Legend</h4>
                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="flex items-center">
                    <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                    <span>4.5+ (Expert)</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 rounded-full bg-blue-500 mr-2"></div>
                    <span>4.0-4.4 (Proficient)</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 rounded-full bg-amber-500 mr-2"></div>
                    <span>3.5-3.9 (Competent)</span>
                  </div>
                  <div className="flex items-center">
                    <div className="h-2 w-2 rounded-full bg-red-500 mr-2"></div>
                    <span>&lt;3.5 (Developing)</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Skill Gap Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Skill Gap Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <p className="text-sm text-muted-foreground">
                  Analysis of team skill requirements versus current capabilities. Gaps are identified to prioritize training initiatives.
                </p>
                
                <div className="space-y-4">
                  {competencyMatrix.skills.map((skill, index) => {
                    // Calculate average rating for this skill across all team members
                    const ratings = Object.values(competencyMatrix.ratings).map(r => r[index]);
                    const avgRating = ratings.reduce((a, b) => a + b, 0) / ratings.length;
                    const targetRating = Math.min(5, avgRating + 0.3); // Higher target for improvement
                    
                    return (
                      <div key={skill} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">{skill}</span>
                          <span className="text-sm text-muted-foreground">
                            Current: {avgRating.toFixed(1)} | Target: {targetRating.toFixed(1)}
                          </span>
                        </div>
                        <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-primary rounded-full" 
                            style={{ width: `${(avgRating / 5) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                <div className="rounded-lg border p-4">
                  <h4 className="text-sm font-medium mb-2">Recommended Training Programs</h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <FileText className="h-4 w-4 mr-2 mt-0.5 text-primary" />
                      Advanced Immigration Law Course
                    </li>
                    <li className="flex items-start">
                      <FileText className="h-4 w-4 mr-2 mt-0.5 text-primary" />
                      Client Communication Excellence
                    </li>
                    <li className="flex items-start">
                      <FileText className="h-4 w-4 mr-2 mt-0.5 text-primary" />
                      Visa Documentation Masterclass
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="reviews" className="space-y-4">
          {/* Upcoming Reviews */}
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Performance Reviews</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingReviews.map((review) => (
                  <div key={review.id} className="flex justify-between items-start border-b pb-4 last:border-0 last:pb-0">
                    <div>
                      <h4 className="text-sm font-medium">{review.employeeName}</h4>
                      <p className="text-xs text-muted-foreground">{review.position} • {review.department}</p>
                      <p className="text-xs mt-1">Reviewer: {review.reviewer}</p>
                    </div>
                    <div className="flex flex-col items-end">
                      <Badge className="mb-1">{review.date}</Badge>
                      <Button variant="outline" size="sm">
                        Prepare
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              
              <Button variant="ghost" className="w-full mt-4">
                <ArrowUpRight className="mr-2 h-4 w-4" />
                View All Scheduled Reviews
              </Button>
            </CardContent>
          </Card>
          
          {/* Performance Review Stats */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Review Completion</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">94%</div>
                <p className="text-xs text-muted-foreground">
                  15 of 16 scheduled reviews completed last quarter
                </p>
                <div className="mt-4 h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-green-500 rounded-full" 
                    style={{ width: "94%" }}
                  ></div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Average Rating Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+0.3</div>
                <p className="text-xs text-muted-foreground">
                  Improvement in average ratings from last review cycle
                </p>
                <div className="mt-2 flex items-center space-x-2">
                  <Badge className="bg-green-500">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    Improving
                  </Badge>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Next Review Cycle</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">June 1</div>
                <p className="text-xs text-muted-foreground">
                  22 employees scheduled
                </p>
                <Button variant="outline" className="mt-4 w-full" size="sm">
                  <Calendar className="mr-2 h-4 w-4" />
                  View Schedule
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 