import { prisma } from '@/lib/prisma'
import { Task, TaskStatus, Priority } from '@prisma/client'

export interface CreateTaskData {
  userId: string
  title: string
  description?: string
  priority?: Priority
  dueDate?: Date
  assignedBy?: string
  category?: string
  applicationId?: string
}

export interface UpdateTaskData {
  title?: string
  description?: string
  status?: TaskStatus
  priority?: Priority
  dueDate?: Date
  category?: string
  applicationId?: string
}

export class TaskService {
  // Create new task
  static async createTask(data: CreateTaskData): Promise<Task> {
    try {
      return await prisma.task.create({
        data,
      })
    } catch (error) {
      console.error('Error creating task:', error)
      throw new Error('Failed to create task')
    }
  }

  // Get tasks for user
  static async getUserTasks(userId: string, includeCompleted = false): Promise<Task[]> {
    try {
      const whereClause: any = { userId }
      
      if (!includeCompleted) {
        whereClause.status = {
          in: ['PENDING', 'IN_PROGRESS']
        }
      }

      return await prisma.task.findMany({
        where: whereClause,
        orderBy: [
          { priority: 'desc' },
          { dueDate: 'asc' },
          { createdAt: 'desc' }
        ],
      })
    } catch (error) {
      console.error('Error fetching tasks:', error)
      return []
    }
  }

  // Get pending tasks for user
  static async getPendingTasks(userId: string, limit = 10): Promise<Task[]> {
    try {
      return await prisma.task.findMany({
        where: { 
          userId,
          status: {
            in: ['PENDING', 'IN_PROGRESS']
          }
        },
        orderBy: [
          { priority: 'desc' },
          { dueDate: 'asc' }
        ],
        take: limit,
      })
    } catch (error) {
      console.error('Error fetching pending tasks:', error)
      return []
    }
  }

  // Get task by ID
  static async getTaskById(id: string, userId: string): Promise<Task | null> {
    try {
      return await prisma.task.findFirst({
        where: { 
          id,
          userId
        },
      })
    } catch (error) {
      console.error('Error fetching task:', error)
      return null
    }
  }

  // Update task
  static async updateTask(id: string, userId: string, data: UpdateTaskData): Promise<Task | null> {
    try {
      const updateData: any = { ...data }
      
      // Set completion date if marking as completed
      if (data.status === 'COMPLETED') {
        updateData.completedAt = new Date()
      }

      return await prisma.task.update({
        where: { id },
        data: updateData,
      })
    } catch (error) {
      console.error('Error updating task:', error)
      throw new Error('Failed to update task')
    }
  }

  // Complete task
  static async completeTask(id: string, userId: string): Promise<Task | null> {
    try {
      return await prisma.task.update({
        where: { 
          id,
          userId
        },
        data: {
          status: 'COMPLETED',
          completedAt: new Date()
        }
      })
    } catch (error) {
      console.error('Error completing task:', error)
      return null
    }
  }

  // Delete task
  static async deleteTask(id: string, userId: string): Promise<boolean> {
    try {
      await prisma.task.delete({
        where: { 
          id,
          userId
        }
      })
      return true
    } catch (error) {
      console.error('Error deleting task:', error)
      return false
    }
  }

  // Get overdue tasks
  static async getOverdueTasks(userId: string): Promise<Task[]> {
    try {
      return await prisma.task.findMany({
        where: { 
          userId,
          status: {
            in: ['PENDING', 'IN_PROGRESS']
          },
          dueDate: {
            lt: new Date()
          }
        },
        orderBy: { dueDate: 'asc' },
      })
    } catch (error) {
      console.error('Error fetching overdue tasks:', error)
      return []
    }
  }

  // Get task statistics
  static async getTaskStats(userId: string) {
    try {
      const [
        total,
        pending,
        inProgress,
        completed,
        overdue
      ] = await Promise.all([
        prisma.task.count({
          where: { userId }
        }),
        prisma.task.count({
          where: { 
            userId,
            status: 'PENDING'
          }
        }),
        prisma.task.count({
          where: { 
            userId,
            status: 'IN_PROGRESS'
          }
        }),
        prisma.task.count({
          where: { 
            userId,
            status: 'COMPLETED'
          }
        }),
        prisma.task.count({
          where: { 
            userId,
            status: {
              in: ['PENDING', 'IN_PROGRESS']
            },
            dueDate: {
              lt: new Date()
            }
          }
        })
      ])

      return {
        total,
        pending,
        inProgress,
        completed,
        overdue
      }
    } catch (error) {
      console.error('Error fetching task stats:', error)
      return {
        total: 0,
        pending: 0,
        inProgress: 0,
        completed: 0,
        overdue: 0
      }
    }
  }
}
