"use client";

import { useState } from "react";
import { 
  Search, 
  Filter, 
  ArrowUpDown, 
  Download, 
  MoreHorizontal, 
  ChevronDown, 
  UserCheck, 
  FileText, 
  AlertTriangle, 
  ArrowUp, 
  ArrowDown,
  Eye,
  Phone,
  Mail,
  Calendar,
  MessageCircle,
  Star,
  Clock,
  SlidersHorizontal,
  PlusCircle
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock data for leads
const mockLeads = [
  {
    id: "LD-2023-5721",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+91 98765 43210",
    country: "India",
    visaType: "EB-1",
    appliedDate: "2023-05-01T10:30:00",
    stage: "Document Collection",
    leadScore: 87,
    conversionProbability: 76,
    lastContact: "2023-05-18T14:30:00",
    nextFollowUp: "2023-05-21T11:00:00",
    pendingDocuments: 2,
    sentiment: "Positive",
    tags: ["Professor", "IIT", "Publication"],
    assigned: true,
    priority: "High"
  },
  {
    id: "LD-2023-5695",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+91 87654 32109",
    country: "India",
    visaType: "Student Visa",
    appliedDate: "2023-05-03T09:15:00",
    stage: "Interview Preparation",
    leadScore: 92,
    conversionProbability: 89,
    lastContact: "2023-05-17T16:45:00",
    nextFollowUp: "2023-05-20T14:30:00",
    pendingDocuments: 1,
    sentiment: "Very Positive",
    tags: ["Stanford", "Scholarship", "Computer Science"],
    assigned: true,
    priority: "Medium"
  },
  {
    id: "LD-2023-5682",
    name: "Rajat Gupta",
    email: "<EMAIL>",
    phone: "+91 76543 21098",
    country: "India",
    visaType: "H1-B",
    appliedDate: "2023-05-05T14:20:00",
    stage: "Application Review",
    leadScore: 75,
    conversionProbability: 68,
    lastContact: "2023-05-16T10:15:00",
    nextFollowUp: "2023-05-19T11:30:00",
    pendingDocuments: 2,
    sentiment: "Neutral",
    tags: ["Google", "Software Engineer", "6 years"],
    assigned: true,
    priority: "Medium"
  },
  {
    id: "LD-2023-5673",
    name: "Meera Patel",
    email: "<EMAIL>",
    phone: "+91 65432 10987",
    country: "India",
    visaType: "O-1",
    appliedDate: "2023-05-08T11:45:00",
    stage: "Initial Consultation",
    leadScore: 68,
    conversionProbability: 54,
    lastContact: "2023-05-15T15:00:00",
    nextFollowUp: "2023-05-19T16:00:00",
    pendingDocuments: 3,
    sentiment: "Cautious",
    tags: ["Artist", "International Exhibition"],
    assigned: true,
    priority: "Low"
  },
  {
    id: "LD-2023-5667",
    name: "Vikram Malhotra",
    email: "<EMAIL>",
    phone: "+91 56789 12345",
    country: "India",
    visaType: "EB-2",
    appliedDate: "2023-05-10T13:30:00",
    stage: "Document Collection",
    leadScore: 79,
    conversionProbability: 72,
    lastContact: "2023-05-14T11:25:00",
    nextFollowUp: "2023-05-22T15:00:00",
    pendingDocuments: 4,
    sentiment: "Positive",
    tags: ["Research", "PhD", "NIW"],
    assigned: true,
    priority: "High"
  },
  {
    id: "LD-2023-5658",
    name: "Lisa Wang",
    email: "<EMAIL>",
    phone: "+86 123 4567 8901",
    country: "China",
    visaType: "F-1",
    appliedDate: "2023-05-12T09:20:00",
    stage: "Initial Consultation",
    leadScore: 85,
    conversionProbability: 78,
    lastContact: "2023-05-15T10:45:00",
    nextFollowUp: "2023-05-18T11:00:00",
    pendingDocuments: 5,
    sentiment: "Positive",
    tags: ["MBA", "Harvard", "Finance"],
    assigned: false,
    priority: "Medium"
  },
  {
    id: "LD-2023-5642",
    name: "Ahmed Khan",
    email: "<EMAIL>",
    phone: "+92 321 1234567",
    country: "Pakistan",
    visaType: "B1/B2",
    appliedDate: "2023-05-14T15:10:00",
    stage: "Initial Consultation",
    leadScore: 63,
    conversionProbability: 45,
    lastContact: "2023-05-17T14:30:00",
    nextFollowUp: "2023-05-21T10:00:00",
    pendingDocuments: 1,
    sentiment: "Neutral",
    tags: ["Business", "Conference", "Short-term"],
    assigned: false,
    priority: "Low"
  },
];

// Lead Score Component
function LeadScoreIndicator({ score }: { score: number }) {
  let color;
  let bgGradient;
  
  if (score >= 80) {
    color = "text-green-800";
    bgGradient = "from-green-400 to-emerald-300";
  } else if (score >= 60) {
    color = "text-amber-800";
    bgGradient = "from-amber-400 to-orange-300";
  } else {
    color = "text-red-800";
    bgGradient = "from-red-400 to-rose-300";
  }
  
  return (
    <div className="flex items-center">
      <div className={`w-12 h-12 bg-gradient-to-br ${bgGradient} rounded-full flex items-center justify-center font-bold text-lg text-white shadow-sm`}>
        {score}
      </div>
      <div className="ml-3">
        <div className="text-xs text-indigo-600 font-medium">Score</div>
        <div className="text-sm font-semibold text-indigo-800">{score}/100</div>
      </div>
    </div>
  );
}

// Conversion Probability Component
function ConversionProbability({ probability }: { probability: number }) {
  let color;
  let bgColor;
  
  if (probability >= 70) {
    color = "text-green-700";
    bgColor = "bg-green-100/70";
  } else if (probability >= 50) {
    color = "text-amber-700";
    bgColor = "bg-amber-100/70";
  } else {
    color = "text-red-700";
    bgColor = "bg-red-100/70";
  }
  
  return (
    <div className="flex items-center">
      <div className={`text-lg font-bold ${color} px-2 py-1 rounded-lg ${bgColor} backdrop-blur-sm`}>{probability}%</div>
      <div className="ml-2 text-xs text-indigo-600 font-medium">probability</div>
    </div>
  );
}

// Function to get sentiment badge color
function getSentimentBadge(sentiment: string) {
  switch (sentiment) {
    case "Very Positive":
      return "bg-green-100/70 text-green-800";
    case "Positive":
      return "bg-emerald-100/70 text-emerald-800";
    case "Neutral":
      return "bg-blue-100/70 text-blue-800";
    case "Cautious":
      return "bg-amber-100/70 text-amber-800";
    case "Negative":
      return "bg-red-100/70 text-red-800";
    default:
      return "bg-gray-100/70 text-gray-800";
  }
}

// Function to get priority badge color
function getPriorityBadge(priority: string) {
  switch (priority) {
    case "High":
      return "bg-red-100/70 text-red-800";
    case "Medium":
      return "bg-amber-100/70 text-amber-800";
    case "Low":
      return "bg-green-100/70 text-green-800";
    default:
      return "bg-gray-100/70 text-gray-800";
  }
}

// Function to format date
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString("en-US", {
    day: "numeric",
    month: "short",
    year: "numeric"
  });
}

// Function to format time
function formatTime(dateString: string) {
  return new Date(dateString).toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit"
  });
}

export default function LeadsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [visaType, setVisaType] = useState("All");
  const [stage, setStage] = useState("All");
  const [priority, setPriority] = useState("All");
  const [sortConfig, setSortConfig] = useState({
    key: "appliedDate",
    direction: "desc"
  });
  const [selectedLead, setSelectedLead] = useState<string | null>(null);
  
  // Filter leads based on search query, visa type, stage, and priority
  const filteredLeads = mockLeads.filter(lead => {
    const matchesSearch = 
      lead.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lead.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lead.id.toLowerCase().includes(searchQuery.toLowerCase());
      
    const matchesVisaType = visaType === "All" || lead.visaType === visaType;
    const matchesStage = stage === "All" || lead.stage === stage;
    const matchesPriority = priority === "All" || lead.priority === priority;
    
    return matchesSearch && matchesVisaType && matchesStage && matchesPriority;
  });
  
  // Sort leads
  const sortedLeads = [...filteredLeads].sort((a, b) => {
    if (a[sortConfig.key as keyof typeof a] < b[sortConfig.key as keyof typeof b]) {
      return sortConfig.direction === "asc" ? -1 : 1;
    }
    if (a[sortConfig.key as keyof typeof a] > b[sortConfig.key as keyof typeof b]) {
      return sortConfig.direction === "asc" ? 1 : -1;
    }
    return 0;
  });
  
  // Handle sort
  const handleSortClick = (column: string) => {
    if (sortConfig.key === column) {
      setSortConfig({
        key: column,
        direction: sortConfig.direction === "asc" ? "desc" : "asc"
      });
    } else {
      setSortConfig({
        key: column,
        direction: "asc"
      });
    }
  };
  
  // Get sort icon
  const getSortIcon = (column: string) => {
    if (sortConfig.key !== column) {
      return <ArrowUpDown className="h-4 w-4 text-indigo-400" />;
    }
    
    if (sortConfig.direction === "asc") {
      return <ArrowUp className="h-4 w-4 text-indigo-600" />;
    }
    
    return <ArrowDown className="h-4 w-4 text-indigo-600" />;
  };
  
  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 md:gap-0">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Lead Management
          </h1>
          <p className="text-gray-600 mt-1">Track and manage all your visa application leads</p>
        </div>
        
        <div className="flex gap-3 self-end">
          <button className="bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-indigo-50/70 transition-colors shadow-sm flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Export Leads
          </button>
          <button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
            <PlusCircle className="h-4 w-4 mr-2" />
            Add New Lead
          </button>
        </div>
      </div>
      
      {/* Filters */}
      <GlassCard className="p-4">
        <div className="flex flex-wrap gap-3">
          <div className="relative flex-1 min-w-0">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-indigo-400" size={18} />
            <input
              type="text"
              placeholder="Search leads by name, email, phone or ID..."
              className="pl-10 pr-4 py-2 border border-indigo-200/60 rounded-lg w-full bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="flex items-center gap-3">
            <div className="relative">
              <select
                className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={visaType}
                onChange={(e) => setVisaType(e.target.value)}
              >
                <option>All</option>
                <option>EB-1</option>
                <option>EB-2</option>
                <option>H1-B</option>
                <option>O-1</option>
                <option>Student Visa</option>
                <option>B1/B2</option>
                <option>F-1</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <ChevronDown className="h-4 w-4 text-indigo-500" />
              </div>
            </div>
            
            <div className="relative">
              <select
                className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={stage}
                onChange={(e) => setStage(e.target.value)}
              >
                <option>All</option>
                <option>Initial Consultation</option>
                <option>Document Collection</option>
                <option>Application Review</option>
                <option>Interview Preparation</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <ChevronDown className="h-4 w-4 text-indigo-500" />
              </div>
            </div>
            
            <div className="relative">
              <select
                className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
              >
                <option>All</option>
                <option>High</option>
                <option>Medium</option>
                <option>Low</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <ChevronDown className="h-4 w-4 text-indigo-500" />
              </div>
            </div>
            
            <button className="p-2 bg-indigo-50/70 backdrop-blur-sm text-indigo-600 rounded-lg hover:bg-indigo-100/70 transition-colors">
              <SlidersHorizontal className="h-5 w-5" />
            </button>
          </div>
        </div>
      </GlassCard>
      
      {/* Stats Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <GlassCard className="p-5 transform transition hover:translate-y-[-4px]">
          <div className="text-sm text-indigo-700 font-medium">Total Leads</div>
          <div className="text-2xl font-bold text-indigo-800 mt-1">{mockLeads.length}</div>
          <div className="text-xs text-indigo-600 mt-2">Last 30 days</div>
        </GlassCard>
        
        <GlassCard className="p-5 transform transition hover:translate-y-[-4px]">
          <div className="text-sm text-indigo-700 font-medium">High Priority</div>
          <div className="text-2xl font-bold text-indigo-800 mt-1">{mockLeads.filter(lead => lead.priority === "High").length}</div>
          <div className="flex items-center text-xs text-red-600 mt-2">
            <AlertTriangle className="h-3.5 w-3.5 mr-1" />
            Needs immediate attention
          </div>
        </GlassCard>
        
        <GlassCard className="p-5 transform transition hover:translate-y-[-4px]">
          <div className="text-sm text-indigo-700 font-medium">Pending Documents</div>
          <div className="text-2xl font-bold text-indigo-800 mt-1">{mockLeads.reduce((total, lead) => total + lead.pendingDocuments, 0)}</div>
          <div className="flex items-center text-xs text-amber-600 mt-2">
            <FileText className="h-3.5 w-3.5 mr-1" />
            Across all leads
          </div>
        </GlassCard>
        
        <GlassCard className="p-5 transform transition hover:translate-y-[-4px]">
          <div className="text-sm text-indigo-700 font-medium">Unassigned Leads</div>
          <div className="text-2xl font-bold text-indigo-800 mt-1">{mockLeads.filter(lead => !lead.assigned).length}</div>
          <div className="flex items-center text-xs text-indigo-600 mt-2">
            <UserCheck className="h-3.5 w-3.5 mr-1" />
            Need assignment
          </div>
        </GlassCard>
      </div>
      
      {/* Lead List */}
      <GlassCard>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-indigo-100/30">
            <thead>
              <tr className="bg-indigo-50/30">
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortClick("name")}
                >
                  <div className="flex items-center">
                    Lead
                    {getSortIcon("name")}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortClick("visaType")}
                >
                  <div className="flex items-center">
                    Visa Type
                    {getSortIcon("visaType")}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortClick("stage")}
                >
                  <div className="flex items-center">
                    Stage
                    {getSortIcon("stage")}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortClick("leadScore")}
                >
                  <div className="flex items-center">
                    Score
                    {getSortIcon("leadScore")}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortClick("priority")}
                >
                  <div className="flex items-center">
                    Priority
                    {getSortIcon("priority")}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortClick("nextFollowUp")}
                >
                  <div className="flex items-center">
                    Next Follow-up
                    {getSortIcon("nextFollowUp")}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-indigo-700 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white/50 backdrop-blur-sm divide-y divide-indigo-100/30">
              {sortedLeads.map((lead) => (
                <tr 
                  key={lead.id} 
                  className={`hover:bg-indigo-50/30 transition-all duration-200 ${
                    selectedLead === lead.id ? 'bg-indigo-50/70' : ''
                  }`}
                  onClick={() => setSelectedLead(selectedLead === lead.id ? null : lead.id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-400 to-blue-300 flex items-center justify-center text-white font-medium shadow-sm">
                        {lead.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-indigo-800">{lead.name}</div>
                        <div className="text-xs text-gray-500">{lead.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
                      {lead.visaType}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-indigo-800">{lead.stage}</div>
                    <div className="text-xs text-indigo-600">Applied: {formatDate(lead.appliedDate)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <div className="flex items-center">
                      <div className={`h-2 w-16 bg-indigo-100/40 rounded-full overflow-hidden mr-2`}>
                        <div 
                          className={`h-full rounded-full ${
                            lead.leadScore >= 80 ? 'bg-gradient-to-r from-green-500 to-emerald-400' : 
                            lead.leadScore >= 60 ? 'bg-gradient-to-r from-amber-500 to-orange-400' : 
                            'bg-gradient-to-r from-red-500 to-rose-400'
                          }`}
                          style={{ width: `${lead.leadScore}%` }}
                        ></div>
                      </div>
                      <span className="text-indigo-800 font-medium">{lead.leadScore}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-medium rounded-full backdrop-blur-sm ${getPriorityBadge(lead.priority)}`}>
                      {lead.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-indigo-600 mr-2" />
                      <div>
                        <div className="text-sm text-indigo-800">{formatDate(lead.nextFollowUp)}</div>
                        <div className="text-xs text-indigo-600">{formatTime(lead.nextFollowUp)}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                        <Phone className="h-4 w-4" />
                      </button>
                      <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                        <Mail className="h-4 w-4" />
                      </button>
                      <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                        <Calendar className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </GlassCard>
      
      {/* Lead Detail */}
      {selectedLead && (
        <GlassCard className="p-6 transform transition-all duration-300">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              {sortedLeads.filter(lead => lead.id === selectedLead).map((lead) => (
                <div key={lead.id}>
                  <div className="flex justify-between items-start mb-6">
                    <div>
                      <h2 className="text-xl font-bold text-indigo-800 flex items-center">
                        {lead.name}
                        <span className={`ml-3 text-xs px-2 py-0.5 rounded-full ${getSentimentBadge(lead.sentiment)}`}>
                          {lead.sentiment}
                        </span>
                      </h2>
                      <p className="text-sm text-gray-600 mt-1">{lead.email} • {lead.phone}</p>
                      <p className="text-sm text-gray-600">{lead.country}</p>
                    </div>
                    <div>
                      <p className="text-xs text-indigo-600 font-medium">ID: {lead.id}</p>
                      <p className="text-xs text-indigo-600 mt-1">Applied: {formatDate(lead.appliedDate)}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
                    <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4">
                      <h3 className="text-sm font-semibold text-indigo-800 mb-3">Application Details</h3>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-indigo-600 font-medium">Visa Type</span>
                          <span className="text-sm font-medium text-indigo-800">{lead.visaType}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-indigo-600 font-medium">Stage</span>
                          <span className="text-sm font-medium text-indigo-800">{lead.stage}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-indigo-600 font-medium">Priority</span>
                          <span className={`text-xs px-2.5 py-1 rounded-full font-medium ${getPriorityBadge(lead.priority)}`}>{lead.priority}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-indigo-600 font-medium">Pending Documents</span>
                          <span className="text-sm font-medium text-indigo-800">{lead.pendingDocuments}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4">
                      <h3 className="text-sm font-semibold text-indigo-800 mb-3">Metrics</h3>
                      
                      <div className="flex justify-between items-center mb-4">
                        <LeadScoreIndicator score={lead.leadScore} />
                        <ConversionProbability probability={lead.conversionProbability} />
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-indigo-600 font-medium">Last Contact</span>
                          <span className="text-sm font-medium text-indigo-800">{formatDate(lead.lastContact)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-indigo-600 font-medium">Next Follow-up</span>
                          <span className="text-sm font-medium text-indigo-800">{formatDate(lead.nextFollowUp)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <h3 className="text-sm font-semibold text-indigo-800 mb-2">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {lead.tags.map((tag, index) => (
                        <span 
                          key={index} 
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100/70 text-indigo-700"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap justify-end gap-3 mt-6">
                    <button className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-indigo-200/60 rounded-lg text-sm text-indigo-800 font-medium flex items-center hover:bg-indigo-50/70 transition-colors">
                      <FileText className="h-4 w-4 mr-1.5 text-indigo-600" />
                      View Documents
                    </button>
                    <button className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-indigo-200/60 rounded-lg text-sm text-indigo-800 font-medium flex items-center hover:bg-indigo-50/70 transition-colors">
                      <MessageCircle className="h-4 w-4 mr-1.5 text-indigo-600" />
                      Communication History
                    </button>
                    <button className="px-3 py-2 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
                      <Calendar className="h-4 w-4 mr-1.5" />
                      Schedule Follow-up
                    </button>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4 h-fit">
              <h3 className="text-sm font-semibold text-indigo-800 mb-4">Activity Timeline</h3>
              
              <div className="relative pl-6 space-y-4 before:absolute before:top-0 before:bottom-0 before:left-2 before:border-l-2 before:border-indigo-200/50">
                <div className="relative">
                  <div className="absolute -left-6 top-1 h-4 w-4 rounded-full bg-gradient-to-r from-blue-600 to-indigo-700"></div>
                  <p className="text-xs text-indigo-600 font-medium">May 18, 2023</p>
                  <p className="text-sm text-indigo-800">Document request sent via email</p>
                </div>
                
                <div className="relative">
                  <div className="absolute -left-6 top-1 h-4 w-4 rounded-full bg-gradient-to-r from-indigo-600 to-purple-700"></div>
                  <p className="text-xs text-indigo-600 font-medium">May 15, 2023</p>
                  <p className="text-sm text-indigo-800">Initial consultation completed</p>
                </div>
                
                <div className="relative">
                  <div className="absolute -left-6 top-1 h-4 w-4 rounded-full bg-gradient-to-r from-purple-600 to-pink-600"></div>
                  <p className="text-xs text-indigo-600 font-medium">May 10, 2023</p>
                  <p className="text-sm text-indigo-800">Application started</p>
                </div>
                
                <div className="relative">
                  <div className="absolute -left-6 top-1 h-4 w-4 rounded-full bg-gray-300"></div>
                  <p className="text-xs text-indigo-600 font-medium">May 1, 2023</p>
                  <p className="text-sm text-indigo-800">Lead created in system</p>
                </div>
              </div>
              
              <button className="w-full mt-4 bg-white/80 backdrop-blur-sm border border-indigo-200/60 py-2 px-4 rounded-lg text-indigo-700 text-xs font-medium hover:bg-indigo-50/70 transition-colors">
                View Full History
              </button>
            </div>
          </div>
        </GlassCard>
      )}
    </div>
  );
} 