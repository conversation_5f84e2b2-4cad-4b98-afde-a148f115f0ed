"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Search,
  Filter,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  Check,
  X,
  AlertCircle,
  Download,
  UserPlus,
  CheckCircle2,
  ChevronDown,
  Eye,
  Edit,
  Lock,
  ArrowUpDown
} from "lucide-react";
import GlassCard from "@/components/GlassCard";
import AddUserDialog from "@/components/admin/AddUserDialog";
import ViewUserDialog from "@/components/admin/ViewUserDialog";
import EditUserDialog from "@/components/admin/EditUserDialog";
import { toast } from "sonner";

// Type for users from API
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
  // Mock fields for UI purposes
  phone?: string;
  country?: string;
  status?: string;
  applications?: number;
  lastLogin?: string | null;
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [countryFilter, setCountryFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [sortField, setSortField] = useState("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isViewUserDialogOpen, setIsViewUserDialogOpen] = useState(false);
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false);
  const [selectedUserForAction, setSelectedUserForAction] = useState<User | null>(null);
  
  const itemsPerPage = 5;

  // Fetch users from API
  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/users');
      
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      
      const data = await response.json();
      
      // Transform API data to include UI fields
      const enhancedUsers = data.users.map((user: User) => ({
        ...user,
        // Mocking fields not available in the actual API for UI purposes
        status: Math.random() > 0.7 ? 'pending' : Math.random() > 0.3 ? 'active' : 'suspended',
        country: ['United States', 'Canada', 'United Kingdom', 'Australia', 'India'][Math.floor(Math.random() * 5)],
        phone: `+1 (${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
        applications: Math.floor(Math.random() * 5),
        lastLogin: Math.random() > 0.2 ? new Date().toISOString() : null
      }));
      
      setUsers(enhancedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);
  
  // Filter and sort users
  const filteredUsers = users.filter(user => {
    // Search filter
    const searchMatch = 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.phone?.includes(searchTerm) || false);
    
    // Status filter
    const statusMatch = statusFilter === "all" || user.status === statusFilter;
    
    // Country filter
    const countryMatch = countryFilter === "all" || user.country === countryFilter;
    
    return searchMatch && statusMatch && countryMatch;
  }).sort((a, b) => {
    if (sortField === "name") {
      return sortDirection === "asc" 
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } else if (sortField === "applications") {
      const aApps = a.applications || 0;
      const bApps = b.applications || 0;
      return sortDirection === "asc"
        ? aApps - bApps
        : bApps - aApps;
    } else if (sortField === "joinDate") {
      return sortDirection === "asc"
        ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
    return 0;
  });
  
  // Pagination
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);
  
  // Unique countries for filter
  const countries = Array.from(new Set(users.map(user => user.country || 'Unknown')));
  
  // Handle sort
  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };
  
  // Handle bulk selection
  const handleSelectAll = () => {
    if (selectedUsers.length === paginatedUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(paginatedUsers.map(user => user.id));
    }
  };
  
  const handleSelectUser = (userId: string) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };
  
  // Handle bulk actions
  const handleBulkAction = (action: string) => {
    if (selectedUsers.length === 0) return;
    
    if (action === "activate") {
      setUsers(users.map(user => 
        selectedUsers.includes(user.id) ? { ...user, status: "active" } : user
      ));
      toast.success(`${selectedUsers.length} users activated`);
    } else if (action === "suspend") {
      setUsers(users.map(user => 
        selectedUsers.includes(user.id) ? { ...user, status: "suspended" } : user
      ));
      toast.success(`${selectedUsers.length} users suspended`);
    }
    
    setSelectedUsers([]);
  };
  
  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Never";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    }).format(date);
  };

  // Handle user added
  const handleUserAdded = () => {
    fetchUsers();
  };

  // Handle view user
  const handleViewUser = (user: User) => {
    setSelectedUserForAction(user);
    setIsViewUserDialogOpen(true);
  };

  // Handle edit user
  const handleEditUser = (user: User) => {
    setSelectedUserForAction(user);
    setIsEditUserDialogOpen(true);
  };

  // Handle lock/unlock (toggle status)
  const handleToggleUserStatus = async (user: User) => {
    try {
      const newStatus = user.status === "active" ? "suspended" : "active";
      
      // Update UI optimistically
      const newUsers = users.map(u => 
        u.id === user.id ? { ...u, status: newStatus } : u
      );
      setUsers(newUsers);
      
      // For now, we'll simulate a successful API call since the actual database schema
      // might not have a status field yet
      toast.success(`User ${user.name} ${newStatus === "active" ? "activated" : "suspended"}`);
      
      // In a real implementation with proper database schema, the API call would look like:
      // const response = await fetch(`/api/admin/users/${user.id}`, {
      //   method: "PATCH",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify({ status: newStatus }),
      // });
      
      // if (!response.ok) {
      //   const data = await response.json();
      //   throw new Error(data.error || "Failed to update user status");
      // }
    } catch (error: any) {
      // Revert UI change on error
      toast.error("Failed to update user status");
      console.error("Error toggling user status:", error);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
          User Management
        </h1>
        <p className="text-sm text-gray-600">Manage user accounts, permissions, and activities</p>
      </div>
      
      {/* Actions Bar */}
      <GlassCard className="mb-6 transform transition hover:translate-y-[-2px]">
        <div className="p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            {/* Search */}
            <div className="relative w-full sm:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-indigo-600" />
              </div>
              <input
                type="text"
                placeholder="Search users..."
                className="pl-10 pr-3 py-2 w-full border border-indigo-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white/70 text-indigo-800"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="flex flex-wrap gap-2 w-full sm:w-auto">
              {/* Status Filter */}
              <div className="relative">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="appearance-none pl-3 pr-9 py-2 border border-indigo-300 rounded-lg text-sm bg-white/70 text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="all">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="pending">Pending</option>
                  <option value="suspended">Suspended</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-600" />
                </div>
              </div>
              
              {/* Country Filter */}
              <div className="relative">
                <select
                  value={countryFilter}
                  onChange={(e) => setCountryFilter(e.target.value)}
                  className="appearance-none pl-3 pr-9 py-2 border border-indigo-300 rounded-lg text-sm bg-white/70 text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="all">All Countries</option>
                  {countries.map(country => (
                    <option key={country} value={country}>{country}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-600" />
                </div>
              </div>
              
              {/* Add User Button */}
              <button 
                className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center"
                onClick={() => setIsAddUserDialogOpen(true)}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Add User
              </button>
              
              {/* Export Button */}
              <button className="border border-indigo-300 rounded-lg text-sm py-2 px-3 bg-white/70 text-indigo-800 font-medium flex items-center">
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
          </div>
          
          {/* Bulk Actions - show only when users selected */}
          {selectedUsers.length > 0 && (
            <div className="mt-4 bg-indigo-50/50 rounded-lg p-2 flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-xs font-medium text-indigo-800 mr-2">
                  {selectedUsers.length} users selected
                </span>
                <button 
                  onClick={() => setSelectedUsers([])}
                  className="text-xs text-indigo-600 hover:text-indigo-800"
                >
                  Clear
                </button>
              </div>
              <div className="flex gap-2">
                <button 
                  onClick={() => handleBulkAction("activate")}
                  className="bg-gradient-to-r from-green-600 to-green-700 text-white text-xs rounded-lg py-1 px-3 font-medium hover:opacity-90 transition flex items-center"
                >
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Activate
                </button>
                <button 
                  onClick={() => handleBulkAction("suspend")}
                  className="bg-gradient-to-r from-amber-600 to-amber-700 text-white text-xs rounded-lg py-1 px-3 font-medium hover:opacity-90 transition flex items-center"
                >
                  <Lock className="h-3 w-3 mr-1" />
                  Suspend
                </button>
              </div>
            </div>
          )}
        </div>
      </GlassCard>
      
      {/* Users Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
        <div className="overflow-x-auto">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-700"></div>
              <span className="ml-2 text-indigo-700">Loading users...</span>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500"
                        checked={selectedUsers.length === paginatedUsers.length && paginatedUsers.length > 0}
                        onChange={handleSelectAll}
                      />
                    </div>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left">
                    <button 
                      className="flex items-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort("name")}
                    >
                      User
                      {sortField === "name" && (
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      )}
                    </button>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact Info
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left">
                    <button 
                      className="flex items-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort("applications")}
                    >
                      Applications
                      {sortField === "applications" && (
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      )}
                    </button>
                  </th>
                  <th scope="col" className="px-6 py-3 text-left">
                    <button 
                      className="flex items-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                      onClick={() => handleSort("joinDate")}
                    >
                      Joined
                      {sortField === "joinDate" && (
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      )}
                    </button>
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedUsers.length > 0 ? (
                  paginatedUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500"
                          checked={selectedUsers.includes(user.id)}
                          onChange={() => handleSelectUser(user.id)}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center">
                            <span className="text-indigo-800 font-medium">
                              {user.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{user.name}</div>
                            <div className="text-sm text-gray-500">{user.country || 'Unknown'}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{user.email}</div>
                        <div className="text-sm text-gray-500">{user.phone || 'Not available'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                          ${user.status === 'active' ? 'bg-green-100 text-green-800' : 
                            user.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                            'bg-red-100 text-red-800'}`}>
                          {user.status ? `${user.status.charAt(0).toUpperCase()}${user.status.slice(1)}` : 'Unknown'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.applications !== undefined ? user.applications : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(user.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button 
                            className="text-indigo-600 hover:text-indigo-900"
                            onClick={() => handleViewUser(user)}
                            title="View user details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button 
                            className="text-indigo-600 hover:text-indigo-900"
                            onClick={() => handleEditUser(user)}
                            title="Edit user details"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button 
                            className={`${user.status === 'active' ? 'text-gray-600 hover:text-gray-900' : 'text-red-600 hover:text-red-900'}`}
                            onClick={() => handleToggleUserStatus(user)}
                            title={user.status === 'active' ? "Suspend user" : "Activate user"}
                          >
                            <Lock className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="px-6 py-10 text-center text-sm text-gray-500">
                      No users found. Try adjusting your filters or search terms.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>
        
        {/* Pagination */}
        {!isLoading && filteredUsers.length > 0 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${
                  currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </button>
              <button
                className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${
                  currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(startIndex + itemsPerPage, filteredUsers.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredUsers.length}</span> results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${
                      currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border ${
                        currentPage === page
                          ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                          : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
                      } text-sm font-medium`}
                    >
                      {page}
                    </button>
                  ))}
                  <button
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${
                      currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Add User Dialog */}
      <AddUserDialog 
        open={isAddUserDialogOpen} 
        onOpenChange={setIsAddUserDialogOpen}
        onUserAdded={handleUserAdded}
      />
      
      {/* View User Dialog */}
      <ViewUserDialog
        open={isViewUserDialogOpen}
        onOpenChange={setIsViewUserDialogOpen}
        user={selectedUserForAction}
      />
      
      {/* Edit User Dialog */}
      <EditUserDialog
        open={isEditUserDialogOpen}
        onOpenChange={setIsEditUserDialogOpen}
        user={selectedUserForAction}
        onUserUpdated={fetchUsers}
      />
    </div>
  );
} 