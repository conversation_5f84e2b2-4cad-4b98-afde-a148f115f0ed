"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Shield,
  FileText,
  Calendar,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Search,
  Filter,
  Download,
  Upload,
  Clock,
  Refresh<PERSON>w,
  PieChart,
  BarChart,
  ChevronRight,
  Bell
} from "lucide-react";

// Mock data for compliance dashboard
const complianceMetrics = {
  overallScore: 97,
  documentsUpToDate: 92,
  trainingCompletion: 94,
  regulatoryChanges: 4,
  upcomingDeadlines: 3,
  lastAuditScore: 95
};

// Mock regulatory requirements
const regulatoryItems = [
  {
    id: 1,
    regulation: "GDPR Data Protection",
    status: "Compliant",
    lastUpdated: "May 10, 2025",
    riskLevel: "Low",
    assignedTo: "Legal Team"
  },
  {
    id: 2,
    regulation: "Employment Visa Regulations",
    status: "Compliant",
    lastUpdated: "April 28, 2025",
    riskLevel: "Low",
    assignedTo: "Immigration Team"
  },
  {
    id: 3,
    regulation: "Anti-Money Laundering Procedures",
    status: "Action Required",
    lastUpdated: "March 15, 2025",
    riskLevel: "Medium",
    assignedTo: "Finance Department"
  },
  {
    id: 4,
    regulation: "Health & Safety Standards",
    status: "Pending Review",
    lastUpdated: "May 5, 2025",
    riskLevel: "Low",
    assignedTo: "Operations Team"
  },
  {
    id: 5,
    regulation: "Travel Insurance Requirements",
    status: "Compliant",
    lastUpdated: "May 1, 2025",
    riskLevel: "Low",
    assignedTo: "Insurance Team"
  }
];

// Mock audit history
const auditHistory = [
  {
    id: 1,
    auditName: "Annual Compliance Review",
    date: "Jan 15, 2025",
    score: 95,
    findings: 3,
    status: "Completed"
  },
  {
    id: 2,
    auditName: "Quarterly GDPR Assessment",
    date: "Apr 2, 2025",
    score: 97,
    findings: 1,
    status: "Completed"
  },
  {
    id: 3,
    auditName: "Visa Processing Compliance",
    date: "Feb 20, 2025",
    score: 94,
    findings: 4,
    status: "Completed"
  },
  {
    id: 4,
    auditName: "External Immigration Audit",
    date: "May 30, 2025",
    score: null,
    findings: null,
    status: "Scheduled"
  }
];

// Mock document management
const complianceDocuments = [
  {
    id: 1,
    name: "Data Protection Policy",
    category: "GDPR",
    status: "Current",
    expiryDate: "Dec 31, 2025",
    lastReviewed: "Jan 10, 2025"
  },
  {
    id: 2,
    name: "Visa Processing Guidelines",
    category: "Immigration",
    status: "Current",
    expiryDate: "Nov 15, 2025",
    lastReviewed: "Feb 5, 2025"
  },
  {
    id: 3,
    name: "Employee Privacy Notice",
    category: "GDPR",
    status: "Current",
    expiryDate: "Dec 31, 2025",
    lastReviewed: "Jan 8, 2025"
  },
  {
    id: 4,
    name: "Health & Safety Procedures",
    category: "Workspace",
    status: "Review Required",
    expiryDate: "June 30, 2025",
    lastReviewed: "June 15, 2024"
  },
  {
    id: 5,
    name: "Anti-Bribery Policy",
    category: "Ethics",
    status: "Current",
    expiryDate: "Mar 31, 2026",
    lastReviewed: "Mar 25, 2025"
  }
];

export default function CompliancePage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Compliant":
        return <Badge className="bg-green-500">Compliant</Badge>;
      case "Action Required":
        return <Badge className="bg-red-500">Action Required</Badge>;
      case "Pending Review":
        return <Badge className="bg-amber-500">Pending Review</Badge>;
      case "Current":
        return <Badge className="bg-green-500">Current</Badge>;
      case "Review Required":
        return <Badge className="bg-amber-500">Review Required</Badge>;
      case "Completed":
        return <Badge className="bg-blue-500">Completed</Badge>;
      case "Scheduled":
        return <Badge variant="outline">Scheduled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case "High":
        return <Badge className="bg-red-500">High</Badge>;
      case "Medium":
        return <Badge className="bg-amber-500">Medium</Badge>;
      case "Low":
        return <Badge className="bg-green-500">Low</Badge>;
      default:
        return <Badge variant="outline">{risk}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Regulatory Center</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button>
            <Bell className="mr-2 h-4 w-4" />
            Compliance Alerts
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="regulations">Regulations</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="audits">Audits</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key compliance metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overall Compliance</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{complianceMetrics.overallScore}%</div>
                <Progress value={complianceMetrics.overallScore} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  +2% from last quarter
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Documents Up-to-Date</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{complianceMetrics.documentsUpToDate}%</div>
                <Progress value={complianceMetrics.documentsUpToDate} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  2 documents need review
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Training Completion</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{complianceMetrics.trainingCompletion}%</div>
                <Progress value={complianceMetrics.trainingCompletion} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  15 employees need training
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Additional metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Upcoming Deadlines</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{complianceMetrics.upcomingDeadlines}</div>
                <p className="text-xs text-muted-foreground mt-2">
                  Next due in 7 days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Regulatory Changes</CardTitle>
                <RefreshCw className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{complianceMetrics.regulatoryChanges}</div>
                <p className="text-xs text-muted-foreground mt-2">
                  New changes this quarter
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Last Audit Score</CardTitle>
                <BarChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{complianceMetrics.lastAuditScore}%</div>
                <p className="text-xs text-muted-foreground mt-2">
                  Annual review Jan 15, 2025
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent activity and alerts */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Compliance Activity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-4">
                <div className="mt-0.5 rounded-full bg-green-100 p-1">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">GDPR quarterly assessment completed</p>
                  <p className="text-xs text-muted-foreground">97% compliance score achieved</p>
                  <p className="text-xs text-muted-foreground">2 days ago</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="mt-0.5 rounded-full bg-amber-100 p-1">
                  <AlertTriangle className="h-4 w-4 text-amber-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Anti-Money Laundering procedures need update</p>
                  <p className="text-xs text-muted-foreground">New requirements effective June 1</p>
                  <p className="text-xs text-muted-foreground">1 week ago</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="mt-0.5 rounded-full bg-blue-100 p-1">
                  <Clock className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">External audit scheduled</p>
                  <p className="text-xs text-muted-foreground">Immigration compliance review</p>
                  <p className="text-xs text-muted-foreground">May 30, 2025</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="regulations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Regulatory Requirements</CardTitle>
              <CardDescription>Manage and track compliance with key regulations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between space-y-2 mb-4">
                <div className="flex w-full max-w-sm items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input 
                    placeholder="Search regulations..." 
                    className="flex-1"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                </Button>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Regulation</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Risk Level</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead>Assigned To</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {regulatoryItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.regulation}</TableCell>
                      <TableCell>{getStatusBadge(item.status)}</TableCell>
                      <TableCell>{getRiskBadge(item.riskLevel)}</TableCell>
                      <TableCell>{item.lastUpdated}</TableCell>
                      <TableCell>{item.assignedTo}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Document Management</CardTitle>
              <CardDescription>Manage and track important compliance documents</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between space-y-2 mb-4">
                <div className="flex w-full max-w-sm items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input 
                    placeholder="Search documents..." 
                    className="flex-1"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Button>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Reviewed</TableHead>
                    <TableHead>Expiry Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {complianceDocuments.map((doc) => (
                    <TableRow key={doc.id}>
                      <TableCell className="font-medium">{doc.name}</TableCell>
                      <TableCell>{doc.category}</TableCell>
                      <TableCell>{getStatusBadge(doc.status)}</TableCell>
                      <TableCell>{doc.lastReviewed}</TableCell>
                      <TableCell>{doc.expiryDate}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audits" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Audit History & Tracking</CardTitle>
              <CardDescription>Track internal and external compliance audits</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between mb-4">
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Audits</SelectItem>
                    <SelectItem value="internal">Internal</SelectItem>
                    <SelectItem value="external">External</SelectItem>
                  </SelectContent>
                </Select>
                <Button>
                  <PieChart className="mr-2 h-4 w-4" />
                  Schedule Audit
                </Button>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Audit Name</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Score</TableHead>
                    <TableHead>Findings</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditHistory.map((audit) => (
                    <TableRow key={audit.id}>
                      <TableCell className="font-medium">{audit.auditName}</TableCell>
                      <TableCell>{audit.date}</TableCell>
                      <TableCell>{getStatusBadge(audit.status)}</TableCell>
                      <TableCell>{audit.score ? `${audit.score}%` : "-"}</TableCell>
                      <TableCell>{audit.findings !== null ? audit.findings : "-"}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 