"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Users,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  ChevronRight,
  Calendar,
  RefreshCw,
  Filter,
} from "lucide-react";
import GlassCard from "@/components/GlassCard";
import RoleBasedAccess from "@/components/auth/RoleBasedAccess";
import RoleBasedAccess from "@/components/auth/RoleBasedAccess";

// Example data
const overviewStats = [
  { 
    title: "Total Users", 
    value: 12847, 
    change: +14.2,
    icon: <Users className="h-5 w-5 text-indigo-600" />,
    href: "/admin/users" 
  },
  { 
    title: "Pending Applications", 
    value: 342, 
    change: -5.8,
    icon: <FileText className="h-5 w-5 text-amber-500" />,
    href: "/admin/applications?status=pending" 
  },
  { 
    title: "Documents for Review", 
    value: 156, 
    change: +24.3,
    icon: <AlertCircle className="h-5 w-5 text-orange-500" />,
    href: "/admin/documents?status=pending" 
  },
  { 
    title: "Approved Visas", 
    value: 9523, 
    change: +11.9,
    icon: <CheckCircle className="h-5 w-5 text-green-500" />,
    href: "/admin/applications?status=approved" 
  },
];

const recentActivities = [
  { 
    id: 1, 
    action: "Document Uploaded", 
    user: "John Smith", 
    time: "2 minutes ago",
    status: "needs-review" 
  },
  { 
    id: 2, 
    action: "Visa Application Submitted", 
    user: "Maria Johnson", 
    time: "10 minutes ago",
    status: "pending" 
  },
  { 
    id: 3, 
    action: "Interview Scheduled", 
    user: "Robert Chen", 
    time: "35 minutes ago",
    status: "scheduled" 
  },
  { 
    id: 4, 
    action: "Visa Approved", 
    user: "Sophia Rodriguez", 
    time: "1 hour ago",
    status: "approved" 
  },
  { 
    id: 5, 
    action: "Document Rejected", 
    user: "Daniel Wilson", 
    time: "2 hours ago",
    status: "rejected" 
  },
];

const upcomingAppointments = [
  {
    id: 1,
    client: "Liam Johnson",
    type: "Student Visa",
    date: "Today, 2:30 PM",
    counselor: "Dr. Emma Thompson"
  },
  {
    id: 2,
    client: "Olivia Garcia",
    type: "Work Visa",
    date: "Today, 4:15 PM",
    counselor: "Michael Chen"
  },
  {
    id: 3,
    client: "Noah Williams",
    type: "Tourist Visa",
    date: "Tomorrow, 10:00 AM",
    counselor: "Dr. Emma Thompson"
  },
  {
    id: 4,
    client: "Emma Brown",
    type: "Family Visa",
    date: "Tomorrow, 3:45 PM",
    counselor: "Sarah Johnson"
  }
];

function StatCard({ title, value, change, icon, href }: { 
  title: string; 
  value: number; 
  change: number;
  icon: React.ReactNode;
  href: string;
}) {
  return (
    <Link href={href as any} className="block">
      <GlassCard className="transform transition hover:translate-y-[-4px]">
        <div className="p-6">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-xs font-medium text-gray-600">{title}</p>
              <h3 className="text-2xl font-bold mt-1 text-indigo-800">{value.toLocaleString()}</h3>
              <div className={`flex items-center mt-2 text-xs font-medium ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {change >= 0 ? (
                  <TrendingUp className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-1" />
                )}
                <span>{Math.abs(change)}% from last month</span>
              </div>
            </div>
            <div className="bg-indigo-50/50 p-3 rounded-full">{icon}</div>
          </div>
        </div>
      </GlassCard>
    </Link>
  );
}

function ActivityCard() {
  return (
    <GlassCard className="transform transition hover:translate-y-[-4px] overflow-hidden">
      <div className="px-6 py-4 border-b border-indigo-100/30 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-indigo-800">Recent Activities</h3>
        <div className="flex items-center gap-2">
          <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
            <RefreshCw className="h-4 w-4" />
          </button>
          <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
            <Filter className="h-4 w-4" />
          </button>
        </div>
      </div>
      <div className="divide-y divide-indigo-100/20">
        {recentActivities.map((activity) => (
          <div key={activity.id} className="px-6 py-4 flex items-center justify-between hover:bg-indigo-50/30 transition-colors">
            <div className="flex items-center">
              <div className="mr-4">
                {activity.status === "needs-review" && (
                  <div className="bg-amber-100/70 backdrop-blur-sm p-2 rounded-full">
                    <AlertCircle className="h-5 w-5 text-amber-500" />
                  </div>
                )}
                {activity.status === "pending" && (
                  <div className="bg-blue-100/70 backdrop-blur-sm p-2 rounded-full">
                    <Clock className="h-5 w-5 text-blue-500" />
                  </div>
                )}
                {activity.status === "scheduled" && (
                  <div className="bg-indigo-100/70 backdrop-blur-sm p-2 rounded-full">
                    <Calendar className="h-5 w-5 text-indigo-500" />
                  </div>
                )}
                {activity.status === "approved" && (
                  <div className="bg-green-100/70 backdrop-blur-sm p-2 rounded-full">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  </div>
                )}
                {activity.status === "rejected" && (
                  <div className="bg-red-100/70 backdrop-blur-sm p-2 rounded-full">
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  </div>
                )}
              </div>
              <div>
                <p className="font-medium text-indigo-800">{activity.action}</p>
                <p className="text-sm text-gray-600">by {activity.user}</p>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-xs text-gray-600 mr-4">{activity.time}</span>
              <ChevronRight className="h-4 w-4 text-indigo-400" />
            </div>
          </div>
        ))}
      </div>
      <div className="px-6 py-3 bg-indigo-50/50 border-t border-indigo-100/30">
        <Link href={"/admin/activities" as any} className="text-sm text-indigo-600 font-medium hover:text-indigo-800 flex items-center justify-center">
          View All Activities
          <ChevronRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </GlassCard>
  );
}

function AppointmentsCard() {
  return (
    <GlassCard className="transform transition hover:translate-y-[-4px] overflow-hidden">
      <div className="px-6 py-4 border-b border-indigo-100/30">
        <h3 className="text-lg font-semibold text-indigo-800">Upcoming Appointments</h3>
      </div>
      <div className="divide-y divide-indigo-100/20">
        {upcomingAppointments.map((appointment) => (
          <div key={appointment.id} className="px-6 py-4 hover:bg-indigo-50/30 transition-colors">
            <div className="flex justify-between items-start mb-1">
              <h4 className="font-medium text-indigo-800">{appointment.client}</h4>
              <span className="text-xs font-medium px-2 py-1 rounded-full bg-indigo-100/70 backdrop-blur-sm text-indigo-700">
                {appointment.type}
              </span>
            </div>
            <div className="flex items-center text-sm text-gray-600 mb-1">
              <Calendar className="h-4 w-4 mr-1 text-indigo-600" />
              <span>{appointment.date}</span>
            </div>
            <div className="text-sm text-gray-600">
              Counselor: {appointment.counselor}
            </div>
          </div>
        ))}
      </div>
      <div className="px-6 py-3 bg-indigo-50/50 border-t border-indigo-100/30">
        <Link href={"/admin/appointments" as any} className="text-sm text-indigo-600 font-medium hover:text-indigo-800 flex items-center justify-center">
          View Full Schedule
          <ChevronRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </GlassCard>
  );
}

export default function AdminDashboard() {
  return (
    <RoleBasedAccess allowedRoles={['admin']}>
      <div>
      <div className="mb-6">
        <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
          Admin Dashboard
        </h1>
        <p className="text-gray-600 mt-1">Overview of visa applications and user activities</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {overviewStats.map((stat) => (
          <StatCard 
            key={stat.title}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            icon={stat.icon}
            href={stat.href}
          />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <ActivityCard />
        </div>
        <div className="lg:col-span-1">
          <AppointmentsCard />
        </div>
      </div>
      </div>
    </RoleBasedAccess>
  );
}