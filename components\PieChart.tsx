import React from 'react';

interface PieChartProps {
  data: { label: string; value: number }[];
}

export default function PieChart({ data }: PieChartProps) {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'];

  return (
    <div className="relative w-64 h-64">
      <svg viewBox="0 0 100 100" className="w-full h-full">
        {data.map((item, index) => {
          const percentage = (item.value / total) * 100;
          const startAngle = index === 0 ? 0 : data.slice(0, index).reduce((sum, d) => sum + (d.value / total) * 360, 0);
          const endAngle = startAngle + (item.value / total) * 360;
          const x1 = 50 + 40 * Math.cos((startAngle * Math.PI) / 180);
          const y1 = 50 + 40 * Math.sin((startAngle * Math.PI) / 180);
          const x2 = 50 + 40 * Math.cos((endAngle * Math.PI) / 180);
          const y2 = 50 + 40 * Math.sin((endAngle * Math.PI) / 180);
          const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;

          return (
            <path
              key={item.label}
              d={`M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`}
              fill={colors[index % colors.length]}
              stroke="white"
              strokeWidth="1"
            />
          );
        })}
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          {data.map((item, index) => (
            <div key={item.label} className="flex items-center">
              <div className="w-4 h-4 mr-2" style={{ backgroundColor: colors[index % colors.length] }}></div>
              <span>{item.label}: {item.value}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 