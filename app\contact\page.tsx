"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  MapPin, 
  Phone, 
  Mail, 
  MessageSquare, 
  Download,
  CheckCircle,
  Send,
  Globe,
  Clock
} from "lucide-react"
import Link from "next/link"
import { ReactNode } from "react"

interface GlassCardProps {
  children: ReactNode;
  className?: string;
}

function GlassCard({ children, className = "" }: GlassCardProps) {
  return (
    <div
      className={`rounded-xl p-4 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl ${className}`}
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {children}
    </div>
  )
}

export default function ContactPage() {
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    mobile: "",
    subject: "",
    message: "",
    service: "General Inquiry",
    priority: "medium"
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormData({
      ...formData,
      [id]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Call our API endpoint
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Show success message
        setFormSubmitted(true);
        
        // Reset form after 5 seconds
        setTimeout(() => {
          setFormSubmitted(false);
          setFormData({
            name: "",
            email: "",
            mobile: "",
            subject: "",
            message: "",
            service: "General Inquiry",
            priority: "medium"
          });
        }, 5000);
      } else {
        // Handle error
        console.error('Error creating lead:', result.message);
        alert('There was a problem submitting your inquiry. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('There was a problem submitting your inquiry. Please try again.');
    }
  };

  return (
    <div className="py-12 md:py-16 bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Let's Brew Success Together!
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Have questions or need assistance? We're here to help you navigate your global education and immigration
            journey.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Contact Information */}
          <GlassCard className="transform transition hover:translate-y-[-4px]">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-indigo-800 mb-6">Get in Touch</h2>

                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                  <div className="bg-indigo-50/50 p-3 rounded-full">
                    <MapPin className="h-5 w-5 text-indigo-600" />
                    </div>
                    <div>
                    <h3 className="text-sm font-semibold text-indigo-800 mb-1">Visit Our Office</h3>
                    <p className="text-sm text-gray-600">
                        Visa Mentor, Vinstitution, 2nd Floor, Property No. 44, Regal Building, Connaught Place, New
                        Delhi – 110001
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                  <div className="bg-indigo-50/50 p-3 rounded-full">
                    <Phone className="h-5 w-5 text-indigo-600" />
                    </div>
                    <div>
                    <h3 className="text-sm font-semibold text-indigo-800 mb-1">Call Us</h3>
                    <p className="text-sm text-gray-600">
                      <a href="tel:+919219542090" className="hover:text-indigo-600 transition">
                          +91 92195 42090
                        </a>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                  <div className="bg-indigo-50/50 p-3 rounded-full">
                    <Mail className="h-5 w-5 text-indigo-600" />
                    </div>
                    <div>
                    <h3 className="text-sm font-semibold text-indigo-800 mb-1">Email Us</h3>
                    <p className="text-sm text-gray-600">
                      <a href="mailto:<EMAIL>" className="hover:text-indigo-600 transition">
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                  <div className="bg-indigo-50/50 p-3 rounded-full">
                    <MessageSquare className="h-5 w-5 text-indigo-600" />
                    </div>
                    <div>
                    <h3 className="text-sm font-semibold text-indigo-800 mb-1">WhatsApp</h3>
                    <p className="text-sm text-gray-600">
                      <a href="https://wa.me/919219542090" className="hover:text-indigo-600 transition">
                          Chat with us on WhatsApp
                        </a>
                      </p>
                    </div>
                  </div>
                </div>

              <div className="mt-8 bg-indigo-50/50 rounded-lg p-4">
                <h3 className="text-sm font-semibold text-indigo-800 mb-2">Free Live Video Consultation</h3>
                <p className="text-xs text-indigo-600 mb-4">
                    Book your slot online for a face-to-face Zoom or Google Meet session. Get instant confirmation and
                    expert advice.
                  </p>
                <Button 
                  className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center"
                >
                  <Clock className="h-4 w-4 mr-2" />
                  Book Consultation
                </Button>
                </div>

                <div className="mt-8">
                <h3 className="text-sm font-semibold text-indigo-800 mb-2">Stay Connected</h3>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="icon" 
                    className="border border-indigo-300 rounded-lg bg-white text-indigo-600 hover:bg-indigo-50 transition" 
                    asChild
                  >
                      <Link href={"/download-app" as any}>
                        <Download className="h-4 w-4" />
                        <span className="sr-only">Download App</span>
                      </Link>
                    </Button>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    className="border border-indigo-300 rounded-lg bg-white text-indigo-600 hover:bg-indigo-50 transition" 
                    asChild
                  >
                      <Link href="https://facebook.com">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          fill="currentColor"
                          viewBox="0 0 16 16"
                        >
                          <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z" />
                        </svg>
                        <span className="sr-only">Facebook</span>
                      </Link>
                    </Button>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    className="border border-indigo-300 rounded-lg bg-white text-indigo-600 hover:bg-indigo-50 transition" 
                    asChild
                  >
                      <Link href="https://twitter.com">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          fill="currentColor"
                          viewBox="0 0 16 16"
                        >
                          <path d="M12.6.75h2.454l-5.36 6.142L16 15.25h-4.937l-3.867-5.07-4.425 5.07H.316l5.733-6.57L0 .75h5.063l3.495 4.633L12.601.75Zm-.86 13.028h1.36L4.323 2.145H2.865l8.875 11.633Z" />
                        </svg>
                        <span className="sr-only">Twitter</span>
                      </Link>
                    </Button>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    className="border border-indigo-300 rounded-lg bg-white text-indigo-600 hover:bg-indigo-50 transition" 
                    asChild
                  >
                      <Link href="https://instagram.com">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          fill="currentColor"
                          viewBox="0 0 16 16"
                        >
                          <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z" />
                        </svg>
                        <span className="sr-only">Instagram</span>
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
          </GlassCard>

          {/* Contact Form */}
          <GlassCard className="transform transition hover:translate-y-[-4px]">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-indigo-800 mb-6">Get Expert Assistance</h2>
                
                {formSubmitted ? (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                    <div className="flex justify-center mb-4">
                      <div className="bg-green-100 p-3 rounded-full">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                  </div>
                  <h3 className="text-sm font-semibold text-green-700 mb-2">Thank You!</h3>
                  <p className="text-xs text-green-600">
                      Your message has been received. One of our visa experts will contact you shortly.
                    </p>
                  </div>
                ) : (
                  <form className="space-y-4" onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                    <label htmlFor="name" className="block text-xs font-medium text-indigo-800 mb-1">
                          Name*
                        </label>
                        <Input 
                          id="name" 
                          placeholder="Your name" 
                          required 
                          value={formData.name}
                          onChange={handleInputChange}
                      className="border border-indigo-300 rounded-lg bg-white text-sm"
                        />
                      </div>
                      <div>
                    <label htmlFor="email" className="block text-xs font-medium text-indigo-800 mb-1">
                          Email*
                      </label>
                        <Input 
                          id="email" 
                          type="email" 
                          placeholder="Your email" 
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                      className="border border-indigo-300 rounded-lg bg-white text-sm"
                        />
                      </div>
                    </div>

                    <div>
                  <label htmlFor="mobile" className="block text-xs font-medium text-indigo-800 mb-1">
                        Mobile*
                      </label>
                      <Input 
                        id="mobile" 
                        placeholder="Your mobile number" 
                        required
                        value={formData.mobile}
                        onChange={handleInputChange}
                    className="border border-indigo-300 rounded-lg bg-white text-sm"
                      />
                  </div>

                <div className="bg-indigo-50/50 rounded-lg p-4">
                  <label htmlFor="service" className="block text-xs font-medium text-indigo-800 mb-1">
                        Service Interested In*
                    </label>
                      <select
                        id="service"
                    className="w-full px-3 py-2 rounded-lg border border-indigo-300 focus:outline-none focus:ring-2 focus:ring-indigo-400 bg-white text-sm text-indigo-800"
                        value={formData.service}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="General Inquiry">General Inquiry</option>
                        <option value="EB-1">EB-1 Visa</option>
                        <option value="O-1">O-1 Visa</option>
                        <option value="H-1B">H-1B Visa</option>
                        <option value="Student Visa">Student Visa</option>
                        <option value="EB-5">EB-5 Visa</option>
                        <option value="J-1">J-1 Visa</option>
                      </select>
                  </div>

                  <div>
                  <label htmlFor="subject" className="block text-xs font-medium text-indigo-800 mb-1">
                        Subject*
                    </label>
                      <Input 
                        id="subject" 
                        placeholder="Message subject" 
                        required
                        value={formData.subject}
                        onChange={handleInputChange}
                    className="border border-indigo-300 rounded-lg bg-white text-sm"
                      />
                  </div>

                  <div>
                  <label htmlFor="message" className="block text-xs font-medium text-indigo-800 mb-1">
                        Message*
                    </label>
                      <Textarea 
                        id="message" 
                        placeholder="Please provide details about your inquiry" 
                    rows={4} 
                        required
                        value={formData.message}
                        onChange={handleInputChange}
                    className="border border-indigo-300 rounded-lg bg-white text-sm"
                      />
                    </div>
                    
                    <div>
                  <label htmlFor="priority" className="block text-xs font-medium text-indigo-800 mb-1">
                        How soon do you need assistance?
                      </label>
                      <select
                        id="priority"
                    className="w-full px-3 py-2 rounded-lg border border-indigo-300 focus:outline-none focus:ring-2 focus:ring-indigo-400 bg-white text-sm text-indigo-800"
                        value={formData.priority}
                        onChange={handleInputChange}
                      >
                        <option value="low">Within the next few months</option>
                        <option value="medium">Within the next few weeks</option>
                        <option value="high">Urgently (within days)</option>
                      </select>
                    </div>

                    <div className="flex items-start space-x-2 mt-4">
                      <Checkbox id="consent" required />
                  <label htmlFor="consent" className="text-xs text-gray-600">
                        I agree to receive communications about visa services. I understand my data will be processed in accordance with the Privacy Policy.
                      </label>
                  </div>

                <Button 
                  type="submit" 
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-3 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center justify-center"
                >
                  <Send className="h-4 w-4 mr-2" />
                      Submit Inquiry
                  </Button>
                </form>
                )}
              </CardContent>
          </GlassCard>
        </div>

        {/* Map Section */}
        <div className="mb-6">
          <GlassCard className="transform transition hover:translate-y-[-4px]">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-indigo-800 mb-4 text-center flex items-center justify-center">
                <Globe className="h-5 w-5 text-indigo-600 mr-2" />
                Find Us
              </h2>
              <div className="h-[300px] bg-indigo-50/50 rounded-lg overflow-hidden">
            {/* Replace with actual map component */}
                <div className="w-full h-full flex items-center justify-center">
                  <p className="text-sm text-indigo-600">Map will be displayed here</p>
            </div>
          </div>
            </CardContent>
          </GlassCard>
        </div>
      </div>
    </div>
  )
}
