"use client";

import React, { useState, useEffect } from "react";
import {
  Calendar,
  ChevronDown,
  Download,
  TrendingUp,
  TrendingDown,
  Refresh<PERSON><PERSON>,
  Clock,
  Filter,
  Map,
  BarChart2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Globe,
} from "lucide-react";
import Glass<PERSON>ard from "@/components/GlassCard";

// Note: In a real application, you would use a proper charting library like Chart.js
// For this example, we're creating placeholder components for charts

const BarChartPlaceholder = ({ data }: { data: any }) => (
  <GlassCard className="h-full transform transition hover:translate-y-[-4px]" opacity="high">
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-indigo-800">{data.title}</h3>
        <div className="flex items-center gap-2">
          <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
            <Download className="h-4 w-4" />
          </button>
          <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
            <RefreshCw className="h-4 w-4" />
          </button>
        </div>
      </div>
      <div className="h-64 flex items-end justify-between gap-2 pb-6 pt-2 px-2">
        {data.data.map((item: any, index: number) => (
          <div key={index} className="flex flex-col items-center">
            <div 
              className="w-12 bg-gradient-to-t from-indigo-500 to-blue-400 rounded-t-md" 
              style={{ height: `${(item.value / data.maxValue) * 100}%` }}
            ></div>
            <div className="mt-2 text-xs text-gray-600">{item.label}</div>
          </div>
        ))}
      </div>
      <div className="text-xs text-gray-500 text-center mt-2">{data.description}</div>
    </div>
  </GlassCard>
);

const LineChartPlaceholder = ({ data }: { data: any }) => (
  <GlassCard className="h-full transform transition hover:translate-y-[-4px]" opacity="high">
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-indigo-800">{data.title}</h3>
        <div className="flex items-center gap-2">
          <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
            <Download className="h-4 w-4" />
          </button>
          <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
            <RefreshCw className="h-4 w-4" />
          </button>
        </div>
      </div>
      <div className="h-64 flex items-center justify-center">
        <div className="w-full h-full relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-indigo-100/50"></div>
          </div>
          <div className="absolute inset-0 flex items-center" style={{ top: "25%" }}>
            <div className="w-full border-t border-indigo-100/30"></div>
          </div>
          <div className="absolute inset-0 flex items-center" style={{ top: "50%" }}>
            <div className="w-full border-t border-indigo-100/30"></div>
          </div>
          <div className="absolute inset-0 flex items-center" style={{ top: "75%" }}>
            <div className="w-full border-t border-indigo-100/30"></div>
          </div>
          
          <svg className="absolute inset-0 w-full h-full">
            <path 
              d={`M0,${64 - data.data[0].value * 60} ${data.data.map((d: any, i: number) => `L${(i * 100) / (data.data.length - 1)},${64 - d.value * 60}`).join(' ')}`} 
              fill="none" 
              stroke="url(#blue-gradient)" 
              strokeWidth="3"
            />
            <defs>
              <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#4F46E5" />
                <stop offset="100%" stopColor="#60A5FA" />
              </linearGradient>
            </defs>
          </svg>
          
          {data.data.map((item: any, index: number) => (
            <div 
              key={index}
              className="absolute w-2 h-2 bg-indigo-600 rounded-full"
              style={{ 
                left: `${(index * 100) / (data.data.length - 1)}%`, 
                top: `${64 - item.value * 60}px`,
                transform: "translate(-50%, -50%)"
              }}
            ></div>
          ))}
        </div>
      </div>
      <div className="flex justify-between mt-2">
        {data.data.map((item: any, index: number) => (
          <div key={index} className="text-xs text-gray-600">
            {item.label}
          </div>
        ))}
      </div>
      <div className="text-xs text-gray-500 text-center mt-3">{data.description}</div>
    </div>
  </GlassCard>
);

const PieChartPlaceholder = ({ data }: { data: any }) => {
  const total = data.data.reduce((sum: number, item: any) => sum + item.value, 0);
  let cumulativePercentage = 0;
  
  return (
    <GlassCard className="h-full transform transition hover:translate-y-[-4px]" opacity="high">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-indigo-800">{data.title}</h3>
          <div className="flex items-center gap-2">
            <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
              <Download className="h-4 w-4" />
            </button>
            <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
        <div className="flex justify-center">
          <div className="relative w-48 h-48">
            <svg className="w-full h-full" viewBox="0 0 100 100">
              {data.data.map((item: any, index: number) => {
                const percentage = (item.value / total) * 100;
                const startAngle = cumulativePercentage * 3.6; // 3.6 degrees per percentage point
                cumulativePercentage += percentage;
                const endAngle = cumulativePercentage * 3.6;
                
                // Convert angle to radians and calculate coordinates
                const startAngleRad = (startAngle - 90) * (Math.PI / 180);
                const endAngleRad = (endAngle - 90) * (Math.PI / 180);
                
                const startX = 50 + 40 * Math.cos(startAngleRad);
                const startY = 50 + 40 * Math.sin(startAngleRad);
                const endX = 50 + 40 * Math.cos(endAngleRad);
                const endY = 50 + 40 * Math.sin(endAngleRad);
                
                const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;
                
                return (
                  <path
                    key={index}
                    d={`M 50 50 L ${startX} ${startY} A 40 40 0 ${largeArcFlag} 1 ${endX} ${endY} Z`}
                    fill={item.color}
                  />
                );
              })}
              <circle cx="50" cy="50" r="30" fill="white" />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center text-indigo-800 font-semibold">
              {total}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-2 mt-4">
          {data.data.map((item: any, index: number) => (
            <div key={index} className="flex items-center">
              <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: item.color }}></div>
              <div className="text-xs">
                <span className="font-medium">{item.label}</span>
                <span className="text-gray-500 ml-1">({item.value})</span>
              </div>
            </div>
          ))}
        </div>
        <div className="text-xs text-gray-500 text-center mt-3">{data.description}</div>
      </div>
    </GlassCard>
  );
};

const MapChartPlaceholder = ({ data }: { data: any }) => (
  <GlassCard className="h-full transform transition hover:translate-y-[-4px]" opacity="high">
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-indigo-800">{data.title}</h3>
        <div className="flex items-center gap-2">
          <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
            <Download className="h-4 w-4" />
          </button>
          <button className="p-1 text-indigo-600 hover:bg-indigo-50/70 rounded-full transition-colors">
            <RefreshCw className="h-4 w-4" />
          </button>
        </div>
      </div>
      <div className="flex justify-center items-center h-64 bg-indigo-50/30 rounded-lg">
        <div className="text-center">
          <Globe className="h-12 w-12 mx-auto text-indigo-400 mb-2" />
          <div className="text-sm text-indigo-600 font-medium">
            Geographic distribution of visa applications
          </div>
          <div className="text-xs text-indigo-400 mt-1">
            Hover over regions to see detailed statistics
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4 mt-4">
        {data.topCountries.map((country: any, index: number) => (
          <div key={index} className="flex items-center">
            <div className="w-8 h-6 border border-indigo-100/40 mr-2 flex-shrink-0 bg-indigo-100/30"></div>
            <div className="text-xs">
              <div className="font-medium text-indigo-800">{country.name}</div>
              <div className="text-gray-500">{country.value} applications</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </GlassCard>
);

// Mock data for charts
const approvalRateData = {
  title: "Visa Approval Rates by Country",
  data: [
    { label: "USA", value: 0.7 },
    { label: "UK", value: 0.6 },
    { label: "CAN", value: 0.8 },
    { label: "AUS", value: 0.75 },
    { label: "IND", value: 0.45 },
    { label: "CHN", value: 0.5 },
  ],
  maxValue: 1,
  description: "Approval percentage by applicant nationality"
};

const processingTimeData = {
  title: "Average Processing Time (Days)",
  data: [
    { label: "Jan", value: 0.6 },
    { label: "Feb", value: 0.7 },
    { label: "Mar", value: 0.5 },
    { label: "Apr", value: 0.6 },
    { label: "May", value: 0.8 },
    { label: "Jun", value: 0.7 },
    { label: "Jul", value: 0.5 },
  ],
  description: "Monthly average processing time in days"
};

const applicationTypeData = {
  title: "Applications by Visa Type",
  data: [
    { label: "Tourist", value: 430, color: "#4F46E5" },
    { label: "Student", value: 320, color: "#60A5FA" },
    { label: "Work", value: 280, color: "#818CF8" },
    { label: "Family", value: 190, color: "#C7D2FE" },
  ],
  description: "Distribution of applications by visa category"
};

const geographicData = {
  title: "Geographic Distribution",
  topCountries: [
    { name: "United States", value: 235 },
    { name: "India", value: 187 },
    { name: "China", value: 156 },
    { name: "United Kingdom", value: 98 },
  ],
  description: "Applications by country of origin"
};

const monthlyStatsData = {
  title: "Monthly Application Statistics",
  data: [
    { label: "Jan", value: 0.4 },
    { label: "Feb", value: 0.5 },
    { label: "Mar", value: 0.7 },
    { label: "Apr", value: 0.6 },
    { label: "May", value: 0.8 },
    { label: "Jun", value: 0.7 },
    { label: "Jul", value: 0.9 },
  ],
  description: "Monthly application submissions"
};

function StatCard({ title, value, change, icon, statClass }: {
  title: string;
  value: string;
  change: number;
  icon: React.ReactNode;
  statClass: string;
}) {
  return (
    <GlassCard className="transform transition hover:translate-y-[-4px]" opacity="high">
      <div className="p-6 flex justify-between">
        <div>
          <h3 className="text-gray-600 text-sm font-medium">{title}</h3>
          <div className="text-2xl font-bold mt-2 text-indigo-800">{value}</div>
          <div className={`flex items-center mt-2 text-sm ${statClass}`}>
            {change >= 0 ? (
              <TrendingUp className="h-4 w-4 mr-1" />
            ) : (
              <TrendingDown className="h-4 w-4 mr-1" />
            )}
            <span>{Math.abs(change)}% from last month</span>
          </div>
        </div>
        <div className="p-3 bg-indigo-50/50 rounded-full h-fit">{icon}</div>
      </div>
    </GlassCard>
  );
}

export default function AnalyticsPage() {
  const [timeFilter, setTimeFilter] = useState("30d");
  
  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Analytics Dashboard
          </h1>
          <p className="text-gray-600 mt-1">Key metrics and performance indicators</p>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="relative">
            <select
              className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/70 rounded-lg bg-white/80 backdrop-blur-sm text-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
              <option value="all">All time</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <ChevronDown className="h-4 w-4 text-indigo-500" />
            </div>
          </div>
          
          <button className="flex items-center text-sm text-indigo-800 bg-indigo-50/80 backdrop-blur-sm border border-indigo-200/60 rounded-lg px-3 py-2 hover:bg-indigo-100/80 transition-colors">
            <Download className="h-4 w-4 mr-1" />
            Export Report
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <StatCard
          title="Total Applications"
          value="1,247"
          change={12.5}
          icon={<BarChart2 className="h-6 w-6 text-indigo-600" />}
          statClass="text-green-600"
        />
        <StatCard
          title="Approval Rate"
          value="68.3%"
          change={4.2}
          icon={<PieChart className="h-6 w-6 text-green-600" />}
          statClass="text-green-600"
        />
        <StatCard
          title="Processing Time"
          value="24.6 days"
          change={-7.8}
          icon={<Clock className="h-6 w-6 text-amber-500" />}
          statClass="text-green-600"
        />
        <StatCard
          title="Success Rate"
          value="92.7%"
          change={-1.4}
          icon={<LineChart className="h-6 w-6 text-blue-600" />}
          statClass="text-red-600"
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <BarChartPlaceholder data={approvalRateData} />
        <LineChartPlaceholder data={processingTimeData} />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <PieChartPlaceholder data={applicationTypeData} />
        <MapChartPlaceholder data={geographicData} />
      </div>
      
      <GlassCard className="transform transition hover:translate-y-[-4px]" opacity="high">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-indigo-800">Application Processing Statistics</h3>
            <div className="flex items-center gap-2">
              <button className="p-1.5 border border-indigo-200/60 rounded text-indigo-700 hover:bg-indigo-50/70 transition-colors text-sm flex items-center">
                <Filter className="h-4 w-4 mr-1" />
                Filter
              </button>
              <button className="p-1.5 border border-indigo-200/60 rounded text-indigo-700 hover:bg-indigo-50/70 transition-colors text-sm flex items-center">
                <Download className="h-4 w-4 mr-1" />
                Export
              </button>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-indigo-100/30">
              <thead className="bg-indigo-50/30">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                    Visa Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                    Applications
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                    Approval Rate
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                    Avg. Processing
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                    Rejection Reason
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-indigo-100/20">
                <tr className="hover:bg-indigo-50/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-indigo-800">Tourist Visa</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">430</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-full bg-indigo-100/40 rounded-full h-2.5 w-32">
                        <div className="bg-gradient-to-r from-green-500 to-emerald-400 h-2.5 rounded-full" style={{ width: "78%" }}></div>
                      </div>
                      <span className="ml-2 text-sm text-gray-600">78%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">18 days</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">Insufficient funds</td>
                </tr>
                <tr className="hover:bg-indigo-50/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-indigo-800">Student Visa</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">320</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-full bg-indigo-100/40 rounded-full h-2.5 w-32">
                        <div className="bg-gradient-to-r from-green-500 to-emerald-400 h-2.5 rounded-full" style={{ width: "65%" }}></div>
                      </div>
                      <span className="ml-2 text-sm text-gray-600">65%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">28 days</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">Incomplete documentation</td>
                </tr>
                <tr className="hover:bg-indigo-50/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-indigo-800">Work Visa</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">280</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-full bg-indigo-100/40 rounded-full h-2.5 w-32">
                        <div className="bg-gradient-to-r from-green-500 to-emerald-400 h-2.5 rounded-full" style={{ width: "54%" }}></div>
                      </div>
                      <span className="ml-2 text-sm text-gray-600">54%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">35 days</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">Job verification failed</td>
                </tr>
                <tr className="hover:bg-indigo-50/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-indigo-800">Family Visa</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">190</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-full bg-indigo-100/40 rounded-full h-2.5 w-32">
                        <div className="bg-gradient-to-r from-green-500 to-emerald-400 h-2.5 rounded-full" style={{ width: "82%" }}></div>
                      </div>
                      <span className="ml-2 text-sm text-gray-600">82%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">22 days</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">Relationship proof</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </GlassCard>
    </div>
  );
} 