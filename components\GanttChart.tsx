import React from 'react';

interface GanttChartProps {
  currentStage: number;
}

export default function GanttChart({ currentStage }: GanttChartProps) {
  const stages = [
    { name: 'Application', status: currentStage >= 1 ? 'completed' : 'pending' },
    { name: 'Documentation', status: currentStage >= 2 ? 'completed' : 'pending' },
    { name: 'Interview', status: currentStage >= 3 ? 'completed' : 'pending' },
    { name: 'Approval', status: currentStage >= 4 ? 'completed' : 'pending' },
  ];

  return (
    <div className="w-full">
      {stages.map((stage, index) => (
        <div key={stage.name} className="flex items-center mb-2">
          <div className="w-24 text-sm">{stage.name}</div>
          <div className="flex-1 h-4 bg-gray-200 rounded-full overflow-hidden">
            <div
              className={`h-full ${
                stage.status === 'completed' ? 'bg-green-500' : 'bg-gray-400'
              }`}
              style={{ width: stage.status === 'completed' ? '100%' : '0%' }}
            ></div>
          </div>
        </div>
      ))}
    </div>
  );
} 