"use client";

import { useState } from "react";
import { 
  Check<PERSON><PERSON>cle2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>Check, 
  CalendarDays, 
  Filter,
  Search,
  RefreshCcw,
  Download,
  FileX,
  FilePlus2,
  AlertCircle
} from "lucide-react";
import { ComplianceTracker } from "../components/compliance-tracker";

// Mock data
const recentViolations = [
  { id: 'vio1', type: 'document-accuracy', executive: '<PERSON><PERSON>', date: '2023-05-15', severity: 'low' },
  { id: 'vio2', type: 'response-time', executive: '<PERSON><PERSON> <PERSON>', date: '2023-05-14', severity: 'medium' },
  { id: 'vio3', type: 'policy-violation', executive: '<PERSON><PERSON><PERSON>', date: '2023-05-13', severity: 'low' },
  { id: 'vio4', type: 'document-accuracy', executive: '<PERSON><PERSON>', date: '2023-05-12', severity: 'high' }
];

const pendingAlerts = [
  { id: 'alert1', type: 'deadline-approaching', description: 'EB-1 submission deadline for client ABC Corp', dueDate: '2023-05-20' },
  { id: 'alert2', type: 'follow-up-required', description: 'O-1 document collection from client <PERSON>', dueDate: '2023-05-18' },
  { id: 'alert3', type: 'escalation-needed', description: 'Complaint resolution for client XYZ Industries', dueDate: '2023-05-16' }
];

const auditLog = [
  { id: 'log1', action: 'DOCUMENT_UPLOADED', executive: 'Priya Sharma', client: 'ABC Corp', timestamp: 'Today, 14:32', status: 'complete' },
  { id: 'log2', action: 'DEADLINE_EXTENDED', executive: 'Rahul Singh', client: 'John Doe', timestamp: 'Today, 11:15', status: 'pending-review' },
  { id: 'log3', action: 'POLICY_EXCEPTION', executive: 'Amit Patel', client: 'XYZ Industries', timestamp: 'Yesterday, 16:45', status: 'flagged' },
  { id: 'log4', action: 'DOCUMENT_DELETED', executive: 'Neha Kumar', client: 'Global Tech', timestamp: 'Yesterday, 14:20', status: 'flagged' },
];

export default function CompliancePage() {
  const [timeframe, setTimeframe] = useState("This Month");
  const [filterStatus, setFilterStatus] = useState("all");
  
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">Compliance Review</h1>
          <p className="text-gray-500 mt-1">Monitor team adherence to company policies and procedures</p>
        </div>
        <div className="flex items-center gap-2">
          <select 
            className="bg-white border border-gray-300 text-gray-700 py-2 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
          >
            <option>Today</option>
            <option>This Week</option>
            <option>This Month</option>
            <option>This Quarter</option>
          </select>
          <button className="flex items-center gap-1 bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700">
            <FileCheck className="h-4 w-4" />
            <span>Generate Report</span>
          </button>
        </div>
      </div>

      {/* Compliance Overview Panel */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h2 className="text-lg font-medium text-gray-800 mb-5">Compliance Overview</h2>
        <ComplianceTracker 
          violations={recentViolations}
          alerts={pendingAlerts}
          kpis={{
            documentAccuracy: 98.4,
            policyAdherence: 99.1
          }}
        />
      </div>

      {/* Audit Log Panel */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-medium text-gray-800">Compliance Audit Log</h2>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="h-4 w-4 text-gray-400 absolute left-3 top-1/2 -translate-y-1/2" />
              <input 
                type="text" 
                placeholder="Search logs..." 
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>
            <select 
              className="bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="complete">Complete</option>
              <option value="pending-review">Pending Review</option>
              <option value="flagged">Flagged</option>
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Executive
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {auditLog
                .filter(log => filterStatus === 'all' ? true : log.status === filterStatus)
                .map((log) => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {log.action === 'DOCUMENT_UPLOADED' && <FilePlus2 className="h-4 w-4 text-green-500 mr-2" />}
                      {log.action === 'DEADLINE_EXTENDED' && <CalendarDays className="h-4 w-4 text-blue-500 mr-2" />}
                      {log.action === 'POLICY_EXCEPTION' && <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />}
                      {log.action === 'DOCUMENT_DELETED' && <FileX className="h-4 w-4 text-red-500 mr-2" />}
                      <span className="text-sm text-gray-900">
                        {log.action === 'DOCUMENT_UPLOADED' && 'Document Uploaded'}
                        {log.action === 'DEADLINE_EXTENDED' && 'Deadline Extended'}
                        {log.action === 'POLICY_EXCEPTION' && 'Policy Exception'}
                        {log.action === 'DOCUMENT_DELETED' && 'Document Deleted'}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.executive}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.client}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.timestamp}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      ${log.status === 'complete' ? 'bg-green-100 text-green-800' :
                        log.status === 'pending-review' ? 'bg-blue-100 text-blue-800' :
                        'bg-red-100 text-red-800'}`}
                    >
                      {log.status === 'complete' && <CheckCircle2 className="mr-1 h-3 w-3" />}
                      {log.status === 'pending-review' && <RefreshCcw className="mr-1 h-3 w-3" />}
                      {log.status === 'flagged' && <AlertTriangle className="mr-1 h-3 w-3" />}
                      {log.status === 'complete' && 'Complete'}
                      {log.status === 'pending-review' && 'Pending Review'}
                      {log.status === 'flagged' && 'Flagged'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-indigo-600 hover:text-indigo-900 mr-4">View</button>
                    <button className="text-gray-600 hover:text-gray-900">
                      <Download className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex justify-between items-center mt-5">
          <div className="text-sm text-gray-500">
            Showing {auditLog.filter(log => filterStatus === 'all' ? true : log.status === filterStatus).length} of {auditLog.length} entries
          </div>
          <div className="flex space-x-1">
            <button className="px-3 py-1 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50">
              Previous
            </button>
            <button className="px-3 py-1 border border-indigo-500 rounded-md bg-indigo-500 text-white hover:bg-indigo-600">
              1
            </button>
            <button className="px-3 py-1 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50">
              2
            </button>
            <button className="px-3 py-1 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50">
              Next
            </button>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Document Accuracy</h3>
          <div className="text-center">
            <div className="inline-flex items-center justify-center p-4 bg-green-50 rounded-full">
              <div className="text-3xl font-bold text-green-700">98.4%</div>
            </div>
            <p className="mt-2 text-sm text-gray-500">Above company target (95%)</p>
          </div>
          <div className="mt-4">
            <div className="text-sm font-medium text-gray-700 mb-1">Historical Performance</div>
            <div className="flex items-end h-40 gap-1 mt-2">
              <div className="flex-1 bg-green-100 rounded-t" style={{height: '85%'}}></div>
              <div className="flex-1 bg-green-200 rounded-t" style={{height: '90%'}}></div>
              <div className="flex-1 bg-green-300 rounded-t" style={{height: '88%'}}></div>
              <div className="flex-1 bg-green-400 rounded-t" style={{height: '92%'}}></div>
              <div className="flex-1 bg-green-500 rounded-t" style={{height: '95%'}}></div>
              <div className="flex-1 bg-green-600 rounded-t" style={{height: '98.4%'}}></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Q1</span>
              <span>Q2</span>
              <span>Q3</span>
              <span>Q4</span>
              <span>Q1</span>
              <span>Current</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Policy Adherence</h3>
          <div className="text-center">
            <div className="inline-flex items-center justify-center p-4 bg-blue-50 rounded-full">
              <div className="text-3xl font-bold text-blue-700">99.1%</div>
            </div>
            <p className="mt-2 text-sm text-gray-500">Above company target (98%)</p>
          </div>
          <div className="mt-4 space-y-3">
            <div>
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">Documentation</span>
                <span className="font-medium text-gray-800">99.5%</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-1.5">
                <div className="h-1.5 rounded-full bg-blue-500" style={{width: '99.5%'}}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">Communication</span>
                <span className="font-medium text-gray-800">98.7%</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-1.5">
                <div className="h-1.5 rounded-full bg-blue-500" style={{width: '98.7%'}}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">Response Time</span>
                <span className="font-medium text-gray-800">97.9%</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-1.5">
                <div className="h-1.5 rounded-full bg-blue-500" style={{width: '97.9%'}}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">Procedural</span>
                <span className="font-medium text-gray-800">99.8%</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-1.5">
                <div className="h-1.5 rounded-full bg-blue-500" style={{width: '99.8%'}}></div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Risk Assessment</h3>
          <div className="space-y-4">
            <div className="bg-green-50 p-3 rounded-lg flex items-start">
              <div className="rounded-full bg-green-100 p-1.5 mr-3">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-green-700">Low Risk Areas</h4>
                <p className="text-xs text-green-600 mt-1">Documentation standards, consultation recording, client response time</p>
              </div>
            </div>
            
            <div className="bg-amber-50 p-3 rounded-lg flex items-start">
              <div className="rounded-full bg-amber-100 p-1.5 mr-3">
                <AlertCircle className="h-4 w-4 text-amber-600" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-amber-700">Watch Areas</h4>
                <p className="text-xs text-amber-600 mt-1">Lead qualification, deadline management, workload distribution</p>
              </div>
            </div>
            
            <div className="bg-red-50 p-3 rounded-lg flex items-start">
              <div className="rounded-full bg-red-100 p-1.5 mr-3">
                <AlertTriangle className="h-4 w-4 text-red-600" />
              </div>
              <div>
                <h4 className="text-sm font-medium text-red-700">Risk Areas</h4>
                <p className="text-xs text-red-600 mt-1">Advanced case documentation from Team B, specific visa deadline adherence</p>
              </div>
            </div>
            
            <button className="w-full mt-2 flex items-center justify-center gap-2 bg-indigo-50 text-indigo-700 py-2 px-3 rounded-lg hover:bg-indigo-100">
              <FileCheck className="h-4 w-4" />
              <span>View Full Risk Report</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 