"use client";

import { useState } from "react";
import { 
  Settings, 
  Users, 
  Lock, 
  Bell, 
  Globe, 
  Database, 
  Server,
  Shield, 
  Smartphone,
  Palette,
  Mail,
  HelpCircle,
  FileText,
  Save
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Import our reusable components
import SettingsCard from "@/app/components/admin/SettingsCard";
import SettingsSection from "@/app/components/admin/SettingsSection";
import FormField from "@/app/components/admin/FormField";
import SaveButton from "@/app/components/admin/SaveButton";

export default function SettingsPage() {
  const [isSaving, setIsSaving] = useState(false);

  // Mock settings
  const [generalSettings, setGeneralSettings] = useState({
    siteName: "Visa Mentor",
    description: "Your trusted advisor for visa applications",
    supportEmail: "<EMAIL>",
    timezone: "UTC",
    language: "en"
  });

  // Mock function to save settings
  const saveGeneralSettings = () => {
    setIsSaving(true);
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
    }, 1000);
  };

  return (
    <div>
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
              Admin Settings
            </h1>
            <p className="text-sm text-gray-600">Configure system settings and preferences</p>
          </div>
          
          <div className="flex gap-2">
            <SaveButton 
              isLoading={isSaving} 
              onClick={saveGeneralSettings} 
            />
          </div>
        </div>
      </div>

      {/* Settings Dashboard - Overview of all settings sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <SettingsCard
          title="General Settings"
          description="Configure application name, description, and basic preferences"
          icon={<Settings className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/general"
        />
        
        <SettingsCard
          title="User Management"
          description="Configure user roles, permissions, and access controls"
          icon={<Users className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/users"
        />
        
        <SettingsCard
          title="Security"
          description="Set password policies, 2FA requirements, and session timeouts"
          icon={<Lock className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/security"
          badge={{ text: "Important", color: "bg-red-100 text-red-700" }}
        />
        
        <SettingsCard
          title="Notifications"
          description="Configure email templates, alerts, and notification preferences"
          icon={<Bell className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/notifications"
        />
        
        <SettingsCard
          title="Integrations"
          description="Connect third-party services, APIs, and external systems"
          icon={<Globe className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/integrations"
        />
        
        <SettingsCard
          title="Compliance & Privacy"
          description="Configure data retention, consent preferences, and legal settings"
          icon={<Shield className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/compliance"
        />
        
        <SettingsCard
          title="Backups & Maintenance"
          description="Schedule backups, system maintenance, and updates"
          icon={<Database className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/backups"
        />
        
        <SettingsCard
          title="Appearance"
          description="Configure application themes, branding, and visual customizations"
          icon={<Palette className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/appearance"
        />
        
        <SettingsCard
          title="Mobile App"
          description="Configure mobile app settings, push notifications, and app features"
          icon={<Smartphone className="h-5 w-5 text-indigo-600" />}
          path="/admin/settings/mobile"
          badge={{ text: "New", color: "bg-green-100 text-green-700" }}
        />
      </div>

      {/* Preview of General Settings Form */}
      <GlassCard className="transform transition hover:translate-y-[-4px]">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-indigo-800 mb-4">General Settings Preview</h2>
          <p className="text-sm text-gray-600 mb-6">A preview of the general settings that can be configured</p>
          
          <div className="space-y-4">
            <div className="space-y-1">
              <label className="text-xs font-medium text-indigo-800">Application Name</label>
              <p className="text-xs text-indigo-600 mb-1">The name of your application as displayed to users</p>
              <input
                type="text"
                className="w-full p-2 border border-indigo-300 rounded-lg bg-white/70 text-indigo-800 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={generalSettings.siteName}
                onChange={(e) => setGeneralSettings({...generalSettings, siteName: e.target.value})}
              />
            </div>
            
            <div className="space-y-1">
              <label className="text-xs font-medium text-indigo-800">Description</label>
              <p className="text-xs text-indigo-600 mb-1">A brief description of your application</p>
              <textarea
                className="w-full p-2 border border-indigo-300 rounded-lg bg-white/70 text-indigo-800 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                rows={3}
                value={generalSettings.description}
                onChange={(e) => setGeneralSettings({...generalSettings, description: e.target.value})}
              />
            </div>
            
            <div className="space-y-1">
              <label className="text-xs font-medium text-indigo-800">Support Email</label>
              <p className="text-xs text-indigo-600 mb-1">The email address users can contact for support</p>
              <input
                type="email"
                className="w-full p-2 border border-indigo-300 rounded-lg bg-white/70 text-indigo-800 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                value={generalSettings.supportEmail}
                onChange={(e) => setGeneralSettings({...generalSettings, supportEmail: e.target.value})}
              />
            </div>
            
            <div className="pt-4">
              <button 
                onClick={saveGeneralSettings}
                disabled={isSaving}
                className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </GlassCard>
    </div>
  );
} 