"use client";

import { useState } from "react";
import { 
  Users,
  Search, 
  Filter, 
  ChevronDown,
  ArrowUpDown,
  MessageSquare,
  Mail,
  CheckCircle,
  Clock,
  AlertCircle,
  BarChart4,
  Award,
  Zap,
  Star,
  Calendar,
  FileText,
  PlusCircle
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock data
const teamMembers = [
  {
    id: "exec1",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    role: "Senior Visa Consultant",
    avatar: "",
    status: "active",
    performance: "exceeding",
    metrics: { 
      conversions: 42, 
      revenue: "₹128k",
      satisfaction: 4.7,
      responseTime: "1.8h",
      completionRate: 98
    },
    clients: 24,
    lastActivity: "2h ago"
  },
  {
    id: "exec2",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    role: "Visa Specialist",
    avatar: "",
    status: "active",
    performance: "meeting",
    metrics: { 
      conversions: 35, 
      revenue: "₹105k",
      satisfaction: 4.5,
      responseTime: "2.2h",
      completionRate: 95
    },
    clients: 18,
    lastActivity: "1h ago"
  },
  {
    id: "exec3",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    role: "Immigration Advisor",
    avatar: "",
    status: "active",
    performance: "meeting",
    metrics: { 
      conversions: 38, 
      revenue: "₹116k",
      satisfaction: 4.6,
      responseTime: "2.0h",
      completionRate: 97
    },
    clients: 21,
    lastActivity: "30m ago"
  },
  {
    id: "exec4",
    name: "Neha Kumar",
    email: "<EMAIL>",
    role: "Senior Visa Consultant",
    avatar: "",
    status: "active",
    performance: "exceeding",
    metrics: { 
      conversions: 44, 
      revenue: "₹132k",
      satisfaction: 4.8,
      responseTime: "1.7h",
      completionRate: 99
    },
    clients: 26,
    lastActivity: "15m ago"
  },
  {
    id: "exec5",
    name: "Vikram Mehta",
    email: "<EMAIL>",
    role: "Visa Processing Specialist",
    avatar: "",
    status: "away",
    performance: "meeting",
    metrics: {
      conversions: 32,
      revenue: "₹98k",
      satisfaction: 4.4,
      responseTime: "2.5h",
      completionRate: 94
    },
    clients: 17,
    lastActivity: "3h ago"
  },
  {
    id: "exec6",
    name: "Ananya Desai",
    email: "<EMAIL>",
    role: "Document Verification Specialist",
    avatar: "",
    status: "inactive",
    performance: "below",
    metrics: {
      conversions: 28,
      revenue: "₹84k",
      satisfaction: 4.2,
      responseTime: "3.1h",
      completionRate: 92
    },
    clients: 15,
    lastActivity: "2d ago"
  }
];

const teamPerformance = {
  averageConversion: 36.5,
  conversionTrend: 2.8,
  averageRevenue: "₹110.5k",
  revenueTrend: 3.5,
  averageSatisfaction: 4.53,
  satisfactionTrend: 0.2,
  averageResponseTime: "2.2h",
  responseTimeTrend: -0.3
};

const recentActivities = [
  {
    id: "act1",
    memberId: "exec1",
    memberName: "Priya Sharma",
    action: "completed visa processing",
    client: "Rajesh Verma",
    timestamp: "2023-05-15T10:30:00Z"
  },
  {
    id: "act2",
    memberId: "exec4",
    memberName: "Neha Kumar",
    action: "submitted documentation",
    client: "Sunita Patel",
    timestamp: "2023-05-15T09:45:00Z"
  },
  {
    id: "act3",
    memberId: "exec2",
    memberName: "Rahul Singh",
    action: "scheduled interview",
    client: "Anil Kumar",
    timestamp: "2023-05-15T09:15:00Z"
  },
  {
    id: "act4",
    memberId: "exec3",
    memberName: "Amit Patel",
    action: "updated application status",
    client: "Meera Shah",
    timestamp: "2023-05-15T08:50:00Z"
  }
];

export default function TeamPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [performanceFilter, setPerformanceFilter] = useState("all");
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [selectedMember, setSelectedMember] = useState<string | null>(null);

  // Filter team members
  const filteredMembers = teamMembers.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          member.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          member.role.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || member.status === statusFilter;
    const matchesPerformance = performanceFilter === "all" || member.performance === performanceFilter;
    
    return matchesSearch && matchesStatus && matchesPerformance;
  });
  
  // Sort team members
  const sortedMembers = [...filteredMembers].sort((a, b) => {
    if (sortBy === "name") {
      return sortOrder === "asc" 
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } else if (sortBy === "performance") {
      const performanceRank = { exceeding: 3, meeting: 2, below: 1 };
      return sortOrder === "asc"
        ? performanceRank[a.performance as keyof typeof performanceRank] - performanceRank[b.performance as keyof typeof performanceRank]
        : performanceRank[b.performance as keyof typeof performanceRank] - performanceRank[a.performance as keyof typeof performanceRank];
    } else if (sortBy === "clients") {
      return sortOrder === "asc" ? a.clients - b.clients : b.clients - a.clients;
    } else if (sortBy === "satisfaction") {
      return sortOrder === "asc" ? a.metrics.satisfaction - b.metrics.satisfaction : b.metrics.satisfaction - a.metrics.satisfaction;
    }
    return 0;
  });

  // Handle sort
  const handleSort = (field: string) => {
    if (field === sortBy) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("asc");
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Team Overview
          </h1>
          <p className="text-gray-600 mt-1">Manage and monitor your sales team performance</p>
        </div>
        <div>
          <button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 text-sm font-medium hover:from-blue-700 hover:to-indigo-800 transition-all shadow-md flex items-center">
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Team Member
          </button>
        </div>
      </div>

      {/* Team Performance Summary */}
      <GlassCard className="p-6">
        <h2 className="text-lg font-semibold text-indigo-800 mb-4">Team Performance Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-indigo-600 font-medium">Avg. Conversion Rate</p>
                <p className="text-2xl font-bold text-indigo-800">{teamPerformance.averageConversion}%</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white shadow-sm">
                <BarChart4 className="h-5 w-5" />
        </div>
          </div>
            <div className="mt-2 flex items-center">
              <p className="text-xs text-green-600 flex items-center">
                <ArrowUpDown className="h-3 w-3 mr-1" />
                {teamPerformance.conversionTrend}% vs last month
              </p>
        </div>
      </div>

          <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-indigo-600 font-medium">Avg. Revenue</p>
                <p className="text-2xl font-bold text-indigo-800">{teamPerformance.averageRevenue}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-br from-green-400 to-emerald-300 rounded-full flex items-center justify-center text-white shadow-sm">
                <Zap className="h-5 w-5" />
              </div>
            </div>
            <div className="mt-2 flex items-center">
              <p className="text-xs text-green-600 flex items-center">
                <ArrowUpDown className="h-3 w-3 mr-1" />
                {teamPerformance.revenueTrend}% vs last month
              </p>
            </div>
        </div>

          <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4">
                  <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-indigo-600 font-medium">Avg. Satisfaction</p>
                <p className="text-2xl font-bold text-indigo-800">{teamPerformance.averageSatisfaction}★</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-br from-purple-400 to-pink-300 rounded-full flex items-center justify-center text-white shadow-sm">
                <Star className="h-5 w-5" />
                    </div>
                  </div>
            <div className="mt-2 flex items-center">
              <p className="text-xs text-green-600 flex items-center">
                <ArrowUpDown className="h-3 w-3 mr-1" />
                {teamPerformance.satisfactionTrend} vs last month
              </p>
            </div>
                </div>
                
          <div className="bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4">
                  <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-indigo-600 font-medium">Avg. Response Time</p>
                <p className="text-2xl font-bold text-indigo-800">{teamPerformance.averageResponseTime}</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-br from-blue-400 to-cyan-300 rounded-full flex items-center justify-center text-white shadow-sm">
                <Clock className="h-5 w-5" />
              </div>
            </div>
            <div className="mt-2 flex items-center">
              <p className="text-xs text-green-600 flex items-center">
                <ArrowUpDown className="h-3 w-3 mr-1" />
                {Math.abs(teamPerformance.responseTimeTrend)}h improvement
              </p>
                    </div>
                  </div>
                </div>
      </GlassCard>

      {/* Team Members List */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <GlassCard className="overflow-hidden">
            {/* Filters */}
            <div className="p-4 border-b border-indigo-100/30">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div className="relative w-full md:w-64">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-indigo-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search team members..."
                    className="pl-10 pr-3 py-2 w-full border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                
                <div className="flex items-center gap-2 w-full md:w-auto">
                  <div className="relative w-full md:w-auto">
                    <select
                      className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                    >
                      <option value="all">All Statuses</option>
                      <option value="active">Active</option>
                      <option value="away">Away</option>
                      <option value="inactive">Inactive</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
              
                  <div className="relative w-full md:w-auto">
                    <select
                      className="appearance-none pl-3 pr-8 py-2 border border-indigo-200/60 rounded-lg text-sm bg-white/80 backdrop-blur-sm text-indigo-800 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      value={performanceFilter}
                      onChange={(e) => setPerformanceFilter(e.target.value)}
                    >
                      <option value="all">All Performance</option>
                      <option value="exceeding">Exceeding</option>
                      <option value="meeting">Meeting</option>
                      <option value="below">Below Target</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <ChevronDown className="h-4 w-4 text-indigo-500" />
              </div>
            </div>
          </div>
        </div>
      </div>

            {/* Team Members Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-indigo-100/30">
                <thead className="bg-indigo-50/30">
                  <tr>
                    <th 
                      scope="col" 
                      className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort("name")}
                    >
                      <div className="flex items-center">
                        Team Member
                        {sortBy === "name" && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
            </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">
                      Status
                    </th>
                    <th 
                      scope="col" 
                      className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort("performance")}
                    >
                      <div className="flex items-center">
                        Performance
                        {sortBy === "performance" && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
                        </div>
                    </th>
                    <th 
                      scope="col" 
                      className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort("clients")}
                    >
                      <div className="flex items-center">
                        Clients
                        {sortBy === "clients" && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="px-6 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort("satisfaction")}
                    >
                      <div className="flex items-center">
                        Satisfaction
                        {sortBy === "satisfaction" && (
                          <ArrowUpDown className="ml-1 h-4 w-4" />
                        )}
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-indigo-700 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white/50 backdrop-blur-sm divide-y divide-indigo-100/30">
                  {sortedMembers.map((member) => (
                    <tr 
                      key={member.id} 
                      className="hover:bg-indigo-50/30 transition-colors cursor-pointer"
                      onClick={() => setSelectedMember(member.id)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white font-medium shadow-sm">
                            {member.name.split(' ').map(n => n[0]).join('')}
                    </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-indigo-800">{member.name}</div>
                            <div className="text-xs text-gray-600">{member.role}</div>
                    </div>
                  </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-medium rounded-full backdrop-blur-sm ${
                          member.status === "active" ? "bg-green-100/70 text-green-800" :
                          member.status === "away" ? "bg-amber-100/70 text-amber-800" :
                          "bg-gray-100/70 text-gray-800"
                        }`}>
                          {member.status === "active" ? "Active" :
                           member.status === "away" ? "Away" : "Inactive"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-medium rounded-full backdrop-blur-sm ${
                          member.performance === "exceeding" ? "bg-green-100/70 text-green-800" :
                          member.performance === "meeting" ? "bg-blue-100/70 text-blue-800" :
                          "bg-orange-100/70 text-orange-800"
                        }`}>
                          {member.performance === "exceeding" ? "Exceeding" :
                           member.performance === "meeting" ? "On Target" : "Below Target"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-indigo-800">
                        {member.clients}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-sm text-indigo-800 mr-1">{member.metrics.satisfaction}</span>
                          <Star className="h-4 w-4 text-amber-500 fill-amber-500" />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                            <MessageSquare className="h-4 w-4" />
                          </button>
                          <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                            <Mail className="h-4 w-4" />
                          </button>
                          <button className="p-1.5 text-indigo-600 hover:text-indigo-900 bg-indigo-50/70 backdrop-blur-sm rounded-lg transition-colors">
                            <FileText className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </GlassCard>
        </div>

        {/* Recent Activity */}
        <div>
          <GlassCard className="p-6 h-full">
            <h2 className="text-lg font-semibold text-indigo-800 mb-4">Recent Team Activity</h2>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 pb-4 border-b border-indigo-100/30 last:border-0 last:pb-0">
                  <div className="bg-indigo-50/70 backdrop-blur-sm rounded-full p-2 flex-shrink-0">
                    <Calendar className="h-5 w-5 text-indigo-600" />
                      </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-indigo-800">
                      <span className="font-semibold">{activity.memberName}</span> {activity.action}
                    </p>
                    <p className="text-xs text-gray-600">Client: {activity.client}</p>
                    <p className="text-xs text-gray-600 mt-1">{formatDate(activity.timestamp)}</p>
                  </div>
                </div>
              ))}
            </div>
            <button className="mt-4 w-full bg-indigo-50/70 backdrop-blur-sm text-indigo-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-indigo-100/70 transition-colors">
              View All Activity
              </button>
          </GlassCard>
        </div>
      </div>
    </div>
  );
} 