"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Search, 
  UserPlus, 
  Filter, 
  Phone, 
  Mail, 
  Calendar, 
  AlertCircle, 
  Check
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Mock data for clients
const clients = [
  { 
    id: 1, 
    name: "<PERSON><PERSON>", 
    email: "<EMAIL>", 
    phone: "+91 98765 43210", 
    status: "Active",
    service: "Student Visa",
    country: "Australia",
    lastContact: "2 days ago",
    stage: "Documents Submitted",
    healthScore: 85,
  },
  { 
    id: 2, 
    name: "<PERSON>", 
    email: "<EMAIL>", 
    phone: "+91 87654 32109", 
    status: "Active",
    service: "Work Visa",
    country: "Canada",
    lastContact: "1 week ago",
    stage: "Visa Interview",
    healthScore: 72,
  },
  { 
    id: 3, 
    name: "Ananya Desai", 
    email: "<EMAIL>", 
    phone: "+91 76543 21098", 
    status: "At Risk",
    service: "Tourist Visa",
    country: "United States",
    lastContact: "3 weeks ago",
    stage: "Application Review",
    healthScore: 45,
  },
  { 
    id: 4, 
    name: "Vikram Singh", 
    email: "<EMAIL>", 
    phone: "+91 65432 10987", 
    status: "Inactive",
    service: "Business Visa",
    country: "United Kingdom",
    lastContact: "1 month ago",
    stage: "Initial Consultation",
    healthScore: 30,
  },
  { 
    id: 5, 
    name: "Meera Kapoor", 
    email: "<EMAIL>", 
    phone: "+91 54321 09876", 
    status: "Active",
    service: "Family Visa",
    country: "New Zealand",
    lastContact: "5 days ago",
    stage: "Visa Approved",
    healthScore: 90,
  },
  { 
    id: 6, 
    name: "Arjun Mehta", 
    email: "<EMAIL>", 
    phone: "+91 43210 98765", 
    status: "Active",
    service: "Student Visa",
    country: "Germany",
    lastContact: "3 days ago",
    stage: "Visa Interview",
    healthScore: 78,
  },
  { 
    id: 7, 
    name: "Neha Gupta", 
    email: "<EMAIL>", 
    phone: "+91 32109 87654", 
    status: "At Risk",
    service: "Work Visa",
    country: "Singapore",
    lastContact: "2 weeks ago",
    stage: "Documents Verification",
    healthScore: 52,
  },
];

// Client action history mock data
const clientActions = [
  { id: 1, date: "2023-06-15", action: "Initial Consultation", notes: "Client interested in Student Visa for Australia" },
  { id: 2, date: "2023-06-22", action: "Documents Submitted", notes: "Passport, academic certificates submitted" },
  { id: 3, date: "2023-07-05", action: "Application Lodged", notes: "Visa application submitted to embassy" },
  { id: 4, date: "2023-07-18", action: "Follow-up Call", notes: "Informed client about application progress" },
  { id: 5, date: "2023-08-02", action: "Additional Documents", notes: "Requested financial proof documents" },
];

export default function ClientsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("all");

  // Filter clients based on search term and active tab
  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.service.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (activeTab === "all") return matchesSearch;
    if (activeTab === "active") return matchesSearch && client.status === "Active";
    if (activeTab === "at-risk") return matchesSearch && client.status === "At Risk";
    if (activeTab === "inactive") return matchesSearch && client.status === "Inactive";
    
    return matchesSearch;
  });

  // Get status badge color based on status
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "At Risk":
        return "bg-amber-100 text-amber-800";
      case "Inactive":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  // Get health score color based on score
  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return "text-green-500";
    if (score >= 60) return "text-amber-500";
    return "text-red-500";
  };

  // Handler for client selection
  const handleClientSelect = (client: any) => {
    setSelectedClient(client);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div className="flex-1 w-full">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
            <Input 
              placeholder="Search clients by name, email, or service" 
              className="pl-10 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div className="flex items-center gap-2 w-full md:w-auto">
          <Button className="flex-1 md:flex-none">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="default" className="flex-1 md:flex-none">
            <UserPlus className="h-4 w-4 mr-2" />
            Add Client
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Clients</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="at-risk">At Risk</TabsTrigger>
          <TabsTrigger value="inactive">Inactive</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Client List */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Clients ({filteredClients.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Service</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Health Score</TableHead>
                      <TableHead>Stage</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredClients.map((client) => (
                      <TableRow 
                        key={client.id} 
                        className={`cursor-pointer ${selectedClient?.id === client.id ? 'bg-gray-50' : ''}`}
                        onClick={() => handleClientSelect(client)}
                      >
                        <TableCell className="font-medium">{client.name}</TableCell>
                        <TableCell>{client.service} ({client.country})</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(client.status)}>
                            {client.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className={getHealthScoreColor(client.healthScore)}>
                            {client.healthScore}%
                          </span>
                        </TableCell>
                        <TableCell>{client.stage}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Client Details */}
        <div>
          {selectedClient ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Client Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">{selectedClient.name}</h3>
                  <Badge className={getStatusColor(selectedClient.status)}>
                    {selectedClient.status}
                  </Badge>
                  <div className="flex flex-col gap-3 mt-4">
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="text-sm">{selectedClient.email}</span>
                    </div>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="text-sm">{selectedClient.phone}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="text-sm">Last Contact: {selectedClient.lastContact}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Service Details</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-gray-500">Service:</span>
                      <p>{selectedClient.service}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Country:</span>
                      <p>{selectedClient.country}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Stage:</span>
                      <p>{selectedClient.stage}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Health Score:</span>
                      <p className={getHealthScoreColor(selectedClient.healthScore)}>
                        {selectedClient.healthScore}%
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Activity History</h4>
                  <div className="space-y-4">
                    {clientActions.map((action) => (
                      <div key={action.id} className="flex items-start gap-3">
                        <div className="rounded-full p-1 bg-blue-100">
                          {action.action.includes("Submitted") || action.action.includes("Lodged") ? (
                            <Check className="h-4 w-4 text-blue-600" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-blue-600" />
                          )}
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">{action.action}</span>
                            <span className="text-xs text-gray-500">{action.date}</span>
                          </div>
                          <p className="text-xs text-gray-600">{action.notes}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button size="sm" variant="outline">Contact</Button>
                  <Button size="sm" variant="outline">Add Action</Button>
                  <Button size="sm">View Complete Profile</Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center h-96">
                <div className="text-gray-400 text-center">
                  <UserPlus className="h-12 w-12 mx-auto mb-4" />
                  <h3 className="text-lg font-medium">No client selected</h3>
                  <p className="text-sm mt-2">Select a client from the list to view details</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
} 