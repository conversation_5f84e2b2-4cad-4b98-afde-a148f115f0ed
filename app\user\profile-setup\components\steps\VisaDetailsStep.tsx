"use client";

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { PROFILE_STEPS, useProfileCompletion } from '@/contexts/ProfileCompletionContext';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

// Define the form schema with validation
const visaDetailsSchema = z.object({
  visaType: z.string().min(1, 'Please select a visa type'),
  visaGoals: z.string().min(10, 'Please provide more details about your visa goals').optional(),
});

type VisaDetailsFormValues = z.infer<typeof visaDetailsSchema>;

const visaTypes = [
  { value: 'tourist', label: 'Tourist Visa' },
  { value: 'student', label: 'Student Visa' },
  { value: 'work', label: 'Work Visa' },
  { value: 'business', label: 'Business Visa' },
  { value: 'family', label: 'Family Visa' },
  { value: 'immigrant', label: 'Immigrant Visa' },
  { value: 'other', label: 'Other' },
];

export default function VisaDetailsStep() {
  const { moveToNextStep, moveToPrevStep, markStepComplete } = useProfileCompletion();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with default values
  const form = useForm<VisaDetailsFormValues>({
    resolver: zodResolver(visaDetailsSchema),
    defaultValues: {
      visaType: '',
      visaGoals: '',
    },
  });

  // Load user data when available
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch('/api/user/profile');
        if (response.ok) {
          const userData = await response.json();
          form.reset({
            visaType: userData.visaType || '',
            visaGoals: userData.visaGoals || '',
          });
        }
      } catch (error) {
        console.error('Failed to fetch user data:', error);
      }
    };

    fetchUserData();
  }, [form]);

  // Handle form submission
  const onSubmit = async (data: VisaDetailsFormValues) => {
    setIsLoading(true);
    
    try {
      // Send data to API
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          visaType: data.visaType,
          visaGoals: data.visaGoals,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      // Mark this step as complete
      markStepComplete(PROFILE_STEPS.VISA_DETAILS);
      
      toast.success('Visa details saved');
      moveToNextStep();
    } catch (error) {
      toast.error('Failed to save visa details');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Visa Details</h2>
        <p className="text-muted-foreground">
          Please provide information about the visa you're interested in.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="visaType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Visa Type</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select visa type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {visaTypes.map((visaType) => (
                      <SelectItem key={visaType.value} value={visaType.value}>
                        {visaType.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="visaGoals"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Visa Goals</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Briefly describe your goals for obtaining this visa..."
                    className="min-h-[120px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex justify-between pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={moveToPrevStep}
            >
              Previous
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : "Save & Continue"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
} 