import { NextRequest, NextResponse } from 'next/server';

// Mock user storage (in production, this would be in a database)
const userProfiles = new Map();

// GET /api/user/profile - Get the current user's profile data
export async function GET(request: NextRequest) {
  try {
    // Authentication removed - use mock user ID
    const mockUserId = "mock-user-id";

    // Get user profile from mock storage or create default
    let userProfile = userProfiles.get(mockUserId);
    
    if (!userProfile) {
      // Create default profile with mock data
      userProfile = {
        id: mockUserId,
        email: "<EMAIL>",
        name: "Demo User",
        image: null,
        role: 'USER',
        status: 'ACTIVE',
        profileCompleted: false,
        profileProgress: 30,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLogin: new Date().toISOString(),
        // Additional profile fields
        phone: '',
        address: '',
        dateOfBirth: '',
        nationality: '',
        passportNumber: '',
        emergencyContact: '',
        preferences: {
          notifications: true,
          newsletter: false,
          language: 'en'
        }
      };

      // Store in mock storage
      userProfiles.set(mockUserId, userProfile);
    }

    return NextResponse.json({ user: userProfile });
  } catch (error) {
    console.error('API: Error fetching user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/user/profile - Update the current user's profile data
export async function PUT(request: NextRequest) {
  try {
    // Authentication removed - use mock user ID
    const mockUserId = "mock-user-id";

    const body = await request.json();

    // Get existing profile or create new one
    let userProfile = userProfiles.get(mockUserId) || {
      id: mockUserId,
      email: "<EMAIL>",
      name: "Demo User",
      image: null,
      role: 'USER',
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
    };

    // Update profile with new data
    userProfile = {
      ...userProfile,
      ...body,
      updatedAt: new Date().toISOString(),
    };

    // Calculate profile completion
    const requiredFields = ['name', 'email', 'phone', 'address', 'dateOfBirth'];
    const completedFields = requiredFields.filter(field => userProfile[field] && userProfile[field].trim() !== '');
    userProfile.profileProgress = Math.round((completedFields.length / requiredFields.length) * 100);
    userProfile.profileCompleted = userProfile.profileProgress === 100;

    // Store updated profile
    userProfiles.set(mockUserId, userProfile);

    return NextResponse.json({ user: userProfile });
  } catch (error) {
    console.error('API: Error updating user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}