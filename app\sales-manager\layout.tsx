"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>hare2,
  FileCheck,
  BarChart2,
  Bell,
  Settings,
  Menu,
  X,
  LogOut,
  Calendar,
  MessageSquare,
  HelpCircle
} from "lucide-react";

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
}

const NavItem = ({ href, icon, label, active }: NavItemProps) => (
  <Link href={href}>
    <div
      className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${
        active
          ? "bg-gradient-to-r from-blue-600 to-indigo-700 text-white font-medium shadow-sm"
          : "text-indigo-800 hover:bg-indigo-50/70 hover:text-indigo-700 backdrop-blur-sm"
      }`}
    >
      <div className="text-lg">{icon}</div>
      <span>{label}</span>
    </div>
  </Link>
);

export default function SalesManagerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
      if (window.innerWidth < 1024) {
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const navItems = [
    { href: "/sales-manager", icon: <BarChart2 />, label: "Dashboard" },
    { href: "/sales-manager/team", icon: <Users />, label: "Team Overview" },
    { href: "/sales-manager/analytics", icon: <PieChart />, label: "Performance Analytics" },
    { href: "/sales-manager/leads", icon: <Share2 />, label: "Lead Distribution" },
    { href: "/sales-manager/compliance", icon: <FileCheck />, label: "Compliance Review" },
    { href: "/sales-manager/calendar", icon: <Calendar />, label: "Calendar" },
    { href: "/sales-manager/settings", icon: <Settings />, label: "Settings" },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex">
      {/* Sidebar */}
      <aside
        className={`bg-white/80 backdrop-blur-lg fixed lg:static inset-y-0 left-0 z-50 transform ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        } lg:translate-x-0 transition-transform duration-300 ease-in-out w-64 border-r border-indigo-100/30 shadow-lg`}
      >
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between h-16 px-4 border-b border-indigo-100/30">
            <Link href="/sales-manager" className="flex items-center">
              <div className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700">Visa Mentor</div>
            </Link>
            {isMobile && (
              <button
                onClick={() => setSidebarOpen(false)}
                className="lg:hidden p-1 rounded-md text-indigo-500 hover:text-indigo-700 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            )}
          </div>

          <div className="overflow-y-auto py-4 flex flex-col flex-grow">
            <div className="px-4 py-2 text-xs uppercase tracking-wider text-indigo-600 font-medium">
              Main
            </div>
            <nav className="px-2 space-y-1 mt-2">
              {navItems.map((item) => (
                <NavItem
                  key={item.href}
                  href={item.href}
                  icon={item.icon}
                  label={item.label}
                  active={pathname === item.href}
                />
              ))}
            </nav>

            <div className="mt-auto">
              <div className="px-4 py-2 text-xs uppercase tracking-wider text-indigo-600 font-medium">
                Support
              </div>
              <nav className="px-2 space-y-1 mt-2">
                <NavItem
                  href="/sales-manager/help"
                  icon={<HelpCircle />}
                  label="Help & Resources"
                  active={pathname === "/sales-manager/help"}
                />
                <NavItem
                  href="/sales-manager/messages"
                  icon={<MessageSquare />}
                  label="Messages"
                  active={pathname === "/sales-manager/messages"}
                />
              </nav>
            </div>

            <div className="mt-4 p-4 border-t border-indigo-100/30">
              <Link href="/">
                <div className="flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 text-red-600 hover:bg-red-50/70 hover:text-red-700 backdrop-blur-sm">
                  <LogOut className="h-5 w-5" />
                  <span>Logout</span>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Top navigation */}
        <header className="bg-white/80 backdrop-blur-lg border-b border-indigo-100/30 shadow-sm">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex">
                {isMobile && (
                  <button
                    onClick={() => setSidebarOpen(true)}
                    className="lg:hidden p-1 rounded-md -ml-1 text-indigo-500 hover:text-indigo-700 transition-colors"
                  >
                    <Menu className="h-6 w-6" />
                  </button>
                )}
                <h1 className="ml-3 text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-700">Sales Manager Portal</h1>
              </div>
              <div className="flex items-center gap-4">
                <div className="relative">
                  <button className="p-1.5 rounded-full bg-indigo-50/70 backdrop-blur-sm text-indigo-600 hover:bg-indigo-100/70 transition-colors">
                    <Bell className="h-5 w-5" />
                  </button>
                  <span className="absolute top-0 right-0 h-2.5 w-2.5 rounded-full bg-gradient-to-r from-red-500 to-rose-600 border-2 border-white"></span>
                </div>
                <div className="flex items-center">
                  <div className="h-9 w-9 rounded-full bg-gradient-to-br from-indigo-600 to-blue-500 flex items-center justify-center text-white font-medium shadow-sm">
                    SM
                  </div>
                  <span className="ml-2 text-sm font-medium text-indigo-800">Sales Manager</span>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-blue-50 via-white to-indigo-50">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
} 