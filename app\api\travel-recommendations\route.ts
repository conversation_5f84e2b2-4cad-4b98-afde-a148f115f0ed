import { NextResponse } from 'next/server';

// Mock data for travel recommendations
const mockRecommendations = [
  {
    id: "rec1",
    title: "Explore the United States",
    country: "US",
    city: "New York",
    imageUrl: "https://images.unsplash.com/photo-1496442226666-8d4d0e62e6e9?q=80&w=1470&auto=format&fit=crop",
    description: "Experience the vibrant culture and iconic landmarks of New York City.",
    price: 1200,
    currency: "USD",
    rating: 4.8,
    reviewCount: 125,
    isBookmarked: false,
    tags: ["City Break", "Culture", "Shopping"]
  },
  {
    id: "rec2",
    title: "Experience the United Kingdom",
    country: "GB",
    city: "London",
    imageUrl: "https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?q=80&w=1470&auto=format&fit=crop",
    description: "Discover the historic landmarks and modern attractions of London.",
    price: 950,
    currency: "GBP",
    rating: 4.7,
    reviewCount: 98,
    isBookmarked: true,
    tags: ["History", "Museums", "City Break"]
  },
  {
    id: "rec3",
    title: "Discover India's Wonders",
    country: "IN",
    city: "Delhi",
    imageUrl: "https://images.unsplash.com/photo-1524492412937-b28074a5d7da?q=80&w=1471&auto=format&fit=crop",
    description: "Explore the rich heritage and diverse culture of Delhi and surrounding areas.",
    price: 850,
    currency: "USD",
    rating: 4.6,
    reviewCount: 87,
    isBookmarked: false,
    tags: ["Culture", "History", "Food"]
  }
];

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');
  const country = searchParams.get('country');
  
  // Apply country filter if provided
  let filteredRecommendations = [...mockRecommendations];
  if (country && country !== 'all') {
    filteredRecommendations = mockRecommendations.filter(rec => rec.country === country);
  }
  
  // Simulate some delay for a more realistic API response
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return NextResponse.json(filteredRecommendations);
}

export async function PATCH(request: Request) {
  const data = await request.json();
  const { id, isBookmarked } = data;
  
  // In a real application, you would update the database here
  // For now, we'll just return a success response
  return NextResponse.json({ success: true });
} 