"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  User,
  Bell,
  Lock,
  Settings,
  Globe,
  Database,
  Link,
  Save,
  Upload,
  AlertTriangle,
  Sliders,
  FileText,
  Building,
  Mail,
  Phone,
  ChevronRight,
  CreditCard,
  Download
} from "lucide-react";

// Mock user data
const userData = {
  name: "<PERSON>",
  email: "<EMAIL>",
  role: "Visa Consultant",
  phone: "+91 9876543210",
  profileImage: "/placeholder-user.jpg",
  company: "Visa Mentor Ltd.",
  timezone: "Asia/Kolkata",
  language: "English",
  dateFormat: "DD/MM/YYYY",
  timeFormat: "24h"
};

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("profile");
  
  // Form states
  const [profileData, setProfileData] = useState(userData);
  const [notificationSettings, setNotificationSettings] = useState({
    emailAlerts: true,
    smsAlerts: true,
    applicationUpdates: true,
    marketingEmails: false,
    deadlineReminders: true,
    teamMentions: true,
    documentRequests: true
  });
  
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: "30",
    ipRestriction: false,
    passwordExpiry: "90",
    auditLogEnabled: true
  });
  
  const [systemSettings, setSystemSettings] = useState({
    darkMode: false,
    compactView: false,
    autoRefresh: true,
    defaultView: "dashboard",
    recordsPerPage: "20"
  });

  const handleSaveSettings = (type) => {
    // Mock API call to save settings
    console.log(`Saving ${type} settings...`);
    // Add success toast or notification here
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
        <Button>
          <Download className="h-4 w-4 mr-2" />
          Export Data
        </Button>
      </div>

      <Tabs defaultValue="profile" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 w-full md:w-auto">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">Profile</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Lock className="h-4 w-4" />
            <span className="hidden sm:inline">Security</span>
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">System</span>
          </TabsTrigger>
        </TabsList>

        {/* Profile Settings */}
        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col md:flex-row gap-8">
                <div className="md:w-1/4 flex flex-col items-center space-y-3">
                  <div className="relative">
                    <div className="h-24 w-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                      <User className="h-12 w-12 text-gray-500" />
                    </div>
                    <Button size="sm" className="absolute bottom-0 right-0 rounded-full w-8 h-8 p-0">
                      <Upload className="h-4 w-4" />
                    </Button>
                  </div>
                  <span className="text-sm text-muted-foreground">Upload a new photo</span>
                </div>
                
                <div className="md:w-3/4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input 
                        id="name" 
                        value={profileData.name} 
                        onChange={(e) => setProfileData({...profileData, name: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input 
                        id="email" 
                        type="email" 
                        value={profileData.email} 
                        onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input 
                        id="phone" 
                        value={profileData.phone} 
                        onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Job Role</Label>
                      <Input 
                        id="role" 
                        value={profileData.role} 
                        onChange={(e) => setProfileData({...profileData, role: e.target.value})}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="company">Company</Label>
                    <Input 
                      id="company" 
                      value={profileData.company} 
                      onChange={(e) => setProfileData({...profileData, company: e.target.value})}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Select 
                    value={profileData.language} 
                    onValueChange={(value) => setProfileData({...profileData, language: value})}
                  >
                    <SelectTrigger id="language">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="English">English</SelectItem>
                      <SelectItem value="Hindi">Hindi</SelectItem>
                      <SelectItem value="Tamil">Tamil</SelectItem>
                      <SelectItem value="Telugu">Telugu</SelectItem>
                      <SelectItem value="Bengali">Bengali</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select 
                    value={profileData.timezone} 
                    onValueChange={(value) => setProfileData({...profileData, timezone: value})}
                  >
                    <SelectTrigger id="timezone">
                      <SelectValue placeholder="Select timezone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Asia/Kolkata">India (GMT+5:30)</SelectItem>
                      <SelectItem value="America/New_York">New York (GMT-4)</SelectItem>
                      <SelectItem value="Europe/London">London (GMT+1)</SelectItem>
                      <SelectItem value="Australia/Sydney">Sydney (GMT+10)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="dateFormat">Date Format</Label>
                  <Select 
                    value={profileData.dateFormat} 
                    onValueChange={(value) => setProfileData({...profileData, dateFormat: value})}
                  >
                    <SelectTrigger id="dateFormat">
                      <SelectValue placeholder="Select date format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                      <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                      <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeFormat">Time Format</Label>
                  <Select 
                    value={profileData.timeFormat} 
                    onValueChange={(value) => setProfileData({...profileData, timeFormat: value})}
                  >
                    <SelectTrigger id="timeFormat">
                      <SelectValue placeholder="Select time format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="12h">12 Hour</SelectItem>
                      <SelectItem value="24h">24 Hour</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex justify-end mt-4">
                <Button onClick={() => handleSaveSettings('profile')}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Notification Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Email Alerts</h4>
                    <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.emailAlerts} 
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, emailAlerts: checked})}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">SMS Alerts</h4>
                    <p className="text-sm text-muted-foreground">Receive important notifications via SMS</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.smsAlerts} 
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, smsAlerts: checked})} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Application Updates</h4>
                    <p className="text-sm text-muted-foreground">Updates about visa application status changes</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.applicationUpdates} 
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, applicationUpdates: checked})} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Marketing Emails</h4>
                    <p className="text-sm text-muted-foreground">Receive promotional offers and news</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.marketingEmails} 
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, marketingEmails: checked})} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Deadline Reminders</h4>
                    <p className="text-sm text-muted-foreground">Reminders for upcoming deadlines</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.deadlineReminders} 
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, deadlineReminders: checked})} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Team Mentions</h4>
                    <p className="text-sm text-muted-foreground">When you are mentioned in comments</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.teamMentions} 
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, teamMentions: checked})} 
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Document Requests</h4>
                    <p className="text-sm text-muted-foreground">When clients upload or request documents</p>
                  </div>
                  <Switch 
                    checked={notificationSettings.documentRequests} 
                    onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, documentRequests: checked})} 
                  />
                </div>
              </div>
              
              <div className="flex justify-end mt-4">
                <Button onClick={() => handleSaveSettings('notifications')}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Security Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Two-Factor Authentication</h4>
                    <p className="text-sm text-muted-foreground">Add an extra layer of security to your account</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch 
                      checked={securitySettings.twoFactorAuth} 
                      onCheckedChange={(checked) => setSecuritySettings({...securitySettings, twoFactorAuth: checked})} 
                    />
                    {!securitySettings.twoFactorAuth && (
                      <Button variant="outline" size="sm">Setup</Button>
                    )}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                  <Select 
                    value={securitySettings.sessionTimeout} 
                    onValueChange={(value) => setSecuritySettings({...securitySettings, sessionTimeout: value})}
                  >
                    <SelectTrigger id="session-timeout">
                      <SelectValue placeholder="Select timeout period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">60 minutes</SelectItem>
                      <SelectItem value="120">120 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">IP Restriction</h4>
                    <p className="text-sm text-muted-foreground">Limit access to specific IP addresses</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch 
                      checked={securitySettings.ipRestriction} 
                      onCheckedChange={(checked) => setSecuritySettings({...securitySettings, ipRestriction: checked})} 
                    />
                    {securitySettings.ipRestriction && (
                      <Button variant="outline" size="sm">Configure</Button>
                    )}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="password-expiry">Password Expiry (days)</Label>
                  <Select 
                    value={securitySettings.passwordExpiry} 
                    onValueChange={(value) => setSecuritySettings({...securitySettings, passwordExpiry: value})}
                  >
                    <SelectTrigger id="password-expiry">
                      <SelectValue placeholder="Select expiry period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30">30 days</SelectItem>
                      <SelectItem value="60">60 days</SelectItem>
                      <SelectItem value="90">90 days</SelectItem>
                      <SelectItem value="never">Never</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Audit Logging</h4>
                    <p className="text-sm text-muted-foreground">Track all actions taken in the system</p>
                  </div>
                  <Switch 
                    checked={securitySettings.auditLogEnabled} 
                    onCheckedChange={(checked) => setSecuritySettings({...securitySettings, auditLogEnabled: checked})} 
                  />
                </div>
                
                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full text-red-600 border-red-200 hover:bg-red-50">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Change Password
                  </Button>
                </div>
              </div>
              
              <div className="flex justify-end mt-4">
                <Button onClick={() => handleSaveSettings('security')}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Login History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { device: "Chrome / Windows", ip: "*************", date: "Today, 9:41 AM", status: "Current" },
                  { device: "Safari / macOS", ip: "*************", date: "Yesterday, 6:30 PM", status: "Success" },
                  { device: "Mobile App / iOS", ip: "*************", date: "Jul 22, 2023, 8:15 AM", status: "Success" },
                  { device: "Firefox / Ubuntu", ip: "*************", date: "Jul 20, 2023, 11:30 AM", status: "Failed" }
                ].map((login, i) => (
                  <div key={i} className="flex justify-between items-center py-2 border-b last:border-b-0">
                    <div>
                      <div className="font-medium">{login.device}</div>
                      <div className="text-sm text-muted-foreground">IP: {login.ip}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm">{login.date}</div>
                      <Badge variant={login.status === "Failed" ? "destructive" : (login.status === "Current" ? "outline" : "secondary")}>
                        {login.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Settings */}
        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Appearance & Behavior</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Dark Mode</h4>
                    <p className="text-sm text-muted-foreground">Enable dark theme for the interface</p>
                  </div>
                  <Switch 
                    checked={systemSettings.darkMode} 
                    onCheckedChange={(checked) => setSystemSettings({...systemSettings, darkMode: checked})} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Compact View</h4>
                    <p className="text-sm text-muted-foreground">Reduce spacing to show more content</p>
                  </div>
                  <Switch 
                    checked={systemSettings.compactView} 
                    onCheckedChange={(checked) => setSystemSettings({...systemSettings, compactView: checked})} 
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Auto Refresh</h4>
                    <p className="text-sm text-muted-foreground">Automatically refresh data every 5 minutes</p>
                  </div>
                  <Switch 
                    checked={systemSettings.autoRefresh} 
                    onCheckedChange={(checked) => setSystemSettings({...systemSettings, autoRefresh: checked})} 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="default-view">Default View</Label>
                  <Select 
                    value={systemSettings.defaultView} 
                    onValueChange={(value) => setSystemSettings({...systemSettings, defaultView: value})}
                  >
                    <SelectTrigger id="default-view">
                      <SelectValue placeholder="Select default view" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dashboard">Dashboard</SelectItem>
                      <SelectItem value="clients">Clients</SelectItem>
                      <SelectItem value="lifecycle">Lifecycle</SelectItem>
                      <SelectItem value="engagement">Engagement</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="records-per-page">Records Per Page</Label>
                  <Select 
                    value={systemSettings.recordsPerPage} 
                    onValueChange={(value) => setSystemSettings({...systemSettings, recordsPerPage: value})}
                  >
                    <SelectTrigger id="records-per-page">
                      <SelectValue placeholder="Select records per page" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10 records</SelectItem>
                      <SelectItem value="20">20 records</SelectItem>
                      <SelectItem value="50">50 records</SelectItem>
                      <SelectItem value="100">100 records</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex justify-end mt-4">
                <Button onClick={() => handleSaveSettings('system')}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">System Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">Version</p>
                    <p className="font-medium">Visa Mentor CRM 1.0.5</p>
                  </div>
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">Last Updated</p>
                    <p className="font-medium">July 25, 2023</p>
                  </div>
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">Database</p>
                    <p className="font-medium">MySQL 8.0.27</p>
                  </div>
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">Storage Used</p>
                    <p className="font-medium">1.2 GB / 5 GB</p>
                  </div>
                </div>
                
                <div className="pt-4">
                  <h4 className="font-medium mb-2">Third-Party Integrations</h4>
                  <div className="space-y-2">
                    {[
                      { name: "Email Service", status: "Connected", lastSync: "2 hours ago" },
                      { name: "Calendar", status: "Connected", lastSync: "1 day ago" },
                      { name: "Document Storage", status: "Connected", lastSync: "3 hours ago" },
                      { name: "Analytics Platform", status: "Disconnected", lastSync: "Never" }
                    ].map((integration, i) => (
                      <div key={i} className="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-md">
                        <span>{integration.name}</span>
                        <div className="flex items-center">
                          <Badge variant={integration.status === "Connected" ? "default" : "outline"} className="mr-2">
                            {integration.status}
                          </Badge>
                          <Button variant="ghost" size="sm">
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 