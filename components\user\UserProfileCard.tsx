"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { UserCircle, Mail, Phone, MapPin, Star, Edit, Save, X, Check, AlertCircle } from "lucide-react"
import { useUser } from "@clerk/nextjs"

interface UserData {
  name: string
  email: string
  phone: string
  location: string
  memberSince: string
  membershipTier: string
  profilePicture?: string
}

interface UserProfileCardProps {
  userData?: UserData
  loading?: boolean
}

export default function UserProfileCard({ userData: initialUserData, loading: initialLoading = false }: UserProfileCardProps) {
  const [loading, setLoading] = useState(initialLoading)
  const [userData, setUserData] = useState<UserData | null>(initialUserData || null)
  const [error, setError] = useState<string | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState<Partial<UserData>>({})
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const { user: clerkUser } = useUser()
  
  useEffect(() => {
    if (!initialUserData && !initialLoading) {
      const fetchUserData = async () => {
        // If we're not authenticated yet, don't make the API call
        if (!clerkUser) {
          console.log("Not authenticated, using guest profile")
          setUserData({
            name: "Guest User",
            email: "<EMAIL>",
            phone: "Not available",
            location: "Not specified",
            memberSince: "Just now",
            membershipTier: "Basic",
            profilePicture: "",
          })
          setLoading(false)
          return
        }
        
        // Only proceed with the API call if we're authenticated
        if (clerkUser) {
        setLoading(true)
        try {
            console.log("Clerk user:", clerkUser)
            
            const response = await fetch('/api/user', {
              credentials: 'include' // Include cookies for auth
            })
            
            console.log("API response status:", response.status)
            
          if (!response.ok) {
            throw new Error('Failed to fetch user data')
          }
          const data = await response.json()
          setUserData(data)
        } catch (err: any) {
          setError(err.message || 'An error occurred')
          console.error('Error fetching user data:', err)
        } finally {
          setLoading(false)
          }
        }
      }

      fetchUserData()
    }
  }, [initialUserData, initialLoading, clerkUser])

  // Initialize form data when entering edit mode
  useEffect(() => {
    if (isEditing && userData) {
      setFormData({
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        location: userData.location,
      })
    }
  }, [isEditing, userData])

  // Clear success message after a delay
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null)
      }, 3000) // Hide after 3 seconds
      
      return () => clearTimeout(timer)
    }
  }, [successMessage])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!userData || !clerkUser) return
    
    try {
      setLoading(true)
      const response = await fetch('/api/user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include', // Include cookies for auth
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) {
        throw new Error('Failed to update profile')
      }
      
      const updatedData = await response.json()
      setUserData(updatedData)
      setIsEditing(false)
      setSuccessMessage('Profile updated successfully!')
    } catch (err: any) {
      console.error('Error updating profile:', err)
      setError('Failed to update profile. Please try again.')
      // Error message will clear when user tries again
      setTimeout(() => setError(null), 3000)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setFormData({})
  }

  if (loading) {
    return (
      <div
        className="rounded-2xl p-6 shadow-xl backdrop-blur-lg w-full h-full animate-pulse"
        style={{
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(12px)",
          border: "1px solid rgba(255, 255, 255, 0.25)",
          boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
        }}
      >
        <div className="flex flex-col items-center">
          <div className="w-24 h-24 rounded-full bg-indigo-200 mb-5"></div>
          <div className="h-6 bg-indigo-200 rounded-lg w-40 mb-3"></div>
          <div className="h-4 bg-indigo-100 rounded-lg w-32 mb-6"></div>
          <div className="space-y-4 w-full">
            <div className="h-4 bg-indigo-100 rounded-lg w-full"></div>
            <div className="h-4 bg-indigo-100 rounded-lg w-full"></div>
            <div className="h-4 bg-indigo-100 rounded-lg w-full"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error && clerkUser) {
    return (
      <div
        className="rounded-2xl p-6 shadow-xl backdrop-blur-lg w-full h-full"
        style={{
          background: "rgba(255, 255, 255, 0.8)",
          backdropFilter: "blur(12px)",
          border: "1px solid rgba(255, 255, 255, 0.25)",
          boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
        }}
      >
        <div className="flex flex-col items-center justify-center h-full">
          <AlertCircle className="h-10 w-10 text-red-500 mb-3" />
          <p className="text-red-600 text-center font-medium mb-2">Failed to load user profile.</p>
          <Button 
            onClick={() => window.location.reload()} 
            variant="outline" 
            className="mt-2 text-indigo-600 border-indigo-300 hover:bg-indigo-50"
            size="sm"
          >
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  // Use fallback data if userData is still null
  const displayUser = userData || {
    name: "Guest User",
    email: "<EMAIL>",
    phone: "Not available",
    location: "Not specified",
    memberSince: "Just now",
    membershipTier: "Basic",
    profilePicture: "",
  }

  return (
    <div
      className="rounded-2xl p-6 shadow-xl backdrop-blur-lg w-full h-full"
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {successMessage && (
        <div className="bg-green-50 border-l-4 border-green-500 p-3 mb-4 rounded-lg shadow-sm">
          <div className="flex items-center">
            <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
            <p className="text-green-800 text-sm font-medium">{successMessage}</p>
          </div>
        </div>
      )}

      {error && !loading && (
        <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4 rounded-lg shadow-sm">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0" />
            <p className="text-red-800 text-sm font-medium">{error}</p>
          </div>
        </div>
      )}

      <div className="flex justify-end mb-2">
        {!isEditing ? (
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => setIsEditing(true)}
            className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 rounded-full"
          >
            <Edit className="h-4 w-4" />
            <span className="sr-only">Edit Profile</span>
          </Button>
        ) : (
          <div className="flex space-x-1">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={handleCancel}
              className="text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Cancel</span>
            </Button>
            <Button 
              variant="ghost" 
              size="icon"
              onClick={handleSubmit}
              className="text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full"
            >
              <Check className="h-4 w-4" />
              <span className="sr-only">Save</span>
            </Button>
          </div>
        )}
      </div>

      <div className="flex flex-col items-center">
        {displayUser.profilePicture ? (
          <div className="relative">
            <Image
              src={displayUser.profilePicture}
              alt={displayUser.name}
              width={96} 
              height={96} 
              className="rounded-full object-cover border-4 border-white shadow-md"
            />
            <div className="absolute -bottom-1 -right-1 bg-indigo-100 p-1 rounded-full border-2 border-white">
              <Star className="w-4 h-4 text-indigo-600" />
            </div>
          </div>
        ) : (
          <div className="relative">
            <div className="w-24 h-24 rounded-full bg-gradient-to-r from-blue-600 to-indigo-700 flex items-center justify-center shadow-md">
              <UserCircle className="w-16 h-16 text-white" />
            </div>
            <div className="absolute -bottom-1 -right-1 bg-indigo-100 p-1 rounded-full border-2 border-white">
              <Star className="w-4 h-4 text-indigo-600" />
            </div>
          </div>
        )}

        {!isEditing ? (
          <>
            <h2 className="text-xl font-bold text-indigo-800 mt-4 mb-1">{displayUser.name}</h2>
            <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white text-xs font-medium px-3 py-1 rounded-full flex items-center mb-5">
              <Star className="w-3 h-3 mr-1" />
              {displayUser.membershipTier} Member
            </div>
            
            <div className="w-full space-y-4">
              <div className="flex items-center text-sm bg-indigo-50/80 p-3 rounded-lg transform transition-all duration-200 hover:bg-indigo-100/80">
                <Mail className="w-5 h-5 text-indigo-600 mr-3 flex-shrink-0" />
                <span className="text-gray-700">{displayUser.email}</span>
              </div>
              <div className="flex items-center text-sm bg-indigo-50/80 p-3 rounded-lg transform transition-all duration-200 hover:bg-indigo-100/80">
                <Phone className="w-5 h-5 text-indigo-600 mr-3 flex-shrink-0" />
                <span className="text-gray-700">{displayUser.phone}</span>
              </div>
              <div className="flex items-center text-sm bg-indigo-50/80 p-3 rounded-lg transform transition-all duration-200 hover:bg-indigo-100/80">
                <MapPin className="w-5 h-5 text-indigo-600 mr-3 flex-shrink-0" />
                <span className="text-gray-700">{displayUser.location}</span>
              </div>
              <div className="flex items-center justify-center text-sm pt-3 mt-3 border-t border-indigo-100">
                <span className="text-indigo-500 text-xs bg-indigo-50 px-3 py-1 rounded-full">Member since {displayUser.memberSince}</span>
              </div>
            </div>
          </>
        ) : (
          <form onSubmit={handleSubmit} className="w-full mt-4">
            <div className="mb-4">
              <label htmlFor="name" className="block text-xs font-medium text-indigo-700 mb-1">Full Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name || ''}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-indigo-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm bg-white/70"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="email" className="block text-xs font-medium text-indigo-700 mb-1">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email || ''}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-indigo-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm bg-white/70"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="phone" className="block text-xs font-medium text-indigo-700 mb-1">Phone Number</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone || ''}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-indigo-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm bg-white/70"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="location" className="block text-xs font-medium text-indigo-700 mb-1">Location</label>
              <input
                type="text"
                id="location"
                name="location"
                value={formData.location || ''}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-indigo-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm bg-white/70"
              />
            </div>

            <div className="pt-3 border-t border-indigo-100">
              <div className="flex justify-between items-center">
                <span className="text-indigo-500 text-xs bg-indigo-50 px-2 py-1 rounded-full">Member since {displayUser.memberSince}</span>
                <div className="text-xs font-medium px-2 py-1 rounded-full bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
                  {displayUser.membershipTier}
                </div>
              </div>
            </div>
          </form>
        )}
      </div>
    </div>
  )
} 