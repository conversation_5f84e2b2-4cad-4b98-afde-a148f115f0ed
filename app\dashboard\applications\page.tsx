"use client";

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { ArrowRight, Search, Filter } from "lucide-react"
import GlassCard from "@/components/GlassCard"

export default function ApplicationsPage() {
  const applications = [
    {
      id: 1,
      type: "Student Visa - USA",
      status: "In Progress",
      progress: 65,
      lastUpdated: "2 days ago",
      startDate: "April 10, 2025",
    },
    {
      id: 2,
      type: "University Application - Stanford",
      status: "Under Review",
      progress: 80,
      lastUpdated: "1 week ago",
      startDate: "March 15, 2025",
    },
    {
      id: 3,
      type: "Scholarship Application - Fulbright",
      status: "Submitted",
      progress: 100,
      lastUpdated: "2 weeks ago",
      startDate: "February 20, 2025",
    },
    {
      id: 4,
      type: "Work Visa - Canada",
      status: "Approved",
      progress: 100,
      lastUpdated: "1 month ago",
      startDate: "January 5, 2025",
    },
    {
      id: 5,
      type: "University Application - MIT",
      status: "Rejected",
      progress: 100,
      lastUpdated: "2 months ago",
      startDate: "December 10, 2024",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">Applications</h1>
          <p className="text-gray-600 mt-1">Manage and track all your visa and university applications.</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search applications..."
              className="w-[200px] pl-8 bg-white/80 backdrop-blur-sm border border-indigo-200/60 focus:border-indigo-400 rounded-lg"
            />
          </div>
          <Button 
            className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-2 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md"
            asChild
          >
            <Link href={"/dashboard/applications/new" as any}>Start New Application</Link>
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input 
            type="search" 
            placeholder="Search applications..." 
            className="pl-8 bg-white/80 backdrop-blur-sm border border-indigo-200/60 focus:border-indigo-400 rounded-lg" 
          />
        </div>
        <div className="flex gap-2">
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px] bg-white/80 backdrop-blur-sm border border-indigo-200/60 focus:border-indigo-400 rounded-lg">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent className="bg-white/90 backdrop-blur-sm border border-indigo-200/60">
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="under-review">Under Review</SelectItem>
              <SelectItem value="submitted">Submitted</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="newest">
            <SelectTrigger className="w-[180px] bg-white/80 backdrop-blur-sm border border-indigo-200/60 focus:border-indigo-400 rounded-lg">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent className="bg-white/90 backdrop-blur-sm border border-indigo-200/60">
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="status">Status</SelectItem>
              <SelectItem value="progress">Progress</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            size="icon" 
            className="border border-indigo-200/60 bg-white/80 backdrop-blur-sm text-indigo-600 hover:bg-indigo-50/70 rounded-lg"
          >
            <Filter className="h-4 w-4" />
            <span className="sr-only">Filter</span>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="bg-white/70 backdrop-blur-sm border border-indigo-100/30 p-1">
          <TabsTrigger 
            value="all" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
          >
            All
          </TabsTrigger>
          <TabsTrigger 
            value="active"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
          >
            Active
          </TabsTrigger>
          <TabsTrigger 
            value="completed"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-100/50 data-[state=active]:to-blue-100/50 data-[state=active]:text-indigo-800 rounded-lg"
          >
            Completed
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <div className="grid gap-4">
            {applications.map((app) => (
              <GlassCard key={app.id} className="transform transition hover:translate-y-[-2px]">
                <div className="p-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-semibold text-indigo-800">{app.type}</h3>
                      <p className="text-sm text-gray-600">Started: {app.startDate}</p>
                    </div>
                    <div
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        app.status === "Approved"
                          ? "bg-green-100/70 backdrop-blur-sm text-green-800"
                          : app.status === "Rejected"
                            ? "bg-red-100/70 backdrop-blur-sm text-red-800"
                            : app.status === "In Progress"
                              ? "bg-blue-100/70 backdrop-blur-sm text-blue-800"
                              : app.status === "Under Review"
                                ? "bg-amber-100/70 backdrop-blur-sm text-amber-800"
                                : "bg-gray-100/70 backdrop-blur-sm text-gray-800"
                      }`}
                    >
                      {app.status}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2 mt-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Progress</span>
                      <span className="text-indigo-800 font-medium">{app.progress}%</span>
                    </div>
                    <div className="h-2 bg-indigo-100/40 rounded-full overflow-hidden">
                      <div 
                        className={`h-full rounded-full ${
                          app.status === "Approved" 
                            ? "bg-gradient-to-r from-green-500 to-emerald-400" 
                            : app.status === "Rejected"
                              ? "bg-gradient-to-r from-red-500 to-rose-400"
                              : "bg-gradient-to-r from-indigo-600 to-blue-400"
                        }`}
                        style={{ width: `${app.progress}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between items-center mt-2">
                      <div className="text-xs text-gray-600">Last updated: {app.lastUpdated}</div>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50/70"
                        asChild
                      >
                        <Link href={`/dashboard/applications/${app.id}` as any}>
                          View Details <ArrowRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </GlassCard>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="active" className="space-y-4">
          <div className="grid gap-4">
            {applications
              .filter((app) => ["In Progress", "Under Review", "Submitted"].includes(app.status))
              .map((app) => (
                <GlassCard key={app.id} className="transform transition hover:translate-y-[-2px]">
                  <div className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-semibold text-indigo-800">{app.type}</h3>
                        <p className="text-sm text-gray-600">Started: {app.startDate}</p>
                      </div>
                      <div
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          app.status === "In Progress"
                            ? "bg-blue-100/70 backdrop-blur-sm text-blue-800"
                            : app.status === "Under Review"
                              ? "bg-amber-100/70 backdrop-blur-sm text-amber-800"
                              : "bg-gray-100/70 backdrop-blur-sm text-gray-800"
                        }`}
                      >
                        {app.status}
                      </div>
                    </div>
                    <div className="flex flex-col gap-2 mt-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Progress</span>
                        <span className="text-indigo-800 font-medium">{app.progress}%</span>
                      </div>
                      <div className="h-2 bg-indigo-100/40 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-indigo-600 to-blue-400 rounded-full"
                          style={{ width: `${app.progress}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <div className="text-xs text-gray-600">Last updated: {app.lastUpdated}</div>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50/70"
                          asChild
                        >
                          <Link href={`/dashboard/applications/${app.id}` as any}>
                            View Details <ArrowRight className="ml-1 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </GlassCard>
              ))}
          </div>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <div className="grid gap-4">
            {applications
              .filter((app) => ["Approved", "Rejected"].includes(app.status))
              .map((app) => (
                <GlassCard key={app.id} className="transform transition hover:translate-y-[-2px]">
                  <div className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-semibold text-indigo-800">{app.type}</h3>
                        <p className="text-sm text-gray-600">Started: {app.startDate}</p>
                      </div>
                      <div
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          app.status === "Approved" ? "bg-green-100/70 backdrop-blur-sm text-green-800" : "bg-red-100/70 backdrop-blur-sm text-red-800"
                        }`}
                      >
                        {app.status}
                      </div>
                    </div>
                    <div className="flex flex-col gap-2 mt-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Progress</span>
                        <span className="text-indigo-800 font-medium">{app.progress}%</span>
                      </div>
                      <div className="h-2 bg-indigo-100/40 rounded-full overflow-hidden">
                        <div 
                          className={`h-full rounded-full ${
                            app.status === "Approved" 
                              ? "bg-gradient-to-r from-green-500 to-emerald-400" 
                              : "bg-gradient-to-r from-red-500 to-rose-400"
                          }`}
                          style={{ width: `${app.progress}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <div className="text-xs text-gray-600">Last updated: {app.lastUpdated}</div>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50/70"
                          asChild
                        >
                          <Link href={`/dashboard/applications/${app.id}` as any}>
                            View Details <ArrowRight className="ml-1 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </GlassCard>
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
