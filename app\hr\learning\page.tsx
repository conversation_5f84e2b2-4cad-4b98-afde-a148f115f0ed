"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  BookOpen,
  FileText,
  Calendar,
  CheckCircle,
  Award,
  Search,
  Filter,
  Plus,
  ArrowRight,
  Clock,
  Star,
  StarHalf,
  ChevronRight,
  Users,
  Layers,
  GraduationCap,
  Compass,
  BarChart
} from "lucide-react";

// Mock data for learning dashboard
const learningMetrics = {
  completedCourses: 48,
  inProgressCourses: 12,
  certifications: 16,
  skillsGap: 7,
  avgCompletionRate: 87,
  topPerformer: "Visa Processing Team"
};

// Mock training courses
const trainingCourses = [
  {
    id: 1,
    title: "Visa Documentation Mastery",
    category: "Immigration",
    duration: "4 hours",
    enrolled: 43,
    rating: 4.8,
    status: "Available"
  },
  {
    id: 2,
    title: "Customer Service Excellence",
    category: "Soft Skills",
    duration: "6 hours",
    enrolled: 78,
    rating: 4.7,
    status: "Available"
  },
  {
    id: 3,
    title: "Travel Regulations Update 2025",
    category: "Compliance",
    duration: "2 hours",
    enrolled: 62,
    rating: 4.6,
    status: "Available"
  },
  {
    id: 4,
    title: "Cultural Sensitivity Training",
    category: "Diversity",
    duration: "3 hours",
    enrolled: 56,
    rating: 4.9,
    status: "Available"
  },
  {
    id: 5,
    title: "Immigration Law Fundamentals",
    category: "Legal",
    duration: "8 hours",
    enrolled: 35,
    rating: 4.5,
    status: "Available"
  }
];

// Mock learning pathways
const learningPathways = [
  {
    id: 1,
    name: "Visa Processing Specialist",
    courses: 5,
    duration: "24 hours",
    completion: 80,
    level: "Intermediate"
  },
  {
    id: 2,
    name: "Immigration Consultant",
    courses: 8,
    duration: "40 hours",
    completion: 45,
    level: "Advanced"
  },
  {
    id: 3,
    name: "Customer Relations Manager",
    courses: 6,
    duration: "30 hours",
    completion: 65,
    level: "Intermediate"
  },
  {
    id: 4,
    name: "Visa Application Reviewer",
    courses: 4,
    duration: "18 hours",
    completion: 100,
    level: "Beginner"
  }
];

// Mock employee training progress
const employeeProgress = [
  {
    id: 1,
    name: "Jane Smith",
    department: "Visa Processing",
    coursesCompleted: 7,
    currentCourse: "Advanced Immigration Law",
    progress: 68,
    certifications: 2
  },
  {
    id: 2,
    name: "Michael Wong",
    department: "Customer Relations",
    coursesCompleted: 5,
    currentCourse: "Conflict Resolution",
    progress: 45,
    certifications: 3
  },
  {
    id: 3,
    name: "Sophia Garcia",
    department: "Legal",
    coursesCompleted: 12,
    currentCourse: "International Travel Regulations",
    progress: 92,
    certifications: 5
  },
  {
    id: 4,
    name: "James Johnson",
    department: "Advisory",
    coursesCompleted: 4,
    currentCourse: "Cultural Competency",
    progress: 30,
    certifications: 1
  }
];

// Mock certifications
const certifications = [
  {
    id: 1,
    name: "Certified Immigration Specialist",
    provider: "International Immigration Board",
    employees: 16,
    expires: "Annual Renewal",
    priority: "High"
  },
  {
    id: 2,
    name: "Travel Documentation Expert",
    provider: "Global Travel Association",
    employees: 24,
    expires: "2 Years",
    priority: "Medium"
  },
  {
    id: 3,
    name: "Customer Service Professional",
    provider: "Customer Excellence Institute",
    employees: 42,
    expires: "3 Years",
    priority: "Medium"
  },
  {
    id: 4,
    name: "Data Privacy Compliance",
    provider: "International Data Protection Authority",
    employees: 35,
    expires: "Annual Renewal",
    priority: "High"
  }
];

export default function LearningPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchQuery, setSearchQuery] = useState("");

  const getLevelBadge = (level: string) => {
    switch (level) {
      case "Beginner":
        return <Badge className="bg-green-500">Beginner</Badge>;
      case "Intermediate":
        return <Badge className="bg-blue-500">Intermediate</Badge>;
      case "Advanced":
        return <Badge className="bg-purple-500">Advanced</Badge>;
      default:
        return <Badge variant="outline">{level}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "High":
        return <Badge className="bg-red-500">High</Badge>;
      case "Medium":
        return <Badge className="bg-amber-500">Medium</Badge>;
      case "Low":
        return <Badge className="bg-green-500">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Skill Development</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <BarChart className="mr-2 h-4 w-4" />
            Learning Reports
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Course
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="courses">Training Catalog</TabsTrigger>
          <TabsTrigger value="pathways">Learning Pathways</TabsTrigger>
          <TabsTrigger value="certifications">Certifications</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key learning metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completed Courses</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{learningMetrics.completedCourses}</div>
                <p className="text-xs text-muted-foreground mt-2">
                  +8 from last quarter
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Enrollments</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{learningMetrics.inProgressCourses}</div>
                <p className="text-xs text-muted-foreground mt-2">
                  Across 4 departments
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Certifications</CardTitle>
                <Award className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{learningMetrics.certifications}</div>
                <p className="text-xs text-muted-foreground mt-2">
                  3 expiring this month
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Additional metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Skills Gap Areas</CardTitle>
                <Compass className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{learningMetrics.skillsGap}</div>
                <p className="text-xs text-muted-foreground mt-2">
                  Priority development areas
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
                <BarChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{learningMetrics.avgCompletionRate}%</div>
                <Progress value={learningMetrics.avgCompletionRate} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  +5% from last quarter
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Top Performing Team</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{learningMetrics.topPerformer}</div>
                <p className="text-xs text-muted-foreground mt-2">
                  96% course completion rate
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Employee learning progress */}
          <Card>
            <CardHeader>
              <CardTitle>Employee Learning Progress</CardTitle>
              <CardDescription>Top performers and ongoing training</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Current Course</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Completed</TableHead>
                    <TableHead>Certifications</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {employeeProgress.map((employee) => (
                    <TableRow key={employee.id}>
                      <TableCell className="font-medium">{employee.name}</TableCell>
                      <TableCell>{employee.department}</TableCell>
                      <TableCell>{employee.currentCourse}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress value={employee.progress} className="w-[80px]" />
                          <span className="text-xs">{employee.progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>{employee.coursesCompleted}</TableCell>
                      <TableCell>{employee.certifications}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="courses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Training Catalog</CardTitle>
              <CardDescription>Browse available courses and training modules</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between space-y-2 mb-4">
                <div className="flex w-full max-w-sm items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input 
                    placeholder="Search courses..." 
                    className="flex-1"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    New Course
                  </Button>
                </div>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Course Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Enrolled</TableHead>
                    <TableHead>Rating</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {trainingCourses.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell className="font-medium">{course.title}</TableCell>
                      <TableCell>{course.category}</TableCell>
                      <TableCell>{course.duration}</TableCell>
                      <TableCell>{course.enrolled}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-500 fill-yellow-500" />
                          <span className="ml-1">{course.rating}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pathways" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Learning Pathways</CardTitle>
              <CardDescription>Structured educational tracks for career development</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                {learningPathways.map((pathway) => (
                  <Card key={pathway.id} className="border border-gray-200">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">{pathway.name}</CardTitle>
                        {getLevelBadge(pathway.level)}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <p className="text-muted-foreground">Courses</p>
                            <p className="font-medium">{pathway.courses}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Duration</p>
                            <p className="font-medium">{pathway.duration}</p>
                          </div>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>Completion</span>
                            <span>{pathway.completion}%</span>
                          </div>
                          <Progress value={pathway.completion} />
                        </div>
                        
                        <Button variant="outline" className="w-full">
                          <GraduationCap className="mr-2 h-4 w-4" />
                          View Pathway
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="certifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Certification Management</CardTitle>
              <CardDescription>Track and manage employee certifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between mb-4">
                <div className="flex w-full max-w-sm items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input 
                    placeholder="Search certifications..." 
                    className="flex-1"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Certification
                </Button>
              </div>
              
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Certification Name</TableHead>
                    <TableHead>Provider</TableHead>
                    <TableHead>Certified Employees</TableHead>
                    <TableHead>Expiry Period</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {certifications.map((cert) => (
                    <TableRow key={cert.id}>
                      <TableCell className="font-medium">{cert.name}</TableCell>
                      <TableCell>{cert.provider}</TableCell>
                      <TableCell>{cert.employees}</TableCell>
                      <TableCell>{cert.expires}</TableCell>
                      <TableCell>{getPriorityBadge(cert.priority)}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 