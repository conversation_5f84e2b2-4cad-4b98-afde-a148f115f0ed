"use client";

import { AlertTriangle } from "lucide-react";

interface Executive {
  id: string;
  name: string;
  workload: number;
}

interface WorkloadThresholds {
  optimal: number;
  warning: number;
  critical: number;
}

interface WorkloadHeatmapProps {
  executives: Executive[];
  thresholds: WorkloadThresholds;
  overrideTools?: boolean;
}

export function WorkloadHeatmap({ executives, thresholds, overrideTools = false }: WorkloadHeatmapProps) {
  const getWorkloadCategory = (workload: number) => {
    if (workload >= thresholds.critical) return 'critical';
    if (workload >= thresholds.warning) return 'warning';
    if (workload >= thresholds.optimal * 0.5) return 'optimal';
    return 'low';
  };
  
  const getWorkloadStatusText = (workload: number) => {
    if (workload >= thresholds.critical) return 'Overloaded';
    if (workload >= thresholds.warning) return 'Heavy';
    if (workload >= thresholds.optimal * 0.5) return 'Balanced';
    return 'Light';
  };

  const getColorForWorkload = (workload: number) => {
    const category = getWorkloadCategory(workload);
    switch (category) {
      case 'critical': return 'bg-red-500';
      case 'warning': return 'bg-amber-500';
      case 'optimal': return 'bg-green-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-300';
    }
  };
  
  const getTextColorForWorkload = (workload: number) => {
    const category = getWorkloadCategory(workload);
    switch (category) {
      case 'critical': return 'text-red-600';
      case 'warning': return 'text-amber-600';
      case 'optimal': return 'text-green-600';
      case 'low': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };
  
  const getBgColorForWorkload = (workload: number) => {
    const category = getWorkloadCategory(workload);
    switch (category) {
      case 'critical': return 'bg-red-50';
      case 'warning': return 'bg-amber-50';
      case 'optimal': return 'bg-green-50';
      case 'low': return 'bg-blue-50';
      default: return 'bg-gray-50';
    }
  };
  
  // Calculate averages and max workload
  const averageWorkload = executives.reduce((sum, exec) => sum + exec.workload, 0) / executives.length;
  const maxWorkload = Math.max(...executives.map(exec => exec.workload));
  
  // Sort executives by workload (highest to lowest)
  const sortedExecutives = [...executives].sort((a, b) => b.workload - a.workload);
  
  return (
    <div className="space-y-4">
      {/* Summary metrics */}
      <div className="grid grid-cols-3 gap-2 text-center">
        <div className="bg-gray-50 p-2 rounded-lg">
          <p className="text-xs text-gray-500">Average Load</p>
          <p className="text-lg font-semibold text-gray-800">{averageWorkload.toFixed(1)}</p>
          <p className="text-xs text-gray-500">leads per exec</p>
        </div>
        <div className="bg-gray-50 p-2 rounded-lg">
          <p className="text-xs text-gray-500">Team Balance</p>
          <p className={`text-lg font-semibold ${averageWorkload < thresholds.warning ? 'text-green-600' : 'text-amber-600'}`}>
            {averageWorkload < thresholds.optimal ? 'Excellent' :
             averageWorkload < thresholds.warning ? 'Good' :
             averageWorkload < thresholds.critical ? 'At Risk' : 'Critical'}
          </p>
        </div>
        <div className={`p-2 rounded-lg ${averageWorkload >= thresholds.warning ? 'bg-amber-50' : 'bg-gray-50'}`}>
          <p className="text-xs text-gray-500">Action Needed</p>
          <p className={`text-lg font-semibold ${averageWorkload >= thresholds.warning ? 'text-amber-600' : 'text-gray-400'}`}>
            {maxWorkload >= thresholds.critical ? 'Urgent' :
             maxWorkload >= thresholds.warning ? 'Yes' : 'No'}
          </p>
        </div>
      </div>
      
      {/* Heatmap visualization */}
      <div className="space-y-2">
        {sortedExecutives.map((executive) => (
          <div 
            key={executive.id} 
            className={`p-3 rounded-lg border ${getBgColorForWorkload(executive.workload)}`}
          >
            <div className="flex justify-between items-center mb-1">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center font-medium text-sm mr-2">
                  {executive.name.split(' ').map(n => n[0]).join('')}
                </div>
                <span className="font-medium text-gray-800">{executive.name}</span>
              </div>
              <div className="flex items-center">
                <span className={`text-sm font-semibold ${getTextColorForWorkload(executive.workload)}`}>
                  {executive.workload}
                </span>
                <span className="text-xs text-gray-500 ml-1">leads</span>
                {executive.workload >= thresholds.warning && (
                  <AlertTriangle 
                    className={`h-4 w-4 ml-2 ${executive.workload >= thresholds.critical ? 'text-red-500' : 'text-amber-500'}`}
                  />
                )}
              </div>
            </div>
            
            <div className="w-full bg-gray-100 rounded-full h-2 overflow-hidden">
              <div 
                className={`h-full rounded-full ${getColorForWorkload(executive.workload)}`} 
                style={{ width: `${(executive.workload / thresholds.critical) * 100}%` }}
              ></div>
            </div>
            
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-500">
                Status: <span className={`font-medium ${getTextColorForWorkload(executive.workload)}`}>
                  {getWorkloadStatusText(executive.workload)}
                </span>
              </span>
              
              {overrideTools && executive.workload >= thresholds.warning && (
                <button className="text-xs text-indigo-600 font-medium hover:text-indigo-800">
                  Reassign Leads
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {/* Legend */}
      <div className="flex items-center justify-center space-x-4 pt-2 mt-2 border-t border-gray-100">
        <div className="flex items-center">
          <div className="h-3 w-3 rounded-full bg-red-500 mr-1"></div>
          <span className="text-xs text-gray-600">Critical</span>
        </div>
        <div className="flex items-center">
          <div className="h-3 w-3 rounded-full bg-amber-500 mr-1"></div>
          <span className="text-xs text-gray-600">Warning</span>
        </div>
        <div className="flex items-center">
          <div className="h-3 w-3 rounded-full bg-green-500 mr-1"></div>
          <span className="text-xs text-gray-600">Optimal</span>
        </div>
        <div className="flex items-center">
          <div className="h-3 w-3 rounded-full bg-blue-500 mr-1"></div>
          <span className="text-xs text-gray-600">Light</span>
        </div>
      </div>
    </div>
  );
} 