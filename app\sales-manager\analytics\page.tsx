"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Download,
  Filter,
  ChevronDown,
  TrendingUp,
  Users,
  Target,
  DollarSign,
  Star
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock data for charts
const performanceData = {
  revenue: {
    current: 1250000,
    previous: 1050000,
    trend: 19.05,
    monthly: [
      { month: "Jan", value: 850000 },
      { month: "Feb", value: 920000 },
      { month: "Mar", value: 980000 },
      { month: "Apr", value: 1050000 },
      { month: "May", value: 1250000 }
    ]
  },
  conversions: {
    current: 68,
    previous: 62,
    trend: 9.68,
    monthly: [
      { month: "Jan", value: 58 },
      { month: "Feb", value: 60 },
      { month: "Mar", value: 61 },
      { month: "Apr", value: 62 },
      { month: "May", value: 68 }
    ]
  },
  leads: {
    current: 324,
    previous: 287,
    trend: 12.89,
    monthly: [
      { month: "Jan", value: 245 },
      { month: "Feb", value: 256 },
      { month: "Mar", value: 270 },
      { month: "Apr", value: 287 },
      { month: "May", value: 324 }
    ]
  },
  satisfaction: {
    current: 4.7,
    previous: 4.5,
    trend: 4.44,
    monthly: [
      { month: "Jan", value: 4.3 },
      { month: "Feb", value: 4.4 },
      { month: "Mar", value: 4.4 },
      { month: "Apr", value: 4.5 },
      { month: "May", value: 4.7 }
    ]
  }
};

const visaTypeDistribution = [
  { type: "EB-1", value: 28, color: "from-blue-500 to-indigo-600" },
  { type: "O-1", value: 22, color: "from-indigo-500 to-purple-600" },
  { type: "H-1B", value: 32, color: "from-purple-500 to-pink-600" },
  { type: "Student", value: 18, color: "from-blue-400 to-cyan-500" }
];

const teamPerformance = [
  { name: "Priya Sharma", conversions: 42, revenue: 328000, satisfaction: 4.8 },
  { name: "Rahul Singh", conversions: 35, revenue: 275000, satisfaction: 4.5 },
  { name: "Amit Patel", conversions: 38, revenue: 296000, satisfaction: 4.6 },
  { name: "Neha Kumar", conversions: 44, revenue: 351000, satisfaction: 4.9 }
];

const conversionFunnel = [
  { stage: "Leads", count: 324, percentage: 100 },
  { stage: "Consultations", count: 246, percentage: 76 },
  { stage: "Applications", count: 198, percentage: 61 },
  { stage: "Approvals", count: 156, percentage: 48 },
  { stage: "Completed", count: 124, percentage: 38 }
];

export default function AnalyticsPage() {
  const [timeframe, setTimeframe] = useState("This Month");
  const [comparisonPeriod, setComparisonPeriod] = useState("Previous Month");
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(value);
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Performance Analytics
          </h1>
          <p className="text-gray-600 mt-1">Comprehensive view of your sales team's performance metrics</p>
        </div>
        <div className="flex flex-wrap items-center gap-3">
          <div className="relative">
            <select
              className="appearance-none bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 py-2 px-4 pr-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 shadow-sm"
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
            >
              <option>Today</option>
              <option>This Week</option>
              <option>This Month</option>
              <option>This Quarter</option>
              <option>This Year</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <ChevronDown className="h-4 w-4 text-indigo-500" />
            </div>
          </div>
          <button className="flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-indigo-700 py-2 px-4 rounded-lg hover:bg-indigo-50/80 transition-colors shadow-sm">
            <Download className="h-4 w-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Revenue</p>
              <p className="text-2xl font-bold text-indigo-800">{formatCurrency(performanceData.revenue.current)}</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-green-400 to-emerald-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <DollarSign size={24} />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
              <p className="ml-auto text-sm font-medium text-green-600 flex items-center">
                <ArrowUpRight size={16} className="mr-1" />
                {performanceData.revenue.trend}%
              </p>
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-green-500 to-emerald-400 rounded-full" style={{width: `${Math.min(100, performanceData.revenue.trend * 3)}%`}}></div>
            </div>
          </div>
        </GlassCard>

        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Conversion Rate</p>
              <p className="text-2xl font-bold text-indigo-800">{performanceData.conversions.current}%</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-blue-400 to-indigo-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Target size={24} />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
              <p className="ml-auto text-sm font-medium text-green-600 flex items-center">
                <ArrowUpRight size={16} className="mr-1" />
                {performanceData.conversions.trend}%
              </p>
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-500 to-indigo-400 rounded-full" style={{width: `${Math.min(100, performanceData.conversions.trend * 5)}%`}}></div>
            </div>
          </div>
        </GlassCard>

        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Total Leads</p>
              <p className="text-2xl font-bold text-indigo-800">{performanceData.leads.current}</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Users size={24} />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
              <p className="ml-auto text-sm font-medium text-green-600 flex items-center">
                <ArrowUpRight size={16} className="mr-1" />
                {performanceData.leads.trend}%
              </p>
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-indigo-500 to-blue-400 rounded-full" style={{width: `${Math.min(100, performanceData.leads.trend * 4)}%`}}></div>
            </div>
          </div>
        </GlassCard>

        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Client Satisfaction</p>
              <p className="text-2xl font-bold text-indigo-800">{performanceData.satisfaction.current}★</p>
            </div>
            <div className="h-12 w-12 bg-gradient-to-br from-purple-400 to-pink-300 rounded-full flex items-center justify-center text-white shadow-sm">
              <Star size={24} />
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <p className="text-sm text-gray-600">vs. {comparisonPeriod}</p>
              <p className="ml-auto text-sm font-medium text-green-600 flex items-center">
                <ArrowUpRight size={16} className="mr-1" />
                {performanceData.satisfaction.trend}%
              </p>
            </div>
            <div className="mt-1 h-2 w-full bg-indigo-100/40 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-purple-500 to-pink-400 rounded-full" style={{width: `${Math.min(100, performanceData.satisfaction.trend * 10)}%`}}></div>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Main Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <GlassCard className="lg:col-span-2 p-6 transform transition hover:translate-y-[-4px]">
          <div className="flex flex-wrap justify-between items-center mb-6">
            <h2 className="text-lg font-semibold text-indigo-800">Revenue Trend</h2>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <select
                  className="appearance-none bg-white/80 backdrop-blur-sm border border-indigo-200/60 text-sm text-indigo-700 py-1.5 px-3 pr-8 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  defaultValue="revenue"
                >
                  <option value="revenue">Revenue</option>
                  <option value="conversions">Conversions</option>
                  <option value="leads">Leads</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
            </div>
          </div>
          
          {/* Placeholder for Revenue Chart */}
          <div className="h-64 bg-indigo-50/50 backdrop-blur-sm rounded-lg flex items-center justify-center">
            <div className="text-center">
              <LineChart className="h-12 w-12 text-indigo-300 mx-auto mb-2" />
              <p className="text-indigo-600 font-medium">Revenue Chart Visualization</p>
              <p className="text-sm text-indigo-400">Monthly trend data would be rendered here</p>
            </div>
          </div>
          
          <div className="mt-4 grid grid-cols-5 gap-2">
            {performanceData.revenue.monthly.map((item) => (
              <div key={item.month} className="text-center">
                <p className="text-xs text-indigo-600 font-medium">{item.month}</p>
                <p className="text-sm text-indigo-800">{formatCurrency(item.value).replace('₹', '').replace(',000', 'K')}</p>
              </div>
            ))}
          </div>
        </GlassCard>

        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <h2 className="text-lg font-semibold text-indigo-800 mb-6">Visa Type Distribution</h2>
          
          {/* Placeholder for Pie Chart */}
          <div className="h-48 flex items-center justify-center mb-4">
            <PieChart className="h-32 w-32 text-indigo-300" />
          </div>
          
          <div className="space-y-3">
            {visaTypeDistribution.map((item) => (
              <div key={item.type}>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">{item.type}</span>
                  <span className="font-medium text-indigo-800">{item.value}%</span>
                </div>
                <div className="w-full bg-indigo-100/40 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full bg-gradient-to-r ${item.color}`} 
                    style={{ width: `${item.value}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </GlassCard>
      </div>

      {/* Team Performance and Conversion Funnel */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <GlassCard className="lg:col-span-2 p-6 transform transition hover:translate-y-[-4px]">
          <h2 className="text-lg font-semibold text-indigo-800 mb-6">Team Performance</h2>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-indigo-100/30">
              <thead>
                <tr className="bg-indigo-50/30">
                  <th className="px-4 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Team Member</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Conversions</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Revenue</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-indigo-700 uppercase tracking-wider">Satisfaction</th>
                </tr>
              </thead>
              <tbody className="bg-white/50 backdrop-blur-sm divide-y divide-indigo-100/30">
                {teamPerformance.map((member, index) => (
                  <tr key={index} className="hover:bg-indigo-50/30 transition-colors">
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-8 w-8 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white font-medium shadow-sm">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-indigo-800">{member.name}</p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-sm text-indigo-800">{member.conversions}</span>
                        <div className="ml-2 w-16 bg-indigo-100/40 rounded-full h-1.5">
                          <div className="h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600" style={{width: `${(member.conversions / 50) * 100}%`}}></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-sm text-indigo-800">{formatCurrency(member.revenue).replace('₹', '').replace(',000', 'K')}</span>
                        <div className="ml-2 w-16 bg-indigo-100/40 rounded-full h-1.5">
                          <div className="h-1.5 rounded-full bg-gradient-to-r from-green-500 to-emerald-400" style={{width: `${(member.revenue / 400000) * 100}%`}}></div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-sm text-indigo-800">{member.satisfaction}★</span>
                        <div className="ml-2 w-16 bg-indigo-100/40 rounded-full h-1.5">
                          <div className="h-1.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-400" style={{width: `${(member.satisfaction / 5) * 100}%`}}></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </GlassCard>

        <GlassCard className="p-6 transform transition hover:translate-y-[-4px]">
          <h2 className="text-lg font-semibold text-indigo-800 mb-6">Conversion Funnel</h2>
          
          <div className="space-y-4">
            {conversionFunnel.map((stage, index) => (
              <div key={stage.stage}>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">{stage.stage}</span>
                  <span className="font-medium text-indigo-800">{stage.count} ({stage.percentage}%)</span>
                </div>
                <div className="w-full bg-indigo-100/40 rounded-lg h-8 relative overflow-hidden">
                  <div 
                    className={`h-8 rounded-lg ${
                      index === 0 ? 'bg-gradient-to-r from-blue-500 to-indigo-600' :
                      index === 1 ? 'bg-gradient-to-r from-indigo-500 to-purple-600' :
                      index === 2 ? 'bg-gradient-to-r from-purple-500 to-pink-600' :
                      index === 3 ? 'bg-gradient-to-r from-pink-500 to-rose-600' :
                      'bg-gradient-to-r from-green-500 to-emerald-400'
                    }`} 
                    style={{ width: `${stage.percentage}%` }}
                  >
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                      {stage.stage}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 bg-indigo-50/50 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-indigo-600 font-medium">Lead-to-Client Conversion</p>
                <p className="text-xl font-bold text-indigo-800">38%</p>
              </div>
              <div className="h-10 w-10 bg-gradient-to-br from-indigo-400 to-blue-300 rounded-full flex items-center justify-center text-white shadow-sm">
                <TrendingUp className="h-5 w-5" />
              </div>
            </div>
            <div className="mt-2">
              <p className="text-xs text-gray-600">Conversion rate from initial lead to completed client</p>
            </div>
          </div>
        </GlassCard>
      </div>
    </div>
  );
} 