import React from 'react';
import { 
  Bar<PERSON><PERSON>2, 
  CheckC<PERSON>cle2, 
  Clock, 
  AlertTriangle, 
  Users, 
  TrendingUp
} from 'lucide-react';

interface CaseOverviewProps {
  currentCaseload: number;
  avgProcessingTime: string;
  approvalRate: string;
  flaggedCases: number;
}

export default function CaseOverview({
  currentCaseload,
  avgProcessingTime,
  approvalRate,
  flaggedCases
}: CaseOverviewProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Key Metrics */}
        <MetricCard 
          title="Current Caseload"
          value={currentCaseload}
          icon={<Users className="h-8 w-8 text-indigo-600" />}
          trend="up"
          trendValue="12%"
          color="bg-indigo-50"
        />
        
        <MetricCard 
          title="Avg. Processing Time"
          value={avgProcessingTime}
          icon={<Clock className="h-8 w-8 text-blue-600" />}
          trend="down"
          trendValue="0.8 days"
          color="bg-blue-50"
        />
        
        <MetricCard 
          title="Approval Rate"
          value={approvalRate}
          icon={<CheckCircle2 className="h-8 w-8 text-green-600" />}
          trend="up"
          trendValue="3%"
          color="bg-green-50"
        />
        
        <MetricCard 
          title="Flagged Cases"
          value={flaggedCases}
          icon={<AlertTriangle className="h-8 w-8 text-amber-600" />}
          trend="down"
          trendValue="1"
          color="bg-amber-50"
          valueColor="text-amber-600"
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Processing Time Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">Processing Time Trends</h3>
          
          <div className="h-64 flex items-center justify-center">
            {/* This would be replaced with an actual chart component */}
            <div className="text-center text-gray-500">
              <BarChart2 size={48} className="mx-auto mb-2 text-gray-400" />
              <p>Visa processing time visualization would go here</p>
              <p className="text-sm">Using Chart.js, Recharts, or similar library</p>
            </div>
          </div>
        </div>
        
        {/* Success Metrics */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold mb-4">Success Metrics</h3>
          
          <div className="h-64 flex items-center justify-center">
            {/* This would be replaced with an actual chart component */}
            <div className="text-center text-gray-500">
              <TrendingUp size={48} className="mx-auto mb-2 text-gray-400" />
              <p>Approval rates by visa type would go here</p>
              <p className="text-sm">Using Chart.js, Recharts, or similar library</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Recent Case Activity */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Case Activity</h3>
        
        <div className="space-y-4">
          {recentActivityData.map((activity, index) => (
            <div key={index} className="flex items-start border-b border-gray-100 last:border-0 pb-4 last:pb-0">
              <div className={`p-2 rounded-lg mr-3 ${getActivityIconColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
              <div>
                <p className="text-sm font-medium">{activity.description}</p>
                <div className="flex items-center mt-1">
                  <span className="text-xs text-gray-500">{activity.time}</span>
                  <span className="mx-2 text-gray-300">•</span>
                  <span className="text-xs text-gray-500">{activity.user}</span>
                  <span className="mx-2 text-gray-300">•</span>
                  <span className="text-xs text-gray-500">{activity.caseId}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend: 'up' | 'down' | 'neutral';
  trendValue: string;
  color: string;
  valueColor?: string;
}

function MetricCard({ title, value, icon, trend, trendValue, color, valueColor = 'text-gray-900' }: MetricCardProps) {
  return (
    <div className={`rounded-xl shadow-sm p-6 ${color}`}>
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className={`text-2xl font-bold mt-1 ${valueColor}`}>{value}</p>
        </div>
        <div className="p-2 rounded-full bg-white/80">{icon}</div>
      </div>
      <div className="mt-4">
        <span className={`inline-flex items-center text-xs font-medium ${
          trend === 'up' ? 'text-green-600' : 
          trend === 'down' ? (title === 'Flagged Cases' ? 'text-green-600' : 'text-red-600') : 
          'text-gray-600'
        }`}>
          {trend === 'up' ? '↑' : trend === 'down' ? '↓' : '→'} {trendValue}
          <span className="ml-1 text-gray-500">from last month</span>
        </span>
      </div>
    </div>
  );
}

// Mock data
const recentActivityData = [
  {
    type: 'document',
    description: 'Financial documents uploaded for Aditya Mehta',
    time: '2 hours ago',
    user: 'Priya Sharma',
    caseId: 'CS-2023-1234'
  },
  {
    type: 'status',
    description: 'Case status changed to "Ready for Review" for Sneha Reddy',
    time: '4 hours ago',
    user: 'Priya Sharma',
    caseId: 'CS-2023-1235'
  },
  {
    type: 'comment',
    description: 'New comment added to Rajat Gupta\'s application',
    time: 'Yesterday at 3:45 PM',
    user: 'Amit Kumar',
    caseId: 'CS-2023-1236'
  },
  {
    type: 'flag',
    description: 'Case flagged for attention: Policy change affecting application',
    time: 'Yesterday at 11:20 AM',
    user: 'System Alert',
    caseId: 'CS-2023-1236'
  },
  {
    type: 'interview',
    description: 'Interview preparation session scheduled with Meera Patel',
    time: '2 days ago',
    user: 'Priya Sharma',
    caseId: 'CS-2023-1237'
  }
];

function getActivityIcon(type: string) {
  switch (type) {
    case 'document':
      return <FileIcon className="h-5 w-5" />;
    case 'status':
      return <RefreshIcon className="h-5 w-5" />;
    case 'comment':
      return <MessageIcon className="h-5 w-5" />;
    case 'flag':
      return <AlertTriangle className="h-5 w-5" />;
    case 'interview':
      return <CalendarIcon className="h-5 w-5" />;
    default:
      return <ActivityIcon className="h-5 w-5" />;
  }
}

function getActivityIconColor(type: string) {
  switch (type) {
    case 'document':
      return 'bg-blue-100 text-blue-600';
    case 'status':
      return 'bg-green-100 text-green-600';
    case 'comment':
      return 'bg-purple-100 text-purple-600';
    case 'flag':
      return 'bg-amber-100 text-amber-600';
    case 'interview':
      return 'bg-indigo-100 text-indigo-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
}

// Simple icon components
function FileIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  );
}

function RefreshIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
  );
}

function MessageIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
    </svg>
  );
}

function CalendarIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
  );
}

function ActivityIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
    </svg>
  );
} 