"use client";

import { useState } from "react";
import { ArrowRight, Info, Check, AlertCircle } from "lucide-react";

interface Executive {
  id: string;
  name: string;
  workload: number;
  status?: string;
}

interface Lead {
  id: string;
  name: string;
  service: string;
  priority: string;
  status: string;
}

interface DistributionRules {
  maxPerExecutive: number;
  serviceSpecialization: {
    [key: string]: string[];
  };
}

interface LeadDistributionProps {
  executives: Executive[];
  leads: Lead[];
  strategy: string;
  rules: DistributionRules;
}

export function LeadDistribution({ executives, leads, strategy, rules }: LeadDistributionProps) {
  const [assignments, setAssignments] = useState<{[key: string]: string}>({});
  const [hoveredExecutive, setHoveredExecutive] = useState<string | null>(null);
  
  // Determine recommended assignments based on strategy
  const getRecommendedAssignment = (lead: Lead) => {
    if (strategy === 'balanced') {
      // Find executive with lowest workload
      const sortedByWorkload = [...executives].sort((a, b) => a.workload - b.workload);
      return sortedByWorkload[0]?.id;
    } else if (strategy === 'skill-based') {
      // Find executive specialized in this service
      const specialists = rules.serviceSpecialization[lead.service] || [];
      if (specialists.length > 0) {
        // Return the specialist with lowest workload
        const specialistExecs = executives.filter(e => specialists.includes(e.id));
        const sortedSpecialists = [...specialistExecs].sort((a, b) => a.workload - b.workload);
        return sortedSpecialists[0]?.id;
      }
      // If no specialist, fall back to balanced
      const sortedByWorkload = [...executives].sort((a, b) => a.workload - b.workload);
      return sortedByWorkload[0]?.id;
    } else if (strategy === 'priority') {
      // High priority leads go to best performers
      const highPerformers = executives.filter(e => e.status === 'exceeding');
      if (lead.priority === 'high' && highPerformers.length > 0) {
        const sortedHighPerformers = [...highPerformers].sort((a, b) => a.workload - b.workload);
        return sortedHighPerformers[0]?.id;
      }
      // Others go to regular performers with capacity
      const sortedByWorkload = [...executives].sort((a, b) => a.workload - b.workload);
      return sortedByWorkload[0]?.id;
    }
    return executives[0]?.id;
  };
  
  const assignLead = (leadId: string, executiveId: string) => {
    setAssignments({
      ...assignments,
      [leadId]: executiveId
    });
    
    // For simplicity, this would usually update the backend too
  };
  
  const getExecutiveWorkload = (executiveId: string) => {
    const baseWorkload = executives.find(e => e.id === executiveId)?.workload || 0;
    const assignedLeadsCount = Object.values(assignments).filter(id => id === executiveId).length;
    return baseWorkload + assignedLeadsCount;
  };
  
  const getWorkloadStatus = (executiveId: string) => {
    const workload = getExecutiveWorkload(executiveId);
    if (workload >= rules.maxPerExecutive) return 'critical';
    if (workload >= rules.maxPerExecutive * 0.8) return 'warning';
    return 'normal';
  };
  
  return (
    <div>
      <div className="mb-4">
        <h3 className="text-sm font-medium text-gray-600 mb-2">Current Strategy: <span className="text-indigo-600 font-semibold capitalize">{strategy}</span></h3>
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 text-sm text-blue-700 flex items-start">
          <Info size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          <p>
            {strategy === 'balanced' && "Leads are distributed evenly to balance workload across the team."}
            {strategy === 'skill-based' && "Leads are assigned to executives with specialized experience in the visa category."}
            {strategy === 'priority' && "High-priority leads go to top performers, ensuring the best conversion rates."}
          </p>
        </div>
      </div>
      
      <div className="grid grid-cols-7 gap-2 text-sm font-medium text-gray-700 mb-2 items-center">
        <div className="col-span-3">Lead</div>
        <div className="col-span-3">Executive</div>
        <div className="col-span-1">Action</div>
      </div>
      
      <div className="space-y-2">
        {leads.map((lead) => {
          const assignedExecutiveId = assignments[lead.id] || getRecommendedAssignment(lead);
          const assignedExecutive = executives.find(e => e.id === assignedExecutiveId);
          
          return (
            <div key={lead.id} className="grid grid-cols-7 gap-2 items-center bg-white p-3 rounded-lg border border-gray-100 hover:border-gray-300 transition-colors">
              <div className="col-span-3">
                <div className="flex items-center">
                  <div>
                    <p className="font-medium text-gray-800">{lead.name}</p>
                    <div className="flex items-center mt-1">
                      <span 
                        className={`text-xs rounded-full px-2 py-0.5 ${
                          lead.service === 'EB-1' ? 'bg-indigo-100 text-indigo-700' :
                          lead.service === 'O-1' ? 'bg-blue-100 text-blue-700' :
                          lead.service === 'H-1B' ? 'bg-purple-100 text-purple-700' :
                          'bg-gray-100 text-gray-700'
                        }`}
                      >
                        {lead.service}
                      </span>
                      <span 
                        className={`text-xs rounded-full px-2 py-0.5 ml-1 ${
                          lead.priority === 'high' ? 'bg-red-100 text-red-700' : 'bg-amber-100 text-amber-700'
                        }`}
                      >
                        {lead.priority === 'high' ? 'High Priority' : 'Medium Priority'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="col-span-3">
                <select
                  className="w-full bg-white border border-gray-300 text-gray-700 py-1.5 px-2 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  value={assignedExecutiveId}
                  onChange={(e) => assignLead(lead.id, e.target.value)}
                >
                  {executives.map((exec) => {
                    const workloadStatus = getWorkloadStatus(exec.id);
                    return (
                      <option 
                        key={exec.id} 
                        value={exec.id}
                        disabled={workloadStatus === 'critical'}
                      >
                        {exec.name} 
                        {workloadStatus === 'critical' ? ' (Full)' : 
                         workloadStatus === 'warning' ? ' (Near capacity)' : ''}
                      </option>
                    );
                  })}
                </select>
                
                <div 
                  className="flex items-center mt-1 text-xs"
                  onMouseEnter={() => setHoveredExecutive(assignedExecutiveId)}
                  onMouseLeave={() => setHoveredExecutive(null)}
                >
                  {rules.serviceSpecialization[lead.service]?.includes(assignedExecutiveId) ? (
                    <span className="text-green-600 flex items-center">
                      <Check size={12} className="mr-1" />
                      Service specialist
                    </span>
                  ) : (
                    <span className="text-gray-500">
                      {getWorkloadStatus(assignedExecutiveId) === 'warning' ? (
                        <span className="text-amber-600 flex items-center">
                          <AlertCircle size={12} className="mr-1" />
                          Approaching capacity
                        </span>
                      ) : getWorkloadStatus(assignedExecutiveId) === 'critical' ? (
                        <span className="text-red-600 flex items-center">
                          <AlertCircle size={12} className="mr-1" />
                          Overloaded
                        </span>
                      ) : (
                        <span className="text-blue-600">
                          {getExecutiveWorkload(assignedExecutiveId)} leads assigned
                        </span>
                      )}
                    </span>
                  )}
                </div>
              </div>
              
              <div className="col-span-1">
                <button 
                  className="p-1.5 bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded-full transition-colors"
                  onClick={() => assignLead(lead.id, getRecommendedAssignment(lead))}
                  title={assignments[lead.id] ? "Reassign" : "Assign"}
                >
                  <ArrowRight size={16} />
                </button>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="mt-6 border-t border-gray-100 pt-4">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Team Workload</h3>
        <div className="space-y-3">
          {executives.map((executive) => {
            const workload = getExecutiveWorkload(executive.id);
            const workloadPercentage = (workload / rules.maxPerExecutive) * 100;
            
            return (
              <div key={executive.id} className={`${hoveredExecutive === executive.id ? 'bg-gray-50 rounded' : ''} p-1`}>
                <div className="flex justify-between text-xs mb-1">
                  <span className="font-medium text-gray-700">{executive.name}</span>
                  <span className="text-gray-500">
                    {workload} / {rules.maxPerExecutive} leads
                    {workload >= rules.maxPerExecutive ? (
                      <span className="ml-1 text-red-600">(At capacity)</span>
                    ) : null}
                  </span>
                </div>
                <div className="w-full bg-gray-100 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      workloadPercentage >= 100 ? 'bg-red-500' :
                      workloadPercentage >= 80 ? 'bg-amber-500' :
                      'bg-green-500'
                    }`}
                    style={{width: `${Math.min(100, workloadPercentage)}%`}}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
} 