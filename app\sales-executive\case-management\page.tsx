"use client";

import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Ta<PERSON>Trigger 
} from "@/components/ui/tabs";
import { 
  Users, 
  FileText, 
  Shield, 
  Video, 
  AlertTriangle,
  Settings
} from "lucide-react";

// Import modular components
import CaseOverview from "@/components/sales/case-management/CaseOverview";
import DocumentWorkflow from "@/components/sales/case-management/DocumentWorkflow";
import ComplianceTool from "@/components/sales/case-management/ComplianceTool";
import ClientEducation from "@/components/sales/case-management/ClientEducation";
import SecurityPanel from "@/components/sales/case-management/SecurityPanel";

export default function CaseManagementPage() {
  const [activeTab, setActiveTab] = useState("overview");
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Case Management Portal</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive visa case management and compliance system
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm font-medium flex items-center">
            <Shield className="h-4 w-4 mr-1" />
            System Status: Operational
          </div>
          <button className="p-2 bg-gray-100 rounded-full hover:bg-gray-200">
            <Settings className="h-5 w-5 text-gray-600" />
          </button>
        </div>
      </div>
      
      <Tabs defaultValue="overview" className="w-full" onValueChange={setActiveTab} value={activeTab}>
        <TabsList className="grid grid-cols-2 md:grid-cols-5 mb-8 bg-gray-100 p-1 rounded-lg">
          <TabsTrigger 
            value="overview" 
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm flex items-center justify-center gap-2 py-3"
          >
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          
          <TabsTrigger 
            value="documents" 
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm flex items-center justify-center gap-2 py-3"
          >
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">Documents</span>
          </TabsTrigger>
          
          <TabsTrigger 
            value="compliance" 
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm flex items-center justify-center gap-2 py-3"
          >
            <AlertTriangle className="h-4 w-4" />
            <span className="hidden sm:inline">Compliance</span>
          </TabsTrigger>
          
          <TabsTrigger 
            value="education" 
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm flex items-center justify-center gap-2 py-3"
          >
            <Video className="h-4 w-4" />
            <span className="hidden sm:inline">Education</span>
          </TabsTrigger>
          
          <TabsTrigger 
            value="security" 
            className="data-[state=active]:bg-white data-[state=active]:shadow-sm flex items-center justify-center gap-2 py-3"
          >
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Security</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-0">
          <CaseOverview 
            currentCaseload={23}
            avgProcessingTime="4.2 days"
            approvalRate="92%"
            flaggedCases={2}
          />
        </TabsContent>
        
        <TabsContent value="documents" className="mt-0">
          <DocumentWorkflow />
        </TabsContent>
        
        <TabsContent value="compliance" className="mt-0">
          <ComplianceTool />
        </TabsContent>
        
        <TabsContent value="education" className="mt-0">
          <ClientEducation />
        </TabsContent>
        
        <TabsContent value="security" className="mt-0">
          <SecurityPanel />
        </TabsContent>
      </Tabs>
    </div>
  );
} 