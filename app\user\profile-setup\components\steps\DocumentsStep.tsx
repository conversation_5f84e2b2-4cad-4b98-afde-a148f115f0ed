"use client";

import { useState } from 'react';
import { PROFILE_STEPS, useProfileCompletion } from '@/contexts/ProfileCompletionContext';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Upload, File, FileCheck, X, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

// Mock document types that user might need to upload
const documentTypes = [
  { id: 'passport', name: 'Passport', required: true },
  { id: 'photo', name: 'Recent Photo', required: true },
  { id: 'financial', name: 'Financial Statement', required: false },
  { id: 'letter', name: 'Letter of Invitation', required: false },
];

export default function DocumentsStep() {
  const { moveToNextStep, moveToPrevStep, markStepComplete } = useProfileCompletion();
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedDocs, setUploadedDocs] = useState<Record<string, boolean>>({
    passport: false,
    photo: false,
    financial: false,
    letter: false,
  });

  // Check if all required documents are uploaded
  const requiredDocsUploaded = documentTypes
    .filter(doc => doc.required)
    .every(doc => uploadedDocs[doc.id]);

  // Simulate file upload
  const handleFileUpload = (docId: string) => {
    setIsLoading(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      setUploadedDocs(prev => ({
        ...prev,
        [docId]: true
      }));
      setIsLoading(false);
      toast.success(`${documentTypes.find(d => d.id === docId)?.name} uploaded successfully`);
    }, 1500);
  };

  const handleRemoveDocument = (docId: string) => {
    setUploadedDocs(prev => ({
      ...prev,
      [docId]: false
    }));
    toast.info(`${documentTypes.find(d => d.id === docId)?.name} removed`);
  };

  const handleContinue = async () => {
    if (!requiredDocsUploaded) {
      toast.error('Please upload all required documents');
      return;
    }

    setIsLoading(true);
    try {
      // Save uploaded document status to profile
      await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          documents: uploadedDocs,
        }),
      });

      markStepComplete(PROFILE_STEPS.DOCUMENTS);
      toast.success('Document information saved');
      moveToNextStep();
    } catch (error) {
      toast.error('Failed to save document information');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Document Upload</h2>
        <p className="text-muted-foreground">
          Please upload the necessary documents for your visa application.
          <span className="text-red-500">*</span> indicates required documents.
        </p>
      </div>

      <div className="space-y-4">
        {documentTypes.map((doc) => (
          <div 
            key={doc.id}
            className={cn(
              "border rounded-md p-4",
              uploadedDocs[doc.id] ? "border-green-500/20 bg-green-50/50" : "border-gray-200"
            )}
          >
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <File className="h-5 w-5 text-muted-foreground" />
                <span>
                  {doc.name} 
                  {doc.required && <span className="text-red-500 ml-1">*</span>}
                </span>
              </div>
              
              {uploadedDocs[doc.id] ? (
                <div className="flex items-center gap-2">
                  <FileCheck className="h-5 w-5 text-green-500" />
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => handleRemoveDocument(doc.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => handleFileUpload(doc.id)}
                  disabled={isLoading}
                  className="flex items-center gap-2"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4" />
                  )}
                  Upload
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-between pt-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={moveToPrevStep}
        >
          Previous
        </Button>
        <Button 
          onClick={handleContinue}
          disabled={isLoading || !requiredDocsUploaded}
        >
          {isLoading ? "Saving..." : "Save & Continue"}
        </Button>
      </div>
    </div>
  );
} 