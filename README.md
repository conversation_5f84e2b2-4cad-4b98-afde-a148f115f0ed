# Visa Mentor

Visa Mentor is a comprehensive visa application management system designed to streamline the visa application process for clients and consultants.

## Features

- **User Management**: Multi-role system with admin, sales executive, sales manager, and client user roles
- **Lead Management**: Intelligent lead scoring and routing system
- **CRM Integration**: Client relationship management tools
- **Document Management**: Secure document upload and management
- **Appointment Scheduling**: Calendar integration for consultations
- **Dashboard Analytics**: Insights for all user roles
- **Mobile Responsive**: Works on all devices

## Tech Stack

- **Frontend**: Next.js 15, React 18, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MySQL with Prisma ORM
- **Authentication**: JWT and NextAuth
- **UI Components**: Radix UI with custom styling

## Getting Started

### Prerequisites

- Node.js 18+ and npm/pnpm
- MySQL database

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/your-username/visa-mentor.git
   cd visa-mentor
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Set up environment variables:
   ```
   cp env.production.template .env
   ```
   Edit the `.env` file with your database credentials and other settings.

4. Set up the database:
   ```
   npx prisma migrate dev
   ```

5. Start the development server:
   ```
   npm run dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Deployment

See [DEPLOYMENT_CHECKLIST.md](./DEPLOYMENT_CHECKLIST.md) for detailed deployment instructions.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Radix UI for accessible components
- Tailwind CSS for styling
- Next.js team for the framework 