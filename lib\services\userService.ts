import { prisma } from '@/lib/prisma'
import { User, UserRole, UserStatus } from '@prisma/client'

export interface CreateUserData {
  clerkId: string
  email: string
  name?: string
  image?: string
  role?: UserRole
}

export interface UpdateUserData {
  name?: string
  phone?: string
  location?: string
  country?: string
  visaType?: string
  visaGoals?: string
  profilePicture?: string
  membershipTier?: string
  profileCompleted?: boolean
  profileProgress?: number
  communicationPreference?: any
  receiveUpdates?: boolean
  receivePromotions?: boolean
}

export class UserService {
  // Create or update user from Clerk authentication
  static async upsertUserFromClerk(clerkId: string, userData: CreateUserData): Promise<User> {
    try {
      const user = await prisma.user.upsert({
        where: { clerkId },
        update: {
          name: userData.name,
          email: userData.email,
          image: userData.image,
          lastLogin: new Date(),
        },
        create: {
          clerkId,
          email: userData.email,
          name: userData.name,
          image: userData.image,
          role: userData.role || 'USER',
          memberSince: new Date().toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long' 
          }),
          lastLogin: new Date(),
        },
      })
      return user
    } catch (error) {
      console.error('Error upserting user:', error)
      throw new Error('Failed to create or update user')
    }
  }

  // Get user by Clerk ID
  static async getUserByClerkId(clerkId: string): Promise<User | null> {
    try {
      return await prisma.user.findUnique({
        where: { clerkId },
        include: {
          applications: {
            orderBy: { updatedAt: 'desc' },
            take: 5
          },
          appointments: {
            where: {
              date: {
                gte: new Date()
              }
            },
            orderBy: { date: 'asc' },
            take: 5
          },
          tasks: {
            where: {
              status: {
                in: ['PENDING', 'IN_PROGRESS']
              }
            },
            orderBy: { dueDate: 'asc' },
            take: 10
          },
          receivedMessages: {
            where: {
              isRead: false
            },
            orderBy: { createdAt: 'desc' },
            take: 5
          }
        }
      })
    } catch (error) {
      console.error('Error fetching user:', error)
      return null
    }
  }

  // Update user profile
  static async updateUserProfile(clerkId: string, updateData: UpdateUserData): Promise<User | null> {
    try {
      return await prisma.user.update({
        where: { clerkId },
        data: updateData,
      })
    } catch (error) {
      console.error('Error updating user profile:', error)
      throw new Error('Failed to update user profile')
    }
  }

  // Get user statistics for dashboard
  static async getUserStats(userId: string) {
    try {
      const [
        applicationsCount,
        appointmentsCount,
        pendingTasksCount,
        unreadMessagesCount
      ] = await Promise.all([
        prisma.application.count({
          where: { userId }
        }),
        prisma.appointment.count({
          where: { 
            userId,
            date: {
              gte: new Date()
            }
          }
        }),
        prisma.task.count({
          where: { 
            userId,
            status: {
              in: ['PENDING', 'IN_PROGRESS']
            }
          }
        }),
        prisma.message.count({
          where: { 
            receiverId: userId,
            isRead: false
          }
        })
      ])

      return {
        applications: applicationsCount,
        appointments: appointmentsCount,
        tasks: pendingTasksCount,
        messages: unreadMessagesCount
      }
    } catch (error) {
      console.error('Error fetching user stats:', error)
      return {
        applications: 0,
        appointments: 0,
        tasks: 0,
        messages: 0
      }
    }
  }

  // Delete user and all related data
  static async deleteUser(clerkId: string): Promise<boolean> {
    try {
      await prisma.user.delete({
        where: { clerkId }
      })
      return true
    } catch (error) {
      console.error('Error deleting user:', error)
      return false
    }
  }
}
