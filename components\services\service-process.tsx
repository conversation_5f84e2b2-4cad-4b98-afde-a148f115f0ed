"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface Step {
  title: string
  description: string
  icon: React.ReactNode
}

interface ServiceProcessProps {
  steps: Step[]
  className?: string
}

export function ServiceProcess({ steps, className }: ServiceProcessProps) {
  return (
    <section className={cn("py-16 bg-gray-50", className)}>
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-outfit font-bold mb-4">How It Works</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Our streamlined process ensures a smooth and efficient experience from start to finish.
          </p>
        </motion.div>

        <div className="relative">
          {/* Connection line */}
          <div className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-[#1E90FF] -translate-x-1/2 hidden md:block" />

          <div className="space-y-12">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative"
              >
                <div className="flex flex-col md:flex-row items-center gap-8">
                  {/* Step number and icon */}
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 rounded-full bg-[#1E90FF] flex items-center justify-center text-white text-2xl font-bold relative z-10">
                      {index + 1}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 bg-white p-6 rounded-lg shadow-lg">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="text-[#1E90FF]">{step.icon}</div>
                      <h3 className="text-xl font-bold">{step.title}</h3>
                    </div>
                    <p className="text-gray-600">{step.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
} 