import { NextRequest, NextResponse } from "next/server";
import formidable from "formidable";
import fs from "fs";
import path from "path";
import { prisma } from "@/lib/prisma";

export const config = {
  api: {
    bodyParser: false,
  },
};

const uploadDir = path.join(process.cwd(), "uploads");
if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir);

export async function POST(req: NextRequest) {
  const form = formidable({ multiples: false, uploadDir, keepExtensions: true });

  return new Promise((resolve, reject) => {
    form.parse(req, async (err, fields, files) => {
      if (err) {
        resolve(NextResponse.json({ error: "Upload failed" }, { status: 500 }));
        return;
      }
      const file = files.file as formidable.File;
      if (!file) {
        resolve(NextResponse.json({ error: "No file uploaded" }, { status: 400 }));
        return;
      }
      // Save metadata to DB (userId is mocked for now)
      const doc = await prisma.document.create({
        data: {
          userId: "mock-user-id",
          filename: file.originalFilename || file.newFilename,
          mimetype: file.mimetype || "application/octet-stream",
          size: file.size,
          url: `/uploads/${file.newFilename}`,
        },
      });
      resolve(NextResponse.json(doc));
    });
  });
} 