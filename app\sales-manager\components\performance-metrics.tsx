"use client";

import { ArrowUpRight, ArrowDownRight } from "lucide-react";
import { useState } from "react";

interface Executive {
  id: string;
  name: string;
  metrics: {
    conversions: number;
    revenue: string;
    satisfaction: number;
  };
  status: string;
}

interface Metric {
  label: string;
  value: string;
  trend: number;
}

interface PerformanceMetricsProps {
  data: Executive[];
  metrics: Metric[];
}

export function PerformanceMetrics({ data, metrics }: PerformanceMetricsProps) {
  const [activeView, setActiveView] = useState<'overview' | 'executive'>('overview');
  const [selectedExecutive, setSelectedExecutive] = useState<string>(data[0]?.id || '');

  return (
    <div>
      <div className="flex border border-gray-200 rounded-lg overflow-hidden mb-6">
        <button
          className={`flex-1 text-center py-2 text-sm font-medium ${
            activeView === 'overview'
              ? 'bg-indigo-600 text-white'
              : 'bg-white text-gray-600 hover:bg-gray-50'
          }`}
          onClick={() => setActiveView('overview')}
        >
          Team Overview
        </button>
        <button
          className={`flex-1 text-center py-2 text-sm font-medium ${
            activeView === 'executive'
              ? 'bg-indigo-600 text-white'
              : 'bg-white text-gray-600 hover:bg-gray-50'
          }`}
          onClick={() => setActiveView('executive')}
        >
          By Executive
        </button>
      </div>

      {activeView === 'overview' ? (
        <div className="space-y-4">
          {metrics.map((metric, index) => (
            <div key={index} className="p-4 border border-gray-100 rounded-lg">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium text-gray-700">{metric.label}</h3>
                <div className="flex items-center">
                  <span className="text-xl font-bold text-gray-800 mr-2">{metric.value}</span>
                  <span className={`flex items-center text-xs font-medium ${metric.trend > 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {metric.trend > 0 ? (
                      <ArrowUpRight size={14} className="mr-0.5" />
                    ) : (
                      <ArrowDownRight size={14} className="mr-0.5" />
                    )}
                    {Math.abs(metric.trend)}%
                  </span>
                </div>
              </div>
              <div className="mt-2 h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                <div 
                  className={`h-full ${metric.label.includes('Conversion') ? 'bg-green-500' : metric.label.includes('Response') ? 'bg-blue-500' : 'bg-purple-500'}`} 
                  style={{width: `${Math.min(100, parseInt(metric.value) || 70)}%`}}
                ></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div>
          <div className="mb-4">
            <label htmlFor="executive-select" className="block text-sm font-medium text-gray-700 mb-1">
              Select Executive
            </label>
            <select
              id="executive-select"
              className="w-full bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={selectedExecutive}
              onChange={(e) => setSelectedExecutive(e.target.value)}
            >
              {data.map((executive) => (
                <option key={executive.id} value={executive.id}>
                  {executive.name}
                </option>
              ))}
            </select>
          </div>

          {data
            .filter((executive) => executive.id === selectedExecutive)
            .map((executive) => (
              <div key={executive.id} className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="bg-indigo-50 p-3 rounded-lg text-center">
                    <p className="text-xs text-indigo-700 mb-1">Conversions</p>
                    <p className="text-xl font-bold text-indigo-900">{executive.metrics.conversions}</p>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg text-center">
                    <p className="text-xs text-green-700 mb-1">Revenue</p>
                    <p className="text-xl font-bold text-green-900">{executive.metrics.revenue}</p>
                  </div>
                  <div className="bg-purple-50 p-3 rounded-lg text-center">
                    <p className="text-xs text-purple-700 mb-1">Satisfaction</p>
                    <p className="text-xl font-bold text-purple-900">{executive.metrics.satisfaction}★</p>
                  </div>
                </div>

                <div className="p-4 border border-gray-100 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium text-gray-700">Performance Status</h3>
                    <span 
                      className={`text-xs font-medium rounded-full px-2 py-1 ${
                        executive.status === 'exceeding' ? 'bg-green-100 text-green-800' :
                        executive.status === 'meeting' ? 'bg-blue-100 text-blue-800' :
                        'bg-orange-100 text-orange-800'
                      }`}
                    >
                      {executive.status === 'exceeding' ? 'Exceeding Target' :
                       executive.status === 'meeting' ? 'Meeting Target' :
                       'Below Target'}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-500">
                    {executive.status === 'exceeding' 
                      ? 'Performing above team average in all metrics.' 
                      : executive.status === 'meeting'
                      ? 'Performance in line with team expectations.'
                      : 'Needs improvement in key performance areas.'}
                  </p>
                </div>

                <div className="p-4 border border-gray-100 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Comparison to Team Average</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span className="text-gray-500">Conversions</span>
                        <span className="font-medium">{(executive.metrics.conversions / 39.75 * 100).toFixed(1)}% of avg</span>
                      </div>
                      <div className="w-full bg-gray-100 rounded-full h-2">
                        <div 
                          className="h-2 rounded-full bg-indigo-500" 
                          style={{width: `${Math.min(100, (executive.metrics.conversions / 39.75 * 100))}%`}}
                        ></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span className="text-gray-500">Revenue</span>
                        <span className="font-medium">
                          {(parseInt(executive.metrics.revenue.replace(/[^0-9]/g, '')) / 120 * 100).toFixed(1)}% of avg
                        </span>
                      </div>
                      <div className="w-full bg-gray-100 rounded-full h-2">
                        <div 
                          className="h-2 rounded-full bg-green-500" 
                          style={{width: `${Math.min(100, parseInt(executive.metrics.revenue.replace(/[^0-9]/g, '')) / 120 * 100)}%`}}
                        ></div>
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span className="text-gray-500">Satisfaction</span>
                        <span className="font-medium">{(executive.metrics.satisfaction / 4.65 * 100).toFixed(1)}% of avg</span>
                      </div>
                      <div className="w-full bg-gray-100 rounded-full h-2">
                        <div 
                          className="h-2 rounded-full bg-purple-500" 
                          style={{width: `${Math.min(100, (executive.metrics.satisfaction / 4.65 * 100))}%`}}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}
    </div>
  );
} 