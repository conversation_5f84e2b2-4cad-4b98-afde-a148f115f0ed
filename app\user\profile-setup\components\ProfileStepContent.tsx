"use client";

import { PROFILE_STEPS, useProfileCompletion } from '@/contexts/ProfileCompletionContext';
import PersonalInfoStep from './steps/PersonalInfoStep';
import ContactDetailsStep from './steps/ContactDetailsStep';
import VisaDetailsStep from './steps/VisaDetailsStep';
import DocumentsStep from './steps/DocumentsStep';
import PreferencesStep from './steps/PreferencesStep';

export default function ProfileStepContent() {
  const { currentStep } = useProfileCompletion();

  // Render appropriate step content based on current step
  const renderStepContent = () => {
    switch (currentStep) {
      case PROFILE_STEPS.PERSONAL:
        return <PersonalInfoStep />;
      case PROFILE_STEPS.CONTACT:
        return <ContactDetailsStep />;
      case PROFILE_STEPS.VISA_DETAILS:
        return <VisaDetailsStep />;
      case PROFILE_STEPS.DOCUMENTS:
        return <DocumentsStep />;
      case PROFILE_STEPS.PREFERENCES:
        return <PreferencesStep />;
      default:
        return <PersonalInfoStep />;
    }
  };

  return (
    <div className="p-1">
      {renderStepContent()}
    </div>
  );
} 