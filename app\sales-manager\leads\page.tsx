"use client";

import { useState, useEffect } from "react";
import { 
  Search, 
  Filter, 
  PlusCircle, 
  Download, 
  Upload, 
  FileSpreadsheet,
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Trash2,
  MoreHorizontal,
  ArrowUp,
  ArrowDown,
  UserPlus,
  Eye,
  Edit,
  ChevronRight,
  CalendarClock,
  IndianRupee,
  Phone,
  RefreshCcw
} from "lucide-react";
import LeadDetailsModal from "../components/lead-details-modal";
import LeadFormModal from "../components/lead-form-modal";

// Define lead status types with colors for consistency
const STATUS_TYPES = {
  new: { label: "New", color: "blue" },
  contacted: { label: "Contacted", color: "indigo" },
  qualified: { label: "Qualified", color: "purple" },
  proposal: { label: "Proposal", color: "amber" },
  negotiation: { label: "Negotiation", color: "orange" },
  closed: { label: "Closed", color: "green" },
  lost: { label: "Lost", color: "red" }
};

// Define the Lead interface
interface Lead {
  id: string;
  clientName: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  service: string;
  date: string;
  status: string;
  priority: string;
  assignedTo: string | null;
  value: number;
  notes: string;
  lastActivity: string;
  nextFollowUp: string | null;
  source: string;
}

// Mock executives data (for assignment dropdown)
const EXECUTIVES = [
  { id: 'exec1', name: 'Priya Sharma', capacity: 12, currentLeads: 6 },
  { id: 'exec2', name: 'Rahul Singh', capacity: 10, currentLeads: 8 },
  { id: 'exec3', name: 'Amit Patel', capacity: 15, currentLeads: 7 },
  { id: 'exec4', name: 'Neha Kumar', capacity: 12, currentLeads: 9 },
];

export default function LeadsPage() {
  // State for various filtering, sorting, and UI controls
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [serviceFilter, setServiceFilter] = useState("all");
  const [assignmentFilter, setAssignmentFilter] = useState("all");
  const [sortField, setSortField] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const [selectedLead, setSelectedLead] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  
  // Additional state for lead management
  const [leads, setLeads] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [editingLeadId, setEditingLeadId] = useState<string | null>(null);
  const [deleteConfirmModalOpen, setDeleteConfirmModalOpen] = useState(false);
  const [deletingLeadId, setDeletingLeadId] = useState<string | null>(null);
  
  // Fetch leads from API on component mount
  useEffect(() => {
    fetchLeads();
  }, []);
  
  // Function to fetch leads from API
  const fetchLeads = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/leads');
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch leads');
      }
      
      setLeads(data.leads);
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      console.error('Error fetching leads:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Function to filter leads based on all active filters
  const getFilteredLeads = () => {
    return leads.filter(lead => {
      // Text search across multiple fields
      const matchesSearch = 
        searchTerm === "" || 
        lead.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.contactEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (lead.notes && lead.notes.toLowerCase().includes(searchTerm.toLowerCase()));
      
      // Status filter
      const matchesStatus = 
        statusFilter === "all" || 
        lead.status === statusFilter;
      
      // Priority filter
      const matchesPriority = 
        priorityFilter === "all" || 
        lead.priority === priorityFilter;
      
      // Service filter
      const matchesService = 
        serviceFilter === "all" || 
        lead.service === serviceFilter;
      
      // Assignment filter
      const matchesAssignment = 
        assignmentFilter === "all" || 
        (assignmentFilter === "unassigned" && !lead.assignedTo) ||
        (assignmentFilter === "assigned" && lead.assignedTo) ||
        lead.assignedTo === assignmentFilter;
      
      // Tab filter
      const matchesTab = 
        activeTab === "all" || 
        (activeTab === "new" && (lead.status === "new" || lead.status === "contacted")) ||
        (activeTab === "active" && (lead.status === "qualified" || lead.status === "proposal" || lead.status === "negotiation")) ||
        (activeTab === "closed" && (lead.status === "closed" || lead.status === "lost"));
      
      return matchesSearch && matchesStatus && matchesPriority && matchesService && matchesAssignment && matchesTab;
    });
  };
  
  // Get filtered and sorted leads
  const filteredLeads = getFilteredLeads().sort((a, b) => {
    // Handle different types of sorting fields
    if (sortField === "date") {
      return sortDirection === "asc" 
        ? new Date(a.date).getTime() - new Date(b.date).getTime()
        : new Date(b.date).getTime() - new Date(a.date).getTime();
    } else if (sortField === "value") {
      return sortDirection === "asc" 
        ? a.value - b.value
        : b.value - a.value;
    } else if (sortField === "clientName" || sortField === "service" || sortField === "status" || sortField === "priority") {
      const aVal = String(a[sortField as keyof typeof a] || "");
      const bVal = String(b[sortField as keyof typeof b] || "");
      return sortDirection === "asc"
        ? aVal.localeCompare(bVal)
        : bVal.localeCompare(aVal);
    }
    return 0;
  });

  // Summary counts for dashboard cards
  const totalLeads = leads.length;
  const newLeads = leads.filter(lead => lead.status === "new").length;
  const highPriorityLeads = leads.filter(lead => lead.priority === "high").length;
  const closedLeads = leads.filter(lead => lead.status === "closed").length;
  const totalValue = leads.reduce((sum, lead) => sum + lead.value, 0);
  const unassignedLeads = leads.filter(lead => !lead.assignedTo).length;
  
  // Format currency with Rupee symbol
  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };
  
  // Format date in a more readable way
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-IN', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    }).format(date);
  };
  
  // Get executive name by ID
  const getExecutiveName = (execId: string | null) => {
    if (!execId) return "Unassigned";
    const exec = EXECUTIVES.find(e => e.id === execId);
    return exec ? exec.name : "Unknown";
  };
  
  // Determine capacity status for an executive
  const getCapacityStatus = (execId: string) => {
    const exec = EXECUTIVES.find(e => e.id === execId);
    if (!exec) return { status: "unknown", text: "Unknown" };
    
    const usedCapacity = (exec.currentLeads / exec.capacity) * 100;
    if (usedCapacity >= 100) return { status: "full", text: "At capacity" };
    if (usedCapacity >= 80) return { status: "high", text: "High workload" };
    return { status: "available", text: "Available" };
  };
  
  // Toggle sorting when column header is clicked
  const handleSortChange = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };
  
  // Handle lead assignment change
  const handleAssignmentChange = async (leadId: string, execId: string) => {
    try {
      const response = await fetch('/api/leads', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: leadId,
          assignedTo: execId || null
        }),
      });
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to update assignment');
      }
      
      // Update the leads list with the updated lead
      setLeads(prevLeads => 
        prevLeads.map(lead => 
          lead.id === leadId 
            ? { ...lead, assignedTo: execId || null }
            : lead
        )
      );
      
    } catch (err: any) {
      console.error('Error updating assignment:', err);
      alert('Failed to update assignment: ' + err.message);
    }
  };
  
  // Handle opening the lead details modal
  const handleViewLead = (leadId: string) => {
    setSelectedLead(leadId);
    setIsDetailsModalOpen(true);
  };
  
  // Handle opening the edit lead form
  const handleEditLead = (leadId: string) => {
    setEditingLeadId(leadId);
    setIsFormModalOpen(true);
  };
  
  // Handle lead deletion
  const handleDeleteLeadConfirm = async () => {
    if (!deletingLeadId) return;
    
    try {
      const response = await fetch(`/api/leads?id=${deletingLeadId}`, {
        method: 'DELETE',
      });
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to delete lead');
      }
      
      // Remove the deleted lead from the list
      setLeads(prevLeads => prevLeads.filter(lead => lead.id !== deletingLeadId));
      
      // Close the confirmation modal
      setDeleteConfirmModalOpen(false);
      setDeletingLeadId(null);
      
      // If the details modal is open for this lead, close it
      if (selectedLead === deletingLeadId) {
        setIsDetailsModalOpen(false);
        setSelectedLead(null);
      }
      
    } catch (err: any) {
      console.error('Error deleting lead:', err);
      alert('Failed to delete lead: ' + err.message);
    }
  };
  
  // Handle opening the delete confirmation modal
  const handleDeleteLead = (leadId: string) => {
    setDeletingLeadId(leadId);
    setDeleteConfirmModalOpen(true);
  };
  
  // Function to handle adding a new lead
  const handleAddLead = () => {
    setEditingLeadId(null);
    setIsFormModalOpen(true);
  };
  
  // Function to handle export leads
  const handleExportLeads = () => {
    // In a real application, you would implement a proper CSV/Excel export
    alert('Export functionality would be implemented here');
  };
  
  // Function to handle import leads
  const handleImportLeads = () => {
    // In a real application, you would implement a file upload and processing
    alert('Import functionality would be implemented here');
  };

  return (
    <div className="space-y-6">
      {/* Page header with title and action buttons */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-800">Leads Management</h1>
          <p className="text-gray-500 mt-1">Track, assign and manage your sales pipeline</p>
        </div>
        <div className="flex items-center gap-2">
          <button 
            className="flex items-center gap-1 bg-white text-gray-700 px-3 py-2 rounded-lg border border-gray-300 hover:bg-gray-50"
            onClick={handleImportLeads}
          >
            <Upload size={16} />
            <span>Import</span>
          </button>
          <button 
            className="flex items-center gap-1 bg-white text-gray-700 px-3 py-2 rounded-lg border border-gray-300 hover:bg-gray-50"
            onClick={handleExportLeads}
          >
            <Download size={16} />
            <span>Export</span>
          </button>
          <button 
            className="flex items-center gap-1 bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700"
            onClick={handleAddLead}
          >
            <PlusCircle size={16} />
            <span>Add Lead</span>
          </button>
        </div>
      </div>
      
      {/* Dashboard cards with summary metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="text-sm text-gray-500 mb-1">Total Leads</div>
          <div className="text-2xl font-bold text-gray-800">{totalLeads}</div>
        </div>
        
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="text-sm text-gray-500 mb-1">New Leads</div>
          <div className="text-2xl font-bold text-blue-600">{newLeads}</div>
        </div>
        
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="text-sm text-gray-500 mb-1">High Priority</div>
          <div className="text-2xl font-bold text-amber-600">{highPriorityLeads}</div>
        </div>
        
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="text-sm text-gray-500 mb-1">Closed Deals</div>
          <div className="text-2xl font-bold text-green-600">{closedLeads}</div>
        </div>
        
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="text-sm text-gray-500 mb-1">Pipeline Value</div>
          <div className="text-2xl font-bold text-indigo-600">{formatCurrency(totalValue)}</div>
        </div>
        
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="text-sm text-gray-500 mb-1">Unassigned</div>
          <div className="text-2xl font-bold text-red-600">{unassignedLeads}</div>
        </div>
      </div>
      
      {/* Tabs for lead filtering */}
      <div className="border-b border-gray-200">
        <nav className="flex -mb-px space-x-8">
          <button 
            className={`py-3 border-b-2 font-medium text-sm px-1 ${
              activeTab === 'all' 
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('all')}
          >
            All Leads
          </button>
          <button 
            className={`py-3 border-b-2 font-medium text-sm px-1 ${
              activeTab === 'new' 
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('new')}
          >
            New / Contacted
          </button>
          <button 
            className={`py-3 border-b-2 font-medium text-sm px-1 ${
              activeTab === 'active' 
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('active')}
          >
            Active Pipeline
          </button>
          <button 
            className={`py-3 border-b-2 font-medium text-sm px-1 ${
              activeTab === 'closed' 
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('closed')}
          >
            Closed / Lost
          </button>
        </nav>
      </div>
      
      {/* Search and filter controls */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search field */}
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              placeholder="Search by name, email, service or notes..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* Filter controls */}
          <div className="flex flex-wrap gap-2">
            {/* Status filter */}
            <select
              className="bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Statuses</option>
              {Object.entries(STATUS_TYPES).map(([key, { label }]) => (
                <option key={key} value={key}>{label}</option>
              ))}
            </select>
            
            {/* Priority filter */}
            <select
              className="bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
            >
              <option value="all">All Priorities</option>
              <option value="high">High Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="low">Low Priority</option>
            </select>
            
            {/* Service filter */}
            <select
              className="bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={serviceFilter}
              onChange={(e) => setServiceFilter(e.target.value)}
            >
              <option value="all">All Services</option>
              <option value="EB-1">EB-1</option>
              <option value="O-1">O-1</option>
              <option value="H-1B">H-1B</option>
              <option value="Student Visa">Student Visa</option>
              <option value="EB-5">EB-5</option>
              <option value="J-1">J-1</option>
            </select>
            
            {/* Assignment filter */}
            <select
              className="bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={assignmentFilter}
              onChange={(e) => setAssignmentFilter(e.target.value)}
            >
              <option value="all">All Assignments</option>
              <option value="unassigned">Unassigned</option>
              <option value="assigned">Assigned</option>
              {EXECUTIVES.map(exec => (
                <option key={exec.id} value={exec.id}>{exec.name}</option>
              ))}
            </select>
            
            {/* Reset filters button */}
            <button 
              className="bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 flex items-center gap-1"
              onClick={() => {
                setSearchTerm("");
                setStatusFilter("all");
                setPriorityFilter("all");
                setServiceFilter("all");
                setAssignmentFilter("all");
              }}
            >
              <RefreshCcw size={16} />
              <span>Reset</span>
            </button>
          </div>
        </div>
      </div>
      
      {/* Leads table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="text-red-500 mb-4">{error}</div>
            <button 
              onClick={fetchLeads}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
            >
              Try Again
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {/* Client column header */}
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('clientName')}
                  >
                    <div className="flex items-center">
                      <span>Client</span>
                      {sortField === 'clientName' && (
                        sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                      )}
                    </div>
                  </th>
                  
                  {/* Service column header */}
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('service')}
                  >
                    <div className="flex items-center">
                      <span>Service</span>
                      {sortField === 'service' && (
                        sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                      )}
                    </div>
                  </th>
                  
                  {/* Status column header */}
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('status')}
                  >
                    <div className="flex items-center">
                      <span>Status</span>
                      {sortField === 'status' && (
                        sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                      )}
                    </div>
                  </th>
                  
                  {/* Value column header */}
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('value')}
                  >
                    <div className="flex items-center">
                      <span>Value</span>
                      {sortField === 'value' && (
                        sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                      )}
                    </div>
                  </th>
                  
                  {/* Date column header */}
                  <th 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSortChange('date')}
                  >
                    <div className="flex items-center">
                      <span>Date</span>
                      {sortField === 'date' && (
                        sortDirection === 'asc' ? <ArrowUp size={14} className="ml-1" /> : <ArrowDown size={14} className="ml-1" />
                      )}
                    </div>
                  </th>
                  
                  {/* Assigned To column header */}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assigned To
                  </th>
                  
                  {/* Actions column header */}
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredLeads.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                      No leads found matching your filters
                    </td>
                  </tr>
                ) : (
                  filteredLeads.map(lead => (
                    <tr 
                      key={lead.id} 
                      className={`hover:bg-gray-50 ${selectedLead === lead.id ? 'bg-blue-50' : ''}`}
                    >
                      {/* Client cell */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-start">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{lead.clientName}</div>
                            <div className="text-sm text-gray-500">{lead.contactName}</div>
                            <div className="flex items-center gap-2 mt-1">
                              {lead.priority === 'high' && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                  High Priority
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                      
                      {/* Service cell */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          lead.service === 'EB-1' ? 'bg-indigo-100 text-indigo-800' :
                          lead.service === 'O-1' ? 'bg-blue-100 text-blue-800' :
                          lead.service === 'H-1B' ? 'bg-purple-100 text-purple-800' :
                          lead.service === 'Student Visa' ? 'bg-green-100 text-green-800' :
                          lead.service === 'EB-5' ? 'bg-pink-100 text-pink-800' :
                          lead.service === 'J-1' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {lead.service}
                        </span>
                      </td>
                      
                      {/* Status cell */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          lead.status === 'new' ? 'bg-blue-100 text-blue-800' :
                          lead.status === 'contacted' ? 'bg-indigo-100 text-indigo-800' :
                          lead.status === 'qualified' ? 'bg-purple-100 text-purple-800' :
                          lead.status === 'proposal' ? 'bg-amber-100 text-amber-800' :
                          lead.status === 'negotiation' ? 'bg-orange-100 text-orange-800' :
                          lead.status === 'closed' ? 'bg-green-100 text-green-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {STATUS_TYPES[lead.status as keyof typeof STATUS_TYPES].label}
                        </span>
                      </td>
                      
                      {/* Value cell */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{formatCurrency(lead.value)}</div>
                      </td>
                      
                      {/* Date cell */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(lead.date)}
                      </td>
                      
                      {/* Assigned To cell */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          className={`text-sm border rounded py-1 px-2 ${
                            !lead.assignedTo 
                              ? 'bg-red-50 text-red-700 border-red-200'
                              : 'bg-white text-gray-700 border-gray-300'
                          }`}
                          value={lead.assignedTo || ''}
                          onChange={(e) => handleAssignmentChange(lead.id, e.target.value)}
                        >
                          <option value="">Unassigned</option>
                          {EXECUTIVES.map(exec => {
                            const capacity = getCapacityStatus(exec.id);
                            return (
                              <option 
                                key={exec.id} 
                                value={exec.id}
                                disabled={capacity.status === 'full' && lead.assignedTo !== exec.id}
                              >
                                {exec.name} {capacity.status === 'full' ? '(Full)' : 
                                           capacity.status === 'high' ? '(High)' : ''}
                              </option>
                            );
                          })}
                        </select>
                      </td>
                      
                      {/* Actions cell */}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button 
                            className="text-indigo-600 hover:text-indigo-900"
                            title="View Details"
                            onClick={() => handleViewLead(lead.id)}
                          >
                            <Eye size={18} />
                          </button>
                          <button 
                            className="text-blue-600 hover:text-blue-900"
                            title="Edit Lead"
                            onClick={() => handleEditLead(lead.id)}
                          >
                            <Edit size={18} />
                          </button>
                          <button 
                            className="text-red-600 hover:text-red-900"
                            title="Delete Lead"
                            onClick={() => handleDeleteLead(lead.id)}
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
        
        {/* Pagination controls */}
        {!loading && !error && filteredLeads.length > 0 && (
          <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Showing <span className="font-medium">{filteredLeads.length}</span> of <span className="font-medium">{totalLeads}</span> leads
            </div>
            
            <div className="flex space-x-1">
              <button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Previous</button>
              <button className="px-3 py-1 border border-indigo-500 bg-indigo-50 text-indigo-600 rounded-md text-sm">1</button>
              <button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">2</button>
              <button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">3</button>
              <button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Next</button>
            </div>
          </div>
        )}
      </div>
      
      {/* Modals */}
      {/* Lead Details Modal */}
      <LeadDetailsModal 
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        leadId={selectedLead}
        onEdit={handleEditLead}
        onDelete={handleDeleteLead}
      />
      
      {/* Lead Form Modal (Add/Edit) */}
      <LeadFormModal 
        isOpen={isFormModalOpen}
        onClose={() => setIsFormModalOpen(false)}
        onSuccess={fetchLeads}
        leadId={editingLeadId}
      />
      
      {/* Delete Confirmation Modal */}
      {deleteConfirmModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl w-full max-w-md overflow-hidden">
            <div className="bg-red-600 p-4 text-white">
              <h2 className="text-xl font-bold">Confirm Deletion</h2>
            </div>
            <div className="p-6">
              <p className="mb-6">Are you sure you want to delete this lead? This action cannot be undone.</p>
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => {
                    setDeleteConfirmModalOpen(false);
                    setDeletingLeadId(null);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteLeadConfirm}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  Delete Lead
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 