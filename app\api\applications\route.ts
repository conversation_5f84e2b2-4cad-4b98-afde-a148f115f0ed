import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { ApplicationService } from '@/lib/services/applicationService';
import { UserService } from '@/lib/services/userService';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get applications for user
    const applications = await ApplicationService.getUserApplications(user.id);

    return NextResponse.json({ applications });
  } catch (error) {
    console.error('API: Error fetching applications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const data = await request.json();

    if (!data.type) {
      return NextResponse.json({ error: 'Application type is required' }, { status: 400 });
    }

    // Create new application
    const application = await ApplicationService.createApplication({
      userId: user.id,
      ...data,
    });

    return NextResponse.json({ application }, { status: 201 });
  } catch (error) {
    console.error('API: Error creating application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
