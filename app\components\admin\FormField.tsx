"use client";

import React from "react";

interface FormFieldProps {
  label: string;
  children: React.ReactNode;
  helper?: string;
  required?: boolean;
}

const FormField = ({ label, children, helper, required = false }: FormFieldProps) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start py-4 border-b border-gray-100 last:border-0">
    <div>
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {helper && <p className="mt-1 text-xs text-gray-500">{helper}</p>}
    </div>
    <div className="md:col-span-2">
      {children}
    </div>
  </div>
);

export default FormField; 