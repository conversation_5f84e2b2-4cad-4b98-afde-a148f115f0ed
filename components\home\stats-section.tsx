import { Users, Award, DollarSign, GraduationCap, CheckCircle } from "lucide-react"
import { ReactNode } from "react"

interface GlassCardProps {
  children: ReactNode;
  className?: string;
}

function GlassCard({ children, className = "" }: GlassCardProps) {
  return (
    <div
      className={`rounded-xl p-4 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl ${className}`}
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {children}
    </div>
  )
}

export default function StatsSection() {
  const stats = [
    {
      id: 1,
      icon: <Users className="h-5 w-5 text-indigo-600" />,
      value: "800,000+",
      label: "Active Users",
    },
    {
      id: 2,
      icon: <Award className="h-5 w-5 text-indigo-600" />,
      value: "200+",
      label: "Talent-Based Visa Categories Supported",
    },
    {
      id: 3,
      icon: <DollarSign className="h-5 w-5 text-indigo-600" />,
      value: "$11,560",
      label: "Average Scholarship Secured",
    },
    {
      id: 4,
      icon: <GraduationCap className="h-5 w-5 text-indigo-600" />,
      value: "97",
      label: "Fully Funded Admits in 2024-25",
    },
    {
      id: 5,
      icon: <CheckCircle className="h-5 w-5 text-indigo-600" />,
      value: "8,000+",
      label: "Visa Approvals",
    },
  ]

  return (
    <section className="py-16">
      <div className="container max-w-7xl mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
          Key Stats & Trust Signals
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6">
          {stats.map((stat) => (
            <GlassCard key={stat.id} className="transform transition hover:translate-y-[-4px]">
              <div className="p-6 flex flex-col items-center text-center">
                <div className="mb-4 bg-indigo-50/50 p-3 rounded-full">{stat.icon}</div>
                <h3 className="text-lg font-semibold text-indigo-800 mb-2">{stat.value}</h3>
                <p className="text-xs text-indigo-600">{stat.label}</p>
              </div>
            </GlassCard>
          ))}
        </div>
      </div>
    </section>
  )
}
