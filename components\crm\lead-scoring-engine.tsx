"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  ChevronUp, 
  ChevronDown, 
  BarChart3, 
  ArrowUp, 
  ArrowDown, 
  Filter, 
  Save, 
  RefreshCw
} from "lucide-react";

// Import types from our models
import { ScoreWeights } from "@/lib/models/lead-scoring";

// Mock data for leads
const mockLeads = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1234567890",
    visaType: "EB-1",
    nationality: "Canada",
    documentCompleteness: 0.85,
    responseTime: 0.95,
    financialCapacity: 0.9,
    status: "new",
    source: "Website",
    score: 0.82,
  },
  {
    id: "2",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    phone: "+1987654321",
    visaType: "O-1",
    nationality: "Mexico",
    documentCompleteness: 0.7,
    responseTime: 0.65,
    financialCapacity: 0.8,
    status: "contacted",
    source: "Referral",
    score: 0.72,
  },
  {
    id: "3",
    name: "Li Wei",
    email: "<EMAIL>",
    phone: "+1122334455",
    visaType: "Student",
    nationality: "China",
    documentCompleteness: 0.6,
    responseTime: 0.8,
    financialCapacity: 0.45,
    status: "qualified",
    source: "Partner",
    score: 0.52,
  },
  {
    id: "4",
    name: "Aditya Patel",
    email: "<EMAIL>",
    phone: "+1555666777",
    visaType: "H-1B",
    nationality: "India",
    documentCompleteness: 0.45,
    responseTime: 0.5,
    financialCapacity: 0.35,
    status: "new",
    source: "Google",
    score: 0.38,
  },
  {
    id: "5",
    name: "Sophie Chen",
    email: "<EMAIL>",
    phone: "+1777888999",
    visaType: "EB-1",
    nationality: "Taiwan",
    documentCompleteness: 0.9,
    responseTime: 0.85,
    financialCapacity: 0.95,
    status: "qualified",
    source: "LinkedIn",
    score: 0.91,
  },
];

// Default weights
const defaultWeights: ScoreWeights = {
  visaType: { 'EB-1': 0.4, 'O-1': 0.3, 'Student': 0.2, 'H-1B': 0.25 },
  documentCompleteness: 0.25,
  responseTime: 0.15,
  financialCapacity: 0.2
};

export function LeadScoringEngine() {
  const [leads, setLeads] = useState(mockLeads);
  const [weights, setWeights] = useState<ScoreWeights>(defaultWeights);
  const [sortField, setSortField] = useState("score");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [isConfigOpen, setIsConfigOpen] = useState(false);

  // Function to update weights and recalculate scores
  const updateWeights = (category: string, value: number) => {
    setWeights(prev => {
      const newWeights = { ...prev, [category]: value };
      return newWeights;
    });
  };

  // Function to update visa type weights
  const updateVisaTypeWeight = (visaType: string, value: number) => {
    setWeights(prev => {
      const newVisaTypeWeights = { ...prev.visaType, [visaType]: value };
      return { ...prev, visaType: newVisaTypeWeights };
    });
  };

  // Function to recalculate scores (in a real app, this would call the API)
  const recalculateScores = () => {
    // Simulate API call and score recalculation
    // In a real app, this would send the weights to the backend
    // and get updated scores
    setLeads(prev => [...prev]);
  };

  // Sorting function
  const sortLeads = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // Sort the leads
  const sortedLeads = [...leads].sort((a, b) => {
    const aValue = a[sortField as keyof typeof a];
    const bValue = b[sortField as keyof typeof b];

    if (typeof aValue === "string" && typeof bValue === "string") {
      return sortDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === "number" && typeof bValue === "number") {
      return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });

  // Get the category for a lead score
  const getScoreCategory = (score: number) => {
    if (score >= 0.7) return { label: "Hot", variant: "destructive" as const };
    if (score >= 0.4) return { label: "Warm", variant: "secondary" as const };
    return { label: "Cold", variant: "outline" as const };
  };

  // Format a number as a percentage
  const formatPercentage = (value: number) => `${Math.round(value * 100)}%`;
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Lead Scoring Engine</CardTitle>
            <CardDescription>
              Machine learning-based lead prioritization
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setIsConfigOpen(!isConfigOpen)}
            >
              <Filter className="mr-2 h-4 w-4" />
              Configure Weights
            </Button>
            <Button 
              size="sm"
              onClick={recalculateScores}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Recalculate
            </Button>
          </div>
        </div>
      </CardHeader>
      
      {isConfigOpen && (
        <CardContent className="border-t pt-4">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Scoring Weights Configuration</h3>
            
            <div className="space-y-4">
              <div>
                <Label>Document Completeness</Label>
                <div className="flex items-center gap-4">
                  <Slider 
                    value={[weights.documentCompleteness * 100]} 
                    min={0} 
                    max={100}
                    step={5}
                    onValueChange={(values) => updateWeights("documentCompleteness", values[0] / 100)}
                    className="flex-1"
                  />
                  <span className="w-12 text-sm">{Math.round(weights.documentCompleteness * 100)}%</span>
                </div>
              </div>
              
              <div>
                <Label>Response Time</Label>
                <div className="flex items-center gap-4">
                  <Slider 
                    value={[weights.responseTime * 100]} 
                    min={0} 
                    max={100}
                    step={5}
                    onValueChange={(values) => updateWeights("responseTime", values[0] / 100)}
                    className="flex-1"
                  />
                  <span className="w-12 text-sm">{Math.round(weights.responseTime * 100)}%</span>
                </div>
              </div>
              
              <div>
                <Label>Financial Capacity</Label>
                <div className="flex items-center gap-4">
                  <Slider 
                    value={[weights.financialCapacity * 100]} 
                    min={0} 
                    max={100}
                    step={5}
                    onValueChange={(values) => updateWeights("financialCapacity", values[0] / 100)}
                    className="flex-1"
                  />
                  <span className="w-12 text-sm">{Math.round(weights.financialCapacity * 100)}%</span>
                </div>
              </div>
              
              <div className="pt-2 border-t">
                <Label className="mb-2 block">Visa Type Weights</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(weights.visaType).map(([type, value]) => (
                    <div key={type}>
                      <Label className="text-xs">{type}</Label>
                      <div className="flex items-center gap-2">
                        <Input 
                          type="number"
                          min={0}
                          max={1}
                          step={0.05}
                          value={value}
                          onChange={(e) => updateVisaTypeWeight(type, parseFloat(e.target.value))}
                          className="h-8"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end pt-2">
                <Button onClick={() => setIsConfigOpen(false)}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Configuration
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      )}
      
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead 
                className="cursor-pointer"
                onClick={() => sortLeads("name")}
              >
                Lead Name
                {sortField === "name" && (
                  sortDirection === "asc" ? 
                    <ChevronUp className="inline ml-1 h-4 w-4" /> : 
                    <ChevronDown className="inline ml-1 h-4 w-4" />
                )}
              </TableHead>
              <TableHead 
                className="cursor-pointer"
                onClick={() => sortLeads("visaType")}
              >
                Visa Type
                {sortField === "visaType" && (
                  sortDirection === "asc" ? 
                    <ChevronUp className="inline ml-1 h-4 w-4" /> : 
                    <ChevronDown className="inline ml-1 h-4 w-4" />
                )}
              </TableHead>
              <TableHead className="text-center">Document</TableHead>
              <TableHead className="text-center">Response</TableHead>
              <TableHead className="text-center">Financial</TableHead>
              <TableHead 
                className="cursor-pointer text-right"
                onClick={() => sortLeads("score")}
              >
                Score
                {sortField === "score" && (
                  sortDirection === "asc" ? 
                    <ChevronUp className="inline ml-1 h-4 w-4" /> : 
                    <ChevronDown className="inline ml-1 h-4 w-4" />
                )}
              </TableHead>
              <TableHead className="text-right">Priority</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedLeads.map((lead) => {
              const scoreCategory = getScoreCategory(lead.score);
              
              return (
                <TableRow key={lead.id}>
                  <TableCell className="font-medium">
                    {lead.name}
                    <div className="text-xs text-muted-foreground">{lead.email}</div>
                  </TableCell>
                  <TableCell>{lead.visaType}</TableCell>
                  <TableCell className="text-center">
                    {formatPercentage(lead.documentCompleteness)}
                  </TableCell>
                  <TableCell className="text-center">
                    {formatPercentage(lead.responseTime)}
                  </TableCell>
                  <TableCell className="text-center">
                    {formatPercentage(lead.financialCapacity)}
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatPercentage(lead.score)}
                  </TableCell>
                  <TableCell className="text-right">
                    <Badge variant={scoreCategory.variant}>
                      {scoreCategory.label}
                    </Badge>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
} 