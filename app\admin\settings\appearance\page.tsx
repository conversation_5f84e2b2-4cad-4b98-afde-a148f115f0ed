"use client";

import { useState } from "react";
import { Palette, Upload, <PERSON>, Trash2, <PERSON>, <PERSON>, <PERSON>, Monitor } from "lucide-react";

// Import our reusable components
import SettingsHeader from "@/app/components/admin/SettingsHeader";
import SettingsSection from "@/app/components/admin/SettingsSection";
import FormField from "@/app/components/admin/FormField";
import SaveButton from "@/app/components/admin/SaveButton";
import SuccessMessage from "@/app/components/admin/SuccessMessage";
import TabNav from "@/app/components/admin/TabNav";

// Color palette options
const colorOptions = [
  { id: "blue", label: "Blue", value: "#3B82F6", previewClass: "bg-blue-500" },
  { id: "indigo", label: "Indigo", value: "#6366F1", previewClass: "bg-indigo-500" },
  { id: "purple", label: "Purple", value: "#8B5CF6", previewClass: "bg-purple-500" },
  { id: "pink", label: "Pink", value: "#EC4899", previewClass: "bg-pink-500" },
  { id: "red", label: "Red", value: "#EF4444", previewClass: "bg-red-500" },
  { id: "orange", label: "Orange", value: "#F97316", previewClass: "bg-orange-500" },
  { id: "green", label: "Green", value: "#10B981", previewClass: "bg-green-500" },
  { id: "teal", label: "Teal", value: "#14B8A6", previewClass: "bg-teal-500" },
];

export default function AppearanceSettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("theme");
  
  // Theme settings state
  const [themeSettings, setThemeSettings] = useState({
    primaryColor: "indigo",
    mode: "system",
    borderRadius: "medium",
    density: "comfortable",
    customCss: "",
  });
  
  // Logo states
  const [logo, setLogo] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>("/logo-placeholder.png");
  const [favicon, setFavicon] = useState<File | null>(null);
  const [faviconPreview, setFaviconPreview] = useState<string | null>("/logo-placeholder.png");
  
  // Save settings
  const saveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      setShowSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
    }, 1000);
  };

  // Handle logo upload
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setLogo(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle favicon upload
  const handleFaviconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFavicon(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setFaviconPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Clear logo
  const clearLogo = () => {
    setLogo(null);
    setLogoPreview("/logo-placeholder.png");
  };
  
  // Clear favicon
  const clearFavicon = () => {
    setFavicon(null);
    setFaviconPreview("/logo-placeholder.png");
  };
  
  // Handle form field changes
  const handleThemeChange = (field: string, value: string) => {
    setThemeSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Define tabs for the tab navigation
  const tabs = [
    { id: "theme", label: "Theme" },
    { id: "branding", label: "Branding" },
    { id: "custom", label: "Customization" },
    { id: "layout", label: "Layout" },
  ];

  return (
    <div>
      {/* Header */}
      <SettingsHeader 
        title="Appearance" 
        description="Configure application themes, branding, and visual customizations"
        actions={
          <SaveButton isLoading={isSaving} onClick={saveSettings} />
        }
      />
      
      {/* Success Message */}
      {showSuccess && (
        <SuccessMessage message="Appearance settings have been successfully saved." />
      )}
      
      {/* Tabs */}
      <TabNav tabs={tabs} activeTab={activeTab} onChange={setActiveTab} />
      
      {/* Theme Tab */}
      {activeTab === "theme" && (
        <SettingsSection 
          title="Theme Settings" 
          description="Configure the visual theme of your application"
        >
          <FormField 
            label="Color Scheme" 
            helper="Select a primary color for your application"
          >
            <div className="grid grid-cols-4 gap-3">
              {colorOptions.map((color) => (
                <div 
                  key={color.id}
                  className={`
                    flex flex-col items-center border rounded-lg p-3 cursor-pointer transition-all
                    ${themeSettings.primaryColor === color.id 
                      ? 'ring-2 ring-offset-2 ring-indigo-500 border-indigo-200' 
                      : 'hover:border-gray-300 border-gray-200'
                    }
                  `}
                  onClick={() => handleThemeChange('primaryColor', color.id)}
                >
                  <div className={`w-8 h-8 rounded-full mb-2 ${color.previewClass}`}></div>
                  <span className="text-sm">{color.label}</span>
                  {themeSettings.primaryColor === color.id && (
                    <Check className="absolute h-4 w-4 text-white top-1 right-1" />
                  )}
                </div>
              ))}
            </div>
          </FormField>
          
          <FormField 
            label="Display Mode" 
            helper="Select the color mode for your application"
          >
            <div className="grid grid-cols-3 gap-3">
              <div 
                className={`
                  flex flex-col items-center justify-center border rounded-lg p-4 cursor-pointer transition-all
                  ${themeSettings.mode === 'light' 
                    ? 'ring-2 ring-offset-2 ring-indigo-500 border-indigo-200' 
                    : 'hover:border-gray-300 border-gray-200'
                  }
                `}
                onClick={() => handleThemeChange('mode', 'light')}
              >
                <Sun className="h-8 w-8 text-amber-500 mb-2" />
                <span className="text-sm font-medium">Light Mode</span>
              </div>
              
              <div 
                className={`
                  flex flex-col items-center justify-center border rounded-lg p-4 cursor-pointer transition-all
                  ${themeSettings.mode === 'dark' 
                    ? 'ring-2 ring-offset-2 ring-indigo-500 border-indigo-200' 
                    : 'hover:border-gray-300 border-gray-200'
                  }
                `}
                onClick={() => handleThemeChange('mode', 'dark')}
              >
                <Moon className="h-8 w-8 text-slate-700 mb-2" />
                <span className="text-sm font-medium">Dark Mode</span>
              </div>
              
              <div 
                className={`
                  flex flex-col items-center justify-center border rounded-lg p-4 cursor-pointer transition-all
                  ${themeSettings.mode === 'system' 
                    ? 'ring-2 ring-offset-2 ring-indigo-500 border-indigo-200' 
                    : 'hover:border-gray-300 border-gray-200'
                  }
                `}
                onClick={() => handleThemeChange('mode', 'system')}
              >
                <Monitor className="h-8 w-8 text-gray-600 mb-2" />
                <span className="text-sm font-medium">System Preference</span>
              </div>
            </div>
          </FormField>
          
          <FormField 
            label="Border Radius" 
            helper="Set the roundness of UI elements"
          >
            <div className="flex items-center space-x-2">
              <input
                type="range"
                min="0"
                max="4"
                step="1"
                value={
                  themeSettings.borderRadius === "none" ? 0 :
                  themeSettings.borderRadius === "small" ? 1 :
                  themeSettings.borderRadius === "medium" ? 2 :
                  themeSettings.borderRadius === "large" ? 3 : 4
                }
                onChange={(e) => {
                  const val = parseInt(e.target.value);
                  let radius = "medium";
                  if (val === 0) radius = "none";
                  else if (val === 1) radius = "small";
                  else if (val === 2) radius = "medium";
                  else if (val === 3) radius = "large";
                  else radius = "full";
                  handleThemeChange('borderRadius', radius);
                }}
                className="w-full"
              />
              <span className="text-sm ml-2 w-24">
                {themeSettings.borderRadius.charAt(0).toUpperCase() + themeSettings.borderRadius.slice(1)}
              </span>
            </div>
            <div className="flex items-center justify-between mt-4">
              <div className={`w-16 h-16 bg-gray-200 border border-gray-300 rounded-none`}>None</div>
              <div className={`w-16 h-16 bg-gray-200 border border-gray-300 rounded-sm`}>Small</div>
              <div className={`w-16 h-16 bg-gray-200 border border-gray-300 rounded-md`}>Medium</div>
              <div className={`w-16 h-16 bg-gray-200 border border-gray-300 rounded-lg`}>Large</div>
              <div className={`w-16 h-16 bg-gray-200 border border-gray-300 rounded-full`}>Full</div>
            </div>
          </FormField>
          
          <FormField 
            label="UI Density" 
            helper="Control the spacing and density of UI elements"
          >
            <select 
              className="w-full p-2 border border-gray-300 rounded-md"
              value={themeSettings.density}
              onChange={(e) => handleThemeChange('density', e.target.value)}
            >
              <option value="compact">Compact - Less whitespace</option>
              <option value="comfortable">Comfortable - Balanced spacing</option>
              <option value="spacious">Spacious - More whitespace</option>
            </select>
          </FormField>
        </SettingsSection>
      )}
      
      {/* Branding Tab */}
      {activeTab === "branding" && (
        <SettingsSection 
          title="Branding Settings" 
          description="Configure your organization's branding elements"
        >
          <FormField 
            label="Application Logo" 
            helper="Upload your main application logo (recommended size: 200x60 px)"
          >
            <div className="flex flex-col space-y-3">
              {logoPreview && (
                <div className="relative inline-block">
                  <img 
                    src={logoPreview} 
                    alt="Logo Preview" 
                    className="h-16 object-contain border rounded p-2 bg-white"
                  />
                  <button 
                    onClick={clearLogo}
                    className="absolute -top-2 -right-2 bg-red-100 text-red-600 rounded-full p-1 hover:bg-red-200"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <label className="cursor-pointer bg-indigo-50 text-indigo-600 px-3 py-2 rounded hover:bg-indigo-100 transition-colors flex items-center">
                  <Upload className="h-4 w-4 mr-2" />
                  <span>Upload Logo</span>
                  <input 
                    type="file" 
                    className="hidden" 
                    accept="image/*"
                    onChange={handleLogoChange}
                  />
                </label>
                {logo && <span className="text-sm text-gray-500">{logo.name}</span>}
              </div>
            </div>
          </FormField>
          
          <FormField 
            label="Favicon" 
            helper="Upload your site favicon (recommended size: 32x32 px)"
          >
            <div className="flex flex-col space-y-3">
              {faviconPreview && (
                <div className="relative inline-block">
                  <img 
                    src={faviconPreview} 
                    alt="Favicon Preview" 
                    className="h-8 w-8 object-contain border rounded p-1 bg-white"
                  />
                  <button 
                    onClick={clearFavicon}
                    className="absolute -top-2 -right-2 bg-red-100 text-red-600 rounded-full p-1 hover:bg-red-200"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <label className="cursor-pointer bg-indigo-50 text-indigo-600 px-3 py-2 rounded hover:bg-indigo-100 transition-colors flex items-center">
                  <Upload className="h-4 w-4 mr-2" />
                  <span>Upload Favicon</span>
                  <input 
                    type="file" 
                    className="hidden" 
                    accept="image/*"
                    onChange={handleFaviconChange}
                  />
                </label>
                {favicon && <span className="text-sm text-gray-500">{favicon.name}</span>}
              </div>
            </div>
          </FormField>
          
          <FormField 
            label="Brand Name" 
            helper="The name of your application or company"
          >
            <input
              type="text"
              className="w-full p-2 border border-gray-300 rounded-md"
              defaultValue="Visa Mentor"
            />
          </FormField>
          
          <FormField 
            label="Footer Text" 
            helper="Text displayed in the application footer"
          >
            <textarea
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={2}
              defaultValue="© 2023 Visa Mentor. All rights reserved."
            />
          </FormField>
        </SettingsSection>
      )}
      
      {/* Custom CSS Tab */}
      {activeTab === "custom" && (
        <SettingsSection 
          title="Custom CSS" 
          description="Add custom CSS to override default styles"
        >
          <FormField 
            label="Custom CSS" 
            helper="Add custom CSS rules to customize your application's appearance"
          >
            <div className="font-mono">
              <textarea
                className="w-full p-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"
                rows={12}
                value={themeSettings.customCss}
                onChange={(e) => handleThemeChange('customCss', e.target.value)}
                placeholder={`/* Example custom CSS */
.header {
  background-color: #f8f9fa;
}
.button-primary {
  background-color: #6366F1;
  color: white;
}`}
              />
            </div>
          </FormField>
          
          <FormField 
            label="Preview" 
            helper="See how your custom CSS affects the application"
          >
            <button className="bg-indigo-50 text-indigo-600 px-4 py-2 rounded-md flex items-center gap-2 hover:bg-indigo-100">
              <Eye className="h-4 w-4" />
              Preview Custom CSS
            </button>
          </FormField>
        </SettingsSection>
      )}
      
      {/* Layout Tab */}
      {activeTab === "layout" && (
        <SettingsSection 
          title="Layout Settings" 
          description="Configure the layout of your application"
        >
          <FormField 
            label="Navigation Style" 
            helper="Choose the style of the main navigation"
          >
            <div className="grid grid-cols-2 gap-4">
              <div className="border rounded-lg p-3 cursor-pointer hover:border-indigo-200">
                <div className="flex h-32 border-2 border-gray-300 rounded mb-2 overflow-hidden">
                  <div className="bg-gray-100 w-16 h-full border-r-2 border-gray-300"></div>
                  <div className="flex-1 flex flex-col">
                    <div className="h-8 bg-gray-100 border-b-2 border-gray-300"></div>
                    <div className="flex-1 bg-white p-2">
                      <div className="h-4 w-3/4 bg-gray-100 rounded mb-2"></div>
                      <div className="h-4 w-1/2 bg-gray-100 rounded"></div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Sidebar Navigation</span>
                  <input type="radio" name="navStyle" defaultChecked className="h-4 w-4 text-indigo-600" />
                </div>
              </div>
              
              <div className="border rounded-lg p-3 cursor-pointer hover:border-indigo-200">
                <div className="flex flex-col h-32 border-2 border-gray-300 rounded mb-2 overflow-hidden">
                  <div className="h-8 bg-gray-100 border-b-2 border-gray-300"></div>
                  <div className="flex-1 bg-white p-2">
                    <div className="h-4 w-3/4 bg-gray-100 rounded mb-2"></div>
                    <div className="h-4 w-1/2 bg-gray-100 rounded"></div>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Top Navigation</span>
                  <input type="radio" name="navStyle" className="h-4 w-4 text-indigo-600" />
                </div>
              </div>
            </div>
          </FormField>
          
          <FormField 
            label="Container Width" 
            helper="Set the maximum width of the content container"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="sm">Small (640px)</option>
              <option value="md">Medium (768px)</option>
              <option value="lg">Large (1024px)</option>
              <option value="xl" selected>Extra Large (1280px)</option>
              <option value="2xl">2X Large (1536px)</option>
              <option value="full">Full Width</option>
            </select>
          </FormField>
          
          <FormField 
            label="Dashboard Layout" 
            helper="Set the default layout for the dashboard"
          >
            <select className="w-full p-2 border border-gray-300 rounded-md">
              <option value="grid">Grid Layout</option>
              <option value="list">List Layout</option>
              <option value="compact">Compact Layout</option>
              <option value="tabbed">Tabbed Layout</option>
            </select>
          </FormField>
        </SettingsSection>
      )}
    </div>
  );
} 