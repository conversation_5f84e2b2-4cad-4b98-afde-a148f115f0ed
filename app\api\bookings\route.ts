import { NextResponse } from 'next/server';

// Mock bookings data
const mockBookings = [
  {
    id: "book1",
    name: "Visa Consultation",
    email: "<EMAIL>",
    phone: "+****************",
    country: "Canada",
    visaType: "Tourist",
    date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
    time: "10:00 AM",
    notes: "First time applicant with questions about documentation",
    status: "scheduled",
    createdAt: new Date().toISOString()
  },
  {
    id: "book2",
    name: "Document Review",
    email: "<EMAIL>",
    phone: "+****************",
    country: "United Kingdom",
    visaType: "Work",
    date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 14 days from now
    time: "2:30 PM",
    notes: "Follow-up on previous consultation",
    status: "scheduled",
    createdAt: new Date().toISOString()
  }
];

export async function GET() {
  // Simulate server delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return NextResponse.json(mockBookings);
}

export async function POST(request: Request) {
  const bookingData = await request.json();
  
  // In a real application, you would save this to a database
  // For now, we'll just return a success response with a fake ID
  
  return NextResponse.json({
    ...bookingData,
    id: `book${Date.now()}`,
    status: "scheduled",
    createdAt: new Date().toISOString()
  });
} 