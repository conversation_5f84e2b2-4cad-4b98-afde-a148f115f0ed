import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { UserService } from '@/lib/services/userService';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get bookings for user
    const bookings = await prisma.booking.findMany({
      where: { userId: user.id },
      orderBy: { date: 'asc' },
    });

    return NextResponse.json(bookings);
  } catch (error) {
    console.error('API: Error fetching bookings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await UserService.getUserByClerkId(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const bookingData = await request.json();

    if (!bookingData.serviceType || !bookingData.date || !bookingData.time) {
      return NextResponse.json({
        error: 'Service type, date, and time are required'
      }, { status: 400 });
    }

    // Create booking in database
    const booking = await prisma.booking.create({
      data: {
        userId: user.id,
        serviceType: bookingData.serviceType,
        date: new Date(bookingData.date),
        time: bookingData.time,
        duration: bookingData.duration || 60,
        counselorName: bookingData.counselorName,
        meetingType: bookingData.meetingType || 'video',
        notes: bookingData.notes,
        price: bookingData.price ? parseFloat(bookingData.price) : null,
      },
    });

    return NextResponse.json(booking, { status: 201 });
  } catch (error) {
    console.error('API: Error creating booking:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}