import { NextRequest, NextResponse } from "next/server";

// Mock conversation data - in a real app, this would be stored in a database
interface MockUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  status: "online" | "offline" | "busy";
  lastActive: string;
}

interface MockConversation {
  id: string;
  participants: string[]; // user IDs
  lastMessage: {
    id: string;
    senderId: string;
    text: string;
    timestamp: string;
    read: boolean;
  };
  unreadCount: number;
}

// Mock users
const mockUsers: Record<string, MockUser> = {
  "user-001": {
    id: "user-001",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    role: "client",
    status: "online",
    lastActive: "Just now"
  },
  "user-002": {
    id: "user-002",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    role: "client",
    status: "offline",
    lastActive: "3 hours ago"
  },
  "user-003": {
    id: "user-003",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    role: "client",
    status: "busy",
    lastActive: "Yesterday"
  },
  "user-004": {
    id: "user-004",
    name: "Meera <PERSON>",
    email: "<EMAIL>",
    role: "client",
    status: "offline",
    lastActive: "2 days ago"
  },
  "counselor-001": {
    id: "counselor-001",
    name: "Priya Sharma",
    email: "<EMAIL>",
    role: "counselor",
    status: "online",
    lastActive: "Just now"
  }
};

// Mock conversations
const mockConversations: MockConversation[] = [
  {
    id: "conv-001",
    participants: ["counselor-001", "user-001"],
    lastMessage: {
      id: "msg-007",
      senderId: "user-001",
      text: "And also my CV. Can you please review it?",
      timestamp: "2023-05-18T10:32:00",
      read: false
    },
    unreadCount: 2
  },
  {
    id: "conv-002",
    participants: ["counselor-001", "user-002"],
    lastMessage: {
      id: "msg-104",
      senderId: "user-002",
      text: "When is my interview scheduled?",
      timestamp: "2023-05-17T16:45:00",
      read: true
    },
    unreadCount: 0
  },
  {
    id: "conv-003",
    participants: ["counselor-001", "user-003"],
    lastMessage: {
      id: "msg-204",
      senderId: "user-003",
      text: "Thank you for your help with the documents",
      timestamp: "2023-05-17T13:20:00",
      read: true
    },
    unreadCount: 0
  },
  {
    id: "conv-004",
    participants: ["counselor-001", "user-004"],
    lastMessage: {
      id: "msg-304",
      senderId: "user-004",
      text: "Do I need any additional documents?",
      timestamp: "2023-05-16T10:15:00",
      read: true
    },
    unreadCount: 0
  }
];

// Mock messages for each conversation
const mockMessages: Record<string, Array<{
  id: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  text: string;
  timestamp: string;
  status: 'sent' | 'delivered' | 'read';
  attachments?: Array<{name: string, url: string, type: string}>;
}>> = {
  "conv-001": [
    {
      id: "msg-001",
      conversationId: "conv-001",
      senderId: "counselor-001",
      receiverId: "user-001",
      text: "Hello Aditya, I've reviewed your profile and you're an excellent candidate for the EB-1 visa. Let's prepare your documentation.",
      timestamp: "2023-05-17T09:30:00",
      status: "read"
    },
    {
      id: "msg-002",
      conversationId: "conv-001",
      senderId: "user-001",
      receiverId: "counselor-001",
      text: "Thank you for the positive feedback! I'm excited to proceed. What documents do I need to prepare first?",
      timestamp: "2023-05-17T09:40:00",
      status: "read"
    },
    {
      id: "msg-003",
      conversationId: "conv-001",
      senderId: "counselor-001",
      receiverId: "user-001",
      text: "For the EB-1 visa, we need to demonstrate your extraordinary ability. Please prepare your CV, publication list, citation records, and recommendation letters from experts in your field.",
      timestamp: "2023-05-17T09:45:00",
      status: "read"
    },
    {
      id: "msg-004",
      conversationId: "conv-001",
      senderId: "user-001",
      receiverId: "counselor-001",
      text: "I have my CV and publication list ready. I'll work on getting the recommendation letters. How many letters would be ideal?",
      timestamp: "2023-05-17T10:00:00",
      status: "read"
    },
    {
      id: "msg-005",
      conversationId: "conv-001",
      senderId: "counselor-001",
      receiverId: "user-001",
      text: "Aim for at least 5-6 recommendation letters from peers or superiors who can speak to your contributions to the field. Quality is more important than quantity though.",
      timestamp: "2023-05-17T10:05:00",
      status: "read"
    },
    {
      id: "msg-006",
      conversationId: "conv-001",
      senderId: "user-001",
      receiverId: "counselor-001",
      text: "I've uploaded my passport documents",
      timestamp: "2023-05-18T10:30:00",
      status: "delivered"
    },
    {
      id: "msg-007",
      conversationId: "conv-001",
      senderId: "user-001",
      receiverId: "counselor-001",
      text: "And also my CV. Can you please review it?",
      timestamp: "2023-05-18T10:32:00",
      status: "delivered",
      attachments: [{
        name: "Aditya_Mehta_CV.pdf",
        url: "/documents/cv.pdf",
        type: "application/pdf"
      }]
    }
  ],
  "conv-002": [
    {
      id: "msg-101",
      conversationId: "conv-002",
      senderId: "counselor-001",
      receiverId: "user-002",
      text: "Hi Sneha, congratulations on your acceptance to Stanford! Let's prepare for your student visa interview.",
      timestamp: "2023-05-16T14:00:00",
      status: "read"
    },
    {
      id: "msg-102",
      conversationId: "conv-002",
      senderId: "user-002",
      receiverId: "counselor-001",
      text: "Thank you! I'm a bit nervous about the interview. What kind of questions should I expect?",
      timestamp: "2023-05-16T14:10:00",
      status: "read"
    },
    {
      id: "msg-103",
      conversationId: "conv-002",
      senderId: "counselor-001",
      receiverId: "user-002",
      text: "Don't worry, we'll prepare you thoroughly. They typically ask about your course, why you chose this university, your future plans, and how you'll fund your education.",
      timestamp: "2023-05-16T14:15:00",
      status: "read"
    },
    {
      id: "msg-104",
      conversationId: "conv-002",
      senderId: "user-002",
      receiverId: "counselor-001",
      text: "When is my interview scheduled?",
      timestamp: "2023-05-17T16:45:00",
      status: "read"
    }
  ],
  "conv-003": [
    {
      id: "msg-201",
      conversationId: "conv-003",
      senderId: "counselor-001",
      receiverId: "user-003",
      text: "Hello Rajat, I've reviewed your H1-B application and everything looks good so far. We need to finalize your employer documentation.",
      timestamp: "2023-05-15T11:00:00",
      status: "read"
    },
    {
      id: "msg-202",
      conversationId: "conv-003",
      senderId: "user-003",
      receiverId: "counselor-001",
      text: "Great to hear that! My employer has sent me the job offer letter and employment contract. I'll upload them now.",
      timestamp: "2023-05-15T11:15:00",
      status: "read",
      attachments: [
        {
          name: "Job_Offer_Letter.pdf",
          url: "/documents/offer.pdf",
          type: "application/pdf"
        },
        {
          name: "Employment_Contract.pdf",
          url: "/documents/contract.pdf",
          type: "application/pdf"
        }
      ]
    },
    {
      id: "msg-203",
      conversationId: "conv-003",
      senderId: "counselor-001",
      receiverId: "user-003",
      text: "Perfect! I've received the documents and they look excellent. Let me know if you have any questions about the next steps.",
      timestamp: "2023-05-15T11:30:00",
      status: "read"
    },
    {
      id: "msg-204",
      conversationId: "conv-003",
      senderId: "user-003",
      receiverId: "counselor-001",
      text: "Thank you for your help with the documents",
      timestamp: "2023-05-17T13:20:00",
      status: "read"
    }
  ],
  "conv-004": [
    {
      id: "msg-301",
      conversationId: "conv-004",
      senderId: "counselor-001",
      receiverId: "user-004",
      text: "Hi Meera, I've reviewed your profile and I believe you have a strong case for an O-1 visa given your artistic achievements.",
      timestamp: "2023-05-14T15:30:00",
      status: "read"
    },
    {
      id: "msg-302",
      conversationId: "conv-004",
      senderId: "user-004",
      receiverId: "counselor-001",
      text: "Thank you, I'm glad to hear that. What kind of evidence would I need to provide for my achievements?",
      timestamp: "2023-05-14T15:45:00",
      status: "read"
    },
    {
      id: "msg-303",
      conversationId: "conv-004",
      senderId: "counselor-001",
      receiverId: "user-004",
      text: "For an O-1 visa, we need to demonstrate your extraordinary ability in the arts. This includes press coverage, awards, exhibition catalogs, and testimonials from experts in your field.",
      timestamp: "2023-05-14T16:00:00",
      status: "read"
    },
    {
      id: "msg-304",
      conversationId: "conv-004",
      senderId: "user-004",
      receiverId: "counselor-001",
      text: "Do I need any additional documents?",
      timestamp: "2023-05-16T10:15:00",
      status: "read"
    }
  ]
};

// Get a user's active conversations
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const userId = searchParams.get("userId") || "counselor-001"; // Default to counselor
  const conversationId = searchParams.get("conversationId");
  
  try {
    // If conversationId is provided, return that specific conversation's messages
    if (conversationId) {
      const conversation = mockConversations.find(c => c.id === conversationId);
      if (!conversation) {
        return NextResponse.json({ error: "Conversation not found" }, { status: 404 });
      }
      
      // Check if the user is a participant
      if (!conversation.participants.includes(userId)) {
        return NextResponse.json({ error: "Unauthorized access to conversation" }, { status: 403 });
      }
      
      const messages = mockMessages[conversationId] || [];
      const otherParticipantId = conversation.participants.find(id => id !== userId);
      const otherParticipant = otherParticipantId ? mockUsers[otherParticipantId] : null;
      
      return NextResponse.json({
        conversation: {
          id: conversation.id,
          participant: otherParticipant,
          messages
        }
      });
    }
    
    // Otherwise, return all conversations for the user
    const userConversations = mockConversations
      .filter(conv => conv.participants.includes(userId))
      .map(conv => {
        // Find the other participant in the conversation
        const otherParticipantId = conv.participants.find(id => id !== userId);
        const otherParticipant = otherParticipantId ? mockUsers[otherParticipantId] : null;
        
        // Return formatted conversation
        return {
          id: conv.id,
          participant: otherParticipant,
          lastMessage: conv.lastMessage,
          unreadCount: conv.unreadCount
        };
      });
    
    return NextResponse.json({ conversations: userConversations });
  } catch (error) {
    console.error("Error fetching conversations:", error);
    return NextResponse.json({ error: "Failed to fetch conversations" }, { status: 500 });
  }
}

// Send a message to a conversation
export async function POST(req: NextRequest) {
  try {
    const { conversationId, senderId, receiverId, text, attachments = [] } = await req.json();
    
    if (!conversationId || !senderId || !receiverId || !text) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }
    
    // Check if conversation exists
    const conversation = mockConversations.find(c => c.id === conversationId);
    if (!conversation) {
      return NextResponse.json({ error: "Conversation not found" }, { status: 404 });
    }
    
    // Check if users are participants
    if (!conversation.participants.includes(senderId) || !conversation.participants.includes(receiverId)) {
      return NextResponse.json({ error: "Invalid participants" }, { status: 400 });
    }
    
    // Create a new message
    const newMessage = {
      id: `msg-${Date.now()}`,
      conversationId,
      senderId,
      receiverId,
      text,
      timestamp: new Date().toISOString(),
      status: 'sent' as const,
      attachments: attachments.length > 0 ? attachments : undefined
    };
    
    // Add message to the conversation
    if (!mockMessages[conversationId]) {
      mockMessages[conversationId] = [];
    }
    mockMessages[conversationId].push(newMessage);
    
    // Update the last message for the conversation
    conversation.lastMessage = {
      id: newMessage.id,
      senderId: newMessage.senderId,
      text: newMessage.text,
      timestamp: newMessage.timestamp,
      read: false
    };
    
    // If the sender is not the receiver, update unread count
    if (senderId !== receiverId) {
      // The receiver will have unread messages
      const receiverIndex = mockConversations.findIndex(
        c => c.id === conversationId && c.participants.includes(receiverId)
      );
      
      if (receiverIndex !== -1) {
        mockConversations[receiverIndex].unreadCount += 1;
      }
    }
    
    return NextResponse.json({ message: newMessage });
  } catch (error) {
    console.error("Error sending message:", error);
    return NextResponse.json({ error: "Failed to send message" }, { status: 500 });
  }
}

// Mark messages as read
export async function PATCH(req: NextRequest) {
  try {
    const { conversationId, userId } = await req.json();
    
    if (!conversationId || !userId) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }
    
    // Check if conversation exists
    const conversationIndex = mockConversations.findIndex(c => c.id === conversationId);
    if (conversationIndex === -1) {
      return NextResponse.json({ error: "Conversation not found" }, { status: 404 });
    }
    
    // Check if user is a participant
    if (!mockConversations[conversationIndex].participants.includes(userId)) {
      return NextResponse.json({ error: "User is not a participant" }, { status: 403 });
    }
    
    // Mark all messages as read for this user
    if (mockMessages[conversationId]) {
      mockMessages[conversationId] = mockMessages[conversationId].map(msg => {
        if (msg.receiverId === userId && msg.senderId !== userId) {
          return { ...msg, status: 'read' as const };
        }
        return msg;
      });
    }
    
    // Reset unread count for the user
    mockConversations[conversationIndex].unreadCount = 0;
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error marking messages as read:", error);
    return NextResponse.json({ error: "Failed to mark messages as read" }, { status: 500 });
  }
} 