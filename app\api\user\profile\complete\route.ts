import { NextResponse } from 'next/server';

// POST /api/user/profile/complete - Mark the user's profile as complete
export async function POST(request: Request) {
  try {
    const body = await request.json();

    // Authentication removed - return mock completed profile
    const completedProfile = {
      id: "mock-user-id",
      email: "<EMAIL>",
      name: "Demo User",
      profileCompleted: true,
      profileProgress: 100,
      ...body,
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      message: 'Profile completed successfully!',
      user: completedProfile
    });
  } catch (error) {
    console.error('API: Error completing profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}