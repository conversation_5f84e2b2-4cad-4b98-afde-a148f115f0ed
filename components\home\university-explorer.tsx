"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Search, Building, MapPin, CreditCard } from "lucide-react"
import { ReactNode } from "react"

interface GlassCardProps {
  children: ReactNode;
  className?: string;
}

function GlassCard({ children, className = "" }: GlassCardProps) {
  return (
    <div
      className={`rounded-xl p-4 shadow-lg backdrop-blur-lg transform transition-all duration-300 hover:shadow-xl ${className}`}
      style={{
        background: "rgba(255, 255, 255, 0.8)",
        backdropFilter: "blur(12px)",
        border: "1px solid rgba(255, 255, 255, 0.25)",
        boxShadow: "rgba(142, 142, 242, 0.1) 0px 8px 24px"
      }}
    >
      {children}
    </div>
  )
}

export default function UniversityExplorer() {
  const [searchQuery, setSearchQuery] = useState("")
  const [country, setCountry] = useState("")

  const universities = [
    {
      id: 1,
      name: "Stanford University",
      country: "USA",
      avgSalary: "$145,559",
      tuition: "$59,322",
      image: "/placeholder.svg?height=80&width=80",
    },
    {
      id: 2,
      name: "University of Toronto",
      country: "Canada",
      avgSalary: "$101,087",
      tuition: "$54,596",
      image: "/placeholder.svg?height=80&width=80",
    },
    {
      id: 3,
      name: "University of Oxford",
      country: "UK",
      avgSalary: "$35,245",
      tuition: "$26,497",
      image: "/placeholder.svg?height=80&width=80",
    },
  ]

  const filteredUniversities = universities.filter(
    (uni) => uni.name.toLowerCase().includes(searchQuery.toLowerCase()) && (country === "" || uni.country === country),
  )

  return (
    <section className="py-16">
      <div className="container max-w-7xl mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-6 text-indigo-800">University Explorer</h2>
        <p className="text-center text-sm text-gray-600 mb-8">
          Search and compare universities by country, program, tuition, and outcomes.
        </p>

        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-indigo-50/50 rounded-lg p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search universities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full border border-indigo-300 rounded-lg bg-white"
              />
            </div>
            <div className="w-full md:w-48">
              <Select value={country} onValueChange={setCountry}>
                  <SelectTrigger className="border border-indigo-300 rounded-lg bg-white text-indigo-800">
                  <SelectValue placeholder="Country" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Countries</SelectItem>
                  <SelectItem value="USA">USA</SelectItem>
                  <SelectItem value="UK">UK</SelectItem>
                  <SelectItem value="Canada">Canada</SelectItem>
                  <SelectItem value="Australia">Australia</SelectItem>
                  <SelectItem value="Germany">Germany</SelectItem>
                </SelectContent>
              </Select>
            </div>
              <Button className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-lg py-3 px-4 font-medium hover:from-blue-700 hover:to-indigo-800 transition shadow-md flex items-center">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUniversities.map((uni) => (
            <GlassCard key={uni.id} className="transform transition hover:translate-y-[-4px]">
              <div className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden bg-indigo-50/50 flex-shrink-0 flex items-center justify-center">
                    <Building className="w-8 h-8 text-indigo-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-indigo-800">{uni.name}</h3>
                    <div className="flex items-center text-xs text-indigo-600">
                      <MapPin className="w-3 h-3 mr-1" />
                      {uni.country}
                    </div>
                  </div>
                </div>
                <div className="bg-indigo-50/50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                      <p className="text-xs text-gray-600">Avg. Salary</p>
                      <p className="text-sm font-medium text-indigo-800">{uni.avgSalary}</p>
                  </div>
                  <div>
                      <p className="text-xs text-gray-600">Tuition</p>
                      <p className="text-sm font-medium text-indigo-800 flex items-center">
                        <CreditCard className="w-3 h-3 mr-1" />
                        {uni.tuition}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </GlassCard>
          ))}
        </div>
      </div>
    </section>
  )
}
