import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// Define types for our data structure
type TouristAttraction = {
  name: string;
  type: string; // museum, landmark, park, etc.
  image: string;
  address: string;
  description: string;
  historicalSignificance: string;
  bestTimeToVisit: string;
  entranceFee: string; // Free or price range
  openingHours: string;
  website?: string;
}

type CountryData = {
  attractions: TouristAttraction[];
  localTips: string;
  weather: string;
  safety: string;
  bestTime: string;
}

type DestinationDataType = {
  [key: string]: CountryData;
}

// Sample data for different destinations
const destinationData: DestinationDataType = {
  "US": {
    attractions: [
      {
        name: "Golden Gate Bridge",
        type: "Landmark",
        image: "https://images.unsplash.com/photo-1501594907352-04cda38ebc29",
        address: "Golden Gate Bridge, San Francisco, CA",
        description: "The iconic 1.7-mile-long suspension bridge connecting San Francisco to Marin County.",
        historicalSignificance: "Completed in 1937, it was the longest suspension bridge in the world at the time and is considered one of the most photographed bridges in the world.",
        bestTimeToVisit: "Early morning or late afternoon for the best lighting and fewer crowds.",
        entranceFee: "Free for pedestrians and cyclists",
        openingHours: "Pedestrian access: 5:00 AM to 9:00 PM",
        website: "https://www.goldengate.org/"
      },
      {
        name: "Smithsonian National Air and Space Museum",
        type: "Museum",
        image: "https://images.unsplash.com/photo-1529307474719-3d0a417aaf8a",
        address: "600 Independence Ave SW, Washington, DC 20560",
        description: "Home to the world's largest collection of aviation and space artifacts.",
        historicalSignificance: "Preserves the history of human flight and space exploration, housing iconic aircraft like the Wright Flyer and Apollo 11 command module.",
        bestTimeToVisit: "Weekday mornings to avoid crowds",
        entranceFee: "Free",
        openingHours: "10:00 AM to 5:30 PM daily",
        website: "https://airandspace.si.edu/"
      },
      {
        name: "Grand Canyon National Park",
        type: "Natural Wonder",
        image: "https://images.unsplash.com/photo-1615551043360-33de8b5f410c",
        address: "Grand Canyon Village, AZ 86023",
        description: "A massive canyon carved by the Colorado River, known for its overwhelming size and colorful landscape.",
        historicalSignificance: "Formed over 6 million years, it contains rock layers that provide an unparalleled record of Earth's geological history.",
        bestTimeToVisit: "Spring or fall for moderate temperatures",
        entranceFee: "$35 per vehicle, valid for 7 days",
        openingHours: "South Rim open 24/7 year-round",
        website: "https://www.nps.gov/grca/"
      }
    ],
    localTips: "National Parks require advance reservations during peak seasons. Carry water when hiking in desert areas. Many museums in Washington DC are free.",
    weather: "Varies greatly by region. Southwest is hot and dry, Northeast experiences all four seasons, and Pacific Northwest is often rainy.",
    safety: "Most tourist areas are safe, but always be aware of your surroundings in major cities.",
    bestTime: "Spring and fall offer pleasant weather in most regions"
  },
  "GB": {
    attractions: [
      {
        name: "British Museum",
        type: "Museum",
        image: "https://images.unsplash.com/photo-1605972088136-e4e0312b3c53",
        address: "Great Russell St, Bloomsbury, London WC1B 3DG",
        description: "One of the world's oldest and most comprehensive museums, dedicated to human history, art, and culture.",
        historicalSignificance: "Houses over 8 million works from all continents, documenting human culture from its beginnings to the present.",
        bestTimeToVisit: "Weekday afternoons",
        entranceFee: "Free (donations appreciated)",
        openingHours: "10:00 AM to 5:00 PM daily, Fridays until 8:30 PM",
        website: "https://www.britishmuseum.org/"
      },
      {
        name: "Stonehenge",
        type: "Ancient Monument",
        image: "https://images.unsplash.com/photo-1599833975787-5c143f373c30",
        address: "Salisbury SP4 7DE, UK",
        description: "Prehistoric stone circle monument, cemetery, and archaeological site.",
        historicalSignificance: "Built around 2500 BCE, Stonehenge is one of the most famous prehistoric monuments and a UNESCO World Heritage site.",
        bestTimeToVisit: "Early morning or evening, especially during summer solstice",
        entranceFee: "£22.50 for adults, free for English Heritage members",
        openingHours: "9:30 AM to 5:00 PM (hours vary seasonally)",
        website: "https://www.english-heritage.org.uk/visit/places/stonehenge/"
      },
      {
        name: "Edinburgh Castle",
        type: "Historic Fortress",
        image: "https://images.unsplash.com/photo-1564631027894-5836c05094ab",
        address: "Castlehill, Edinburgh EH1 2NG, UK",
        description: "Historic fortress dominating the skyline of Edinburgh from its position on Castle Rock.",
        historicalSignificance: "Has served as a royal residence, military garrison, prison, and fortress since at least the 12th century.",
        bestTimeToVisit: "Early morning to avoid crowds",
        entranceFee: "£18 for adults",
        openingHours: "9:30 AM to 5:00 PM daily",
        website: "https://www.edinburghcastle.scot/"
      }
    ],
    localTips: "Many museums in London are free. Look right first when crossing streets. Consider an Oyster card for public transport in London.",
    weather: "Generally mild but unpredictable. Always carry an umbrella. Summer temperatures rarely exceed 25°C (77°F).",
    safety: "The UK is generally safe for tourists. Take normal precautions in crowded areas and city centers.",
    bestTime: "May to September for the best weather"
  },
  "IN": {
    attractions: [
      {
        name: "Taj Mahal",
        type: "Monument",
        image: "https://images.unsplash.com/photo-1564507592333-c60657eea523",
        address: "Dharmapuri, Forest Colony, Tajganj, Agra, Uttar Pradesh 282001",
        description: "An ivory-white marble mausoleum built by Emperor Shah Jahan in memory of his favorite wife.",
        historicalSignificance: "Completed in 1643, it's considered the finest example of Mughal architecture and a UNESCO World Heritage site.",
        bestTimeToVisit: "Sunrise for the best lighting and fewer crowds",
        entranceFee: "1,100 INR for foreign tourists",
        openingHours: "Sunrise to sunset, closed on Fridays",
        website: "https://www.tajmahal.gov.in/"
      },
      {
        name: "Amber Fort",
        type: "Historic Fortress",
        image: "https://images.unsplash.com/photo-1599661046289-e31897846e41",
        address: "Devisinghpura, Amer, Jaipur, Rajasthan 302001",
        description: "A magnificent fort built from red sandstone and marble, known for its artistic Hindu style elements.",
        historicalSignificance: "Built in 1592 by Raja Man Singh I, it served as the residence of the Rajput Maharajas and their families.",
        bestTimeToVisit: "Early morning or late afternoon",
        entranceFee: "500 INR for foreign tourists",
        openingHours: "8:00 AM to 5:30 PM daily",
        website: "https://www.amer-fort.com/"
      },
      {
        name: "Varanasi Ghats",
        type: "Religious Site",
        image: "https://images.unsplash.com/photo-1561361058-c12e06f5800c",
        address: "Varanasi, Uttar Pradesh",
        description: "A series of steps leading down to the sacred Ganges River, where religious ceremonies and cremations take place.",
        historicalSignificance: "Varanasi is one of the oldest continuously inhabited cities in the world and a major religious hub for Hinduism.",
        bestTimeToVisit: "Early morning for the sunrise ceremonies (Ganga Aarti)",
        entranceFee: "Free to visit",
        openingHours: "Always open",
        website: "N/A"
      }
    ],
    localTips: "Remove shoes before entering temples. Dress modestly, especially in religious sites. Bargaining is expected in markets. Carry hand sanitizer and toilet paper.",
    weather: "Varies dramatically by region and season. October to March is generally most pleasant, avoiding summer heat and monsoon rains.",
    safety: "Exercise caution, especially at night. Women travelers should be particularly vigilant. Be aware of common scams in tourist areas.",
    bestTime: "October to March"
  }
}

// Default countries to show if user's destination isn't found
const defaultCountries = ["US", "GB", "IN", "CA", "AU"]

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const userId = searchParams.get("userId")
    const country = searchParams.get("country")
    
    if (!userId) {
      return NextResponse.json(
        { error: "Missing userId parameter" },
        { status: 400 }
      )
    }
    
    // Generate mock recommendations directly without checking database
    let targetCountry = country || defaultCountries[Math.floor(Math.random() * defaultCountries.length)]
    
    // If we don't have data for this country, use a default
    if (!destinationData[targetCountry]) {
      targetCountry = defaultCountries[0] // Default to US
    }
    
    const countryData = destinationData[targetCountry]
    const recommendations = []
    
    // Generate recommendations based on the sample data without saving to database
    for (const attraction of countryData.attractions) {
      const recommendation = {
        id: `rec_${Math.random().toString(36).substring(2, 15)}`,
        userId,
        destinationCountry: targetCountry,
        attractionName: attraction.name,
        attractionType: attraction.type,
        image: attraction.image,
        address: attraction.address,
        description: attraction.description,
        historicalSignificance: attraction.historicalSignificance,
        bestTimeToVisit: attraction.bestTimeToVisit,
        entranceFee: attraction.entranceFee,
        openingHours: attraction.openingHours,
        website: attraction.website || null,
        localTips: countryData.localTips,
        weatherInfo: countryData.weather,
        safetyTips: countryData.safety,
        isBookmarked: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      recommendations.push(recommendation)
    }
    
    return NextResponse.json(recommendations)
  } catch (error) {
    console.error("Error generating travel recommendations:", error)
    return NextResponse.json(
      { error: "Failed to generate travel recommendations" },
      { status: 500 }
    )
  }
}

// Update recommendation - toggle bookmark, etc.
export async function PATCH(req: Request) {
  try {
    const body = await req.json()
    const { id, isBookmarked } = body
    
    if (!id) {
      return NextResponse.json(
        { error: "Missing recommendation id" },
        { status: 400 }
      )
    }
    
    // Just return the data as if it was updated
    return NextResponse.json({
      id,
      isBookmarked,
      updatedAt: new Date()
    })
  } catch (error) {
    console.error("Error updating travel recommendation:", error)
    return NextResponse.json(
      { error: "Failed to update travel recommendation" },
      { status: 500 }
    )
  }
}

// Delete a recommendation
export async function DELETE(req: Request) {
  try {
    const { searchParams } = new URL(req.url)
    const id = searchParams.get("id")
    
    if (!id) {
      return NextResponse.json(
        { error: "Missing recommendation id" },
        { status: 400 }
      )
    }
    
    // Just return success
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting travel recommendation:", error)
    return NextResponse.json(
      { error: "Failed to delete travel recommendation" },
      { status: 500 }
    )
  }
} 