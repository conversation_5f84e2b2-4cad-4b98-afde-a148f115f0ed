import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Facebook, Instagram, Linkedin, Twitter } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-gray-50 border-t">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <div className="mb-4">
              <Image 
                src="/images/visa-mentor-logo.png" 
                alt="Visa Mentor Logo" 
                width={140} 
                height={35} 
                className="object-contain mb-2" 
                style={{ width: 'auto', height: 'auto' }}
                priority
              />
            </div>
            <p className="text-gray-600 mb-4">Your trusted partner for global education and immigration services.</p>
            <div className="flex space-x-4">
              <Link href="https://facebook.com" className="text-gray-500 hover:text-[#1E90FF]">
                <Facebook size={20} />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="https://twitter.com" className="text-gray-500 hover:text-[#1E90FF]">
                <Twitter size={20} />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link href="https://instagram.com" className="text-gray-500 hover:text-[#1E90FF]">
                <Instagram size={20} />
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="https://linkedin.com" className="text-gray-500 hover:text-[#1E90FF]">
                <Linkedin size={20} />
                <span className="sr-only">LinkedIn</span>
              </Link>
            </div>
          </div>

          <div>
            <h3 className="text-base font-medium text-gray-700 mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-600 hover:text-[#1E90FF]">
                  Home
                </Link>
              </li>
              <li>
                <Link href={"/services" as any} className="text-gray-600 hover:text-[#1E90FF]">
                  Services
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-600 hover:text-[#1E90FF]">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-[#1E90FF]">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/resources" className="text-gray-600 hover:text-[#1E90FF]">
                  Resources
                </Link>
              </li>
              <li>
                <Link href={"/blog" as any} className="text-gray-600 hover:text-[#1E90FF]">
                  Blog
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-base font-medium text-gray-700 mb-4">Services</h3>
            <ul className="space-y-2">
              <li>
                <Link href={"/services/study-abroad" as any} className="text-gray-600 hover:text-[#1E90FF]">
                  Study Abroad
                </Link>
              </li>
              <li>
                <Link href={"/services/work-visas" as any} className="text-gray-600 hover:text-[#1E90FF]">
                  Work Visas
                </Link>
              </li>
              <li>
                <Link href={"/services/travel-visas" as any} className="text-gray-600 hover:text-[#1E90FF]">
                  Travel Visas
                </Link>
              </li>
              <li>
                <Link href={"/services/counseling" as any} className="text-gray-600 hover:text-[#1E90FF]">
                  Counseling
                </Link>
              </li>
              <li>
                <Link href={"/services/documentation" as any} className="text-gray-600 hover:text-[#1E90FF]">
                  Documentation
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-4">Subscribe</h3>
            <p className="text-gray-600 mb-4">Stay updated with the latest visa and immigration news.</p>
            <div className="flex gap-2">
              <Input type="email" placeholder="Your email" className="bg-white" />
              <Button className="bg-[#1E90FF] hover:bg-[#0078E7]">Subscribe</Button>
            </div>
          </div>
        </div>

        <div className="border-t mt-12 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-600 text-sm flex items-center">
            © {new Date().getFullYear()} 
            <span className="mx-1">
              <Image 
                src="/images/visa-mentor-logo.png" 
                alt="Visa Mentor Logo" 
                width={80} 
                height={20} 
                className="object-contain" 
                style={{ width: 'auto', height: 'auto' }}
              />
            </span>
            All rights reserved.
          </p>
          <div className="flex gap-4 mt-4 md:mt-0">
            <Link href={"/privacy" as any} className="text-gray-600 hover:text-[#1E90FF] text-sm">
              Privacy Policy
            </Link>
            <Link href={"/terms" as any} className="text-gray-600 hover:text-[#1E90FF] text-sm">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
