import { NextRequest, NextResponse } from 'next/server'

// Mock user data store
const mockUsers = new Map([
  ['1', {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'ADMIN',
    status: 'ACTIVE',
    profileCompleted: true,
    profileProgress: 100,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }],
  ['2', {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'USER',
    status: 'ACTIVE',
    profileCompleted: false,
    profileProgress: 60,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }]
])

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication removed - allow access to admin endpoints
    const { id: userId } = await params
    const user = mockUsers.get(userId)

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json({ user })
  } catch (error) {
    console.error('API: Error fetching user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication removed - allow access to admin endpoints
    const { id: userId } = await params
    const body = await request.json()

    const existingUser = mockUsers.get(userId)
    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Update user data
    const updatedUser = {
      ...existingUser,
      ...body,
      id: userId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    }

    mockUsers.set(userId, updatedUser)

    return NextResponse.json({
      success: true,
      message: 'User updated successfully',
      user: updatedUser
    })
  } catch (error) {
    console.error('API: Error updating user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authentication removed - allow access to admin endpoints
    const { id: userId } = await params

    if (!mockUsers.has(userId)) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    mockUsers.delete(userId)

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    })
  } catch (error) {
    console.error('API: Error deleting user:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}