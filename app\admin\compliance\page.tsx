"use client";

import { useState } from "react";
import Link from "next/link";
import { 
  <PERSON>, 
  FileText, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>, 
  FileBar<PERSON>hart,
  Clock,
  Calendar,
  ArrowRight,
  RefreshCw
} from "lucide-react";
import GlassCard from "@/components/GlassCard";

// Mock data
const complianceStats = {
  regulationsMet: 28,
  regulationsTotal: 32,
  highRiskIssues: 2,
  mediumRiskIssues: 5,
  lowRiskIssues: 8,
  pendingAudits: 3,
  completedAudits: 7,
  upcomingDeadlines: 4
};

const recentActivities = [
  { 
    id: "act-1", 
    type: "audit",
    action: "Compliance audit completed",
    details: "Data privacy procedures review",
    date: "2023-10-12T09:30:00Z",
    status: "completed"
  },
  { 
    id: "act-2", 
    type: "issue",
    action: "High-risk issue identified",
    details: "Document retention policy gap",
    date: "2023-10-10T14:25:00Z",
    status: "pending"
  },
  { 
    id: "act-3", 
    type: "update",
    action: "Regulation update",
    details: "GDPR documentation requirements updated",
    date: "2023-10-08T11:15:00Z",
    status: "info"
  },
  { 
    id: "act-4", 
    type: "task",
    action: "Remediation task completed",
    details: "Updated privacy policy document",
    date: "2023-10-05T15:40:00Z",
    status: "completed"
  },
  { 
    id: "act-5", 
    type: "audit",
    action: "Internal audit scheduled",
    details: "Application processing procedures",
    date: "2023-10-02T10:00:00Z",
    status: "upcoming"
  }
];

export default function CompliancePage() {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshData = () => {
    setIsRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  // Calculate compliance rate
  const complianceRate = Math.round((complianceStats.regulationsMet / complianceStats.regulationsTotal) * 100);
  
  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-700 to-indigo-700">
            Compliance Management
          </h1>
          <p className="text-gray-600 mt-1">Monitor regulatory compliance and manage audits</p>
        </div>
        <button 
          onClick={refreshData}
          className="p-2 text-indigo-600 hover:text-indigo-800 rounded-full hover:bg-indigo-50/70 backdrop-blur-sm transition-colors"
        >
          <RefreshCw className={`h-5 w-5 ${isRefreshing ? "animate-spin" : ""}`} />
        </button>
      </div>
      
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <GlassCard className="p-5 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Compliance Rate</p>
              <h3 className="text-2xl font-bold mt-1 text-indigo-800">{complianceRate}%</h3>
            </div>
            <div className={`rounded-full p-2 ${
              complianceRate >= 90 ? "bg-green-100/70 backdrop-blur-sm text-green-600" : 
              complianceRate >= 70 ? "bg-amber-100/70 backdrop-blur-sm text-amber-600" : 
              "bg-red-100/70 backdrop-blur-sm text-red-600"
            }`}>
              <Shield className="h-5 w-5" />
            </div>
          </div>
          <div className="mt-3 h-2 bg-indigo-100/40 rounded-full overflow-hidden">
            <div 
              className={`h-full rounded-full ${
                complianceRate >= 90 ? "bg-gradient-to-r from-green-500 to-emerald-400" : 
                complianceRate >= 70 ? "bg-gradient-to-r from-amber-500 to-yellow-400" : 
                "bg-gradient-to-r from-red-500 to-rose-400"
              }`}
              style={{ width: `${complianceRate}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            {complianceStats.regulationsMet} of {complianceStats.regulationsTotal} requirements met
          </p>
        </GlassCard>
        
        <GlassCard className="p-5 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Issues</p>
              <h3 className="text-2xl font-bold mt-1 text-indigo-800">
                {complianceStats.highRiskIssues + complianceStats.mediumRiskIssues + complianceStats.lowRiskIssues}
              </h3>
            </div>
            <div className="rounded-full p-2 bg-amber-100/70 backdrop-blur-sm text-amber-600">
              <AlertTriangle className="h-5 w-5" />
            </div>
          </div>
          <div className="mt-3 flex gap-2">
            <div className="flex-1">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">High</span>
                <span className="font-medium text-red-600">{complianceStats.highRiskIssues}</span>
              </div>
              <div className="h-1.5 bg-indigo-100/40 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-red-500 to-rose-400 rounded-full" style={{ width: '100%' }}></div>
              </div>
            </div>
            <div className="flex-1">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">Medium</span>
                <span className="font-medium text-amber-600">{complianceStats.mediumRiskIssues}</span>
              </div>
              <div className="h-1.5 bg-indigo-100/40 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-amber-500 to-yellow-400 rounded-full" style={{ width: '100%' }}></div>
              </div>
            </div>
            <div className="flex-1">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">Low</span>
                <span className="font-medium text-blue-600">{complianceStats.lowRiskIssues}</span>
              </div>
              <div className="h-1.5 bg-indigo-100/40 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-blue-500 to-indigo-400 rounded-full" style={{ width: '100%' }}></div>
              </div>
            </div>
          </div>
        </GlassCard>
        
        <GlassCard className="p-5 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Audits</p>
              <h3 className="text-2xl font-bold mt-1 text-indigo-800">
                {complianceStats.pendingAudits + complianceStats.completedAudits}
              </h3>
            </div>
            <div className="rounded-full p-2 bg-indigo-100/70 backdrop-blur-sm text-indigo-600">
              <ClipboardCheck className="h-5 w-5" />
            </div>
          </div>
          <div className="mt-3 flex gap-2">
            <div className="flex-1">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">Pending</span>
                <span className="font-medium text-amber-600">{complianceStats.pendingAudits}</span>
              </div>
              <div className="h-1.5 bg-indigo-100/40 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-amber-500 to-yellow-400 rounded-full" style={{ width: '100%' }}></div>
              </div>
            </div>
            <div className="flex-1">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-600">Completed</span>
                <span className="font-medium text-green-600">{complianceStats.completedAudits}</span>
              </div>
              <div className="h-1.5 bg-indigo-100/40 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-green-500 to-emerald-400 rounded-full" style={{ width: '100%' }}></div>
              </div>
            </div>
          </div>
        </GlassCard>
        
        <GlassCard className="p-5 transform transition hover:translate-y-[-4px]">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-indigo-700 font-medium">Upcoming Deadlines</p>
              <h3 className="text-2xl font-bold mt-1 text-indigo-800">{complianceStats.upcomingDeadlines}</h3>
            </div>
            <div className="rounded-full p-2 bg-blue-100/70 backdrop-blur-sm text-blue-600">
              <Calendar className="h-5 w-5" />
            </div>
          </div>
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Next deadline in</span>
              <span className="font-medium text-blue-600">3 days</span>
            </div>
            <div className="mt-1 flex items-center">
              <Clock className="h-3.5 w-3.5 text-blue-600 mr-1" />
              <span className="text-xs text-gray-600">Documentation Review</span>
            </div>
          </div>
        </GlassCard>
      </div>
      
      {/* Compliance Modules Sections */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Link 
          href="/admin/compliance" 
          className="block transform transition hover:translate-y-[-4px] hover:shadow-md"
        >
          <GlassCard className="p-5 h-full">
            <div className="flex justify-between items-start">
              <div>
                <div className="rounded-full w-12 h-12 bg-indigo-100/70 backdrop-blur-sm flex items-center justify-center mb-3">
                  <Shield className="h-6 w-6 text-indigo-600" />
                </div>
                <h3 className="text-lg font-semibold text-indigo-800">Regulations & Requirements</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Track and manage regulatory compliance requirements
                </p>
              </div>
              <ArrowRight className="h-5 w-5 text-indigo-400" />
            </div>
            <div className="mt-4 text-sm text-indigo-600 font-medium">
              {complianceStats.regulationsMet} of {complianceStats.regulationsTotal} requirements met
            </div>
          </GlassCard>
        </Link>
        
        <Link 
          href="/admin/compliance" 
          className="block transform transition hover:translate-y-[-4px] hover:shadow-md"
        >
          <GlassCard className="p-5 h-full">
            <div className="flex justify-between items-start">
              <div>
                <div className="rounded-full w-12 h-12 bg-green-100/70 backdrop-blur-sm flex items-center justify-center mb-3">
                  <ClipboardCheck className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-indigo-800">Audit Management</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Schedule, conduct and track internal & external audits
                </p>
              </div>
              <ArrowRight className="h-5 w-5 text-indigo-400" />
            </div>
            <div className="mt-4 text-sm text-green-600 font-medium">
              {complianceStats.pendingAudits} pending, {complianceStats.completedAudits} completed audits
            </div>
          </GlassCard>
        </Link>
        
        <Link 
          href="/admin/compliance" 
          className="block transform transition hover:translate-y-[-4px] hover:shadow-md"
        >
          <GlassCard className="p-5 h-full">
            <div className="flex justify-between items-start">
              <div>
                <div className="rounded-full w-12 h-12 bg-blue-100/70 backdrop-blur-sm flex items-center justify-center mb-3">
                  <FileBarChart className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-indigo-800">Reports & Documentation</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Generate compliance reports and maintain documentation
                </p>
              </div>
              <ArrowRight className="h-5 w-5 text-indigo-400" />
            </div>
            <div className="mt-4 text-sm text-blue-600 font-medium">
              Generate custom reports and documentation
            </div>
          </GlassCard>
        </Link>
      </div>
      
      {/* Recent Activity */}
      <GlassCard className="p-5 mb-5 transform transition hover:translate-y-[-2px]">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-indigo-800">Recent Activity</h3>
          <Link href="/admin/compliance" className="text-sm text-indigo-600 hover:text-indigo-800">
            View All
          </Link>
        </div>
        
        <div className="space-y-4">
          {recentActivities.map((activity) => (
            <div key={activity.id} className="flex items-start pb-4 border-b border-indigo-100/20 last:border-0 last:pb-0">
              <div className={`rounded-full p-2 mr-3 ${
                activity.status === "completed" ? "bg-green-100/70 backdrop-blur-sm text-green-600" :
                activity.status === "pending" ? "bg-amber-100/70 backdrop-blur-sm text-amber-600" :
                activity.status === "upcoming" ? "bg-blue-100/70 backdrop-blur-sm text-blue-600" :
                "bg-indigo-100/70 backdrop-blur-sm text-indigo-600"
              }`}>
                {activity.type === "audit" ? <ClipboardCheck className="h-5 w-5" /> :
                 activity.type === "issue" ? <AlertTriangle className="h-5 w-5" /> :
                 activity.type === "update" ? <RefreshCw className="h-5 w-5" /> :
                 <CheckCircle className="h-5 w-5" />}
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <span className="font-medium text-indigo-800">{activity.action}</span>
                  <span className="text-xs text-gray-600">
                    {new Date(activity.date).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{activity.details}</p>
                {activity.status === "pending" && (
                  <div className="mt-2">
                    <button className="text-xs bg-indigo-100/70 backdrop-blur-sm text-indigo-700 px-3 py-1.5 rounded-lg hover:bg-indigo-200/70 transition-colors">
                      Take Action
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </GlassCard>
    </div>
  );
} 